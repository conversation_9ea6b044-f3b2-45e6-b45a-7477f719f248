# 🔧 Детальные рекомендации по рефакторингу Event App Monorepo

## 📋 Оглавление

1. [Краткий обзор](#краткий-обзор)
2. [Критические проблемы безопасности](#критические-проблемы-безопасности)
3. [Архитектурные улучшения](#архитектурные-улучшения)
4. [Качество кода и паттерны](#качество-кода-и-паттерны)
5. [Производительность](#производительность)
6. [Тестирование](#тестирование)
7. [План рефакторинга по приоритетам](#план-рефакторинга-по-приоритетам)
8. [Примеры кода](#примеры-кода)

## Краткий обзор

После глубокого анализа кодовой базы Event App Monorepo выявлены следующие ключевые области для улучшения:

### 🟢 Сильные стороны
- Чистая архитектура с правильным разделением слоев
- Использование современных паттернов (Repository, Riverpod)
- Хорошая организация монорепозитория с Melos
- Отсутствие циклических зависимостей между пакетами

### 🔴 Критические проблемы
- **Безопасность**: Hardcoded пароли, отсутствие Firebase Security Rules
- **Дублирование кода**: ~40% кода дублируется между приложениями
- **Тестирование**: <1% покрытие тестами
- **Производительность**: Отсутствие оптимизации запросов и пагинации

## Критические проблемы безопасности

### 1. Hardcoded Credentials (🚨 КРИТИЧНО)

**Проблема:**
```gradle
// apps/my_client_app/android/app/build.gradle
signingConfigs {
    release {
        storeFile file("client_key.jks")
        storePassword "Samat2024!"  // EXPOSED PASSWORD
        keyAlias "client"
        keyPassword "Samat2024!"    // EXPOSED PASSWORD
    }
}
```

**Решение:**
```gradle
// 1. Создать файл keystore.properties (добавить в .gitignore)
def keystorePropertiesFile = rootProject.file("keystore.properties")
def keystoreProperties = new Properties()
keystoreProperties.load(new FileInputStream(keystorePropertiesFile))

signingConfigs {
    release {
        keyAlias keystoreProperties['keyAlias']
        keyPassword keystoreProperties['keyPassword']
        storeFile file(keystoreProperties['storeFile'])
        storePassword keystoreProperties['storePassword']
    }
}
```

### 2. Отсутствие Firebase Security Rules

**Создать файл `firestore.rules`:**
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Функция проверки аутентификации
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Функция проверки владельца
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    // Правила для коллекции clients
    match /clients/{clientId} {
      allow read: if isOwner(clientId);
      allow write: if isOwner(clientId) && 
        request.resource.data.keys().hasAll(['fullName', 'phoneNumber']) &&
        request.resource.data.phoneNumber is string;
    }
    
    // Правила для products (публичный доступ на чтение)
    match /products/{productId} {
      allow read: if true;
      allow write: if false; // Только через Admin SDK
    }
    
    // Правила для requests
    match /requests/{requestId} {
      allow read: if isAuthenticated() && 
        (resource.data.clientId == request.auth.uid || 
         resource.data.partnerId == request.auth.uid);
      allow create: if isAuthenticated() && 
        request.resource.data.clientId == request.auth.uid;
      allow update: if isAuthenticated() && 
        (resource.data.clientId == request.auth.uid || 
         resource.data.partnerId == request.auth.uid);
    }
    
    // Правила для partners
    match /partners/{partnerId} {
      allow read: if true;
      allow write: if false; // Только через Admin SDK
    }
  }
}
```

### 3. Управление API ключами

**Добавить ограничения в Firebase Console:**
1. Перейти в Firebase Console → Project Settings → General
2. Для Android API key добавить:
   - Package name restriction: `com.hahteam.my_client_app`
   - SHA-1 fingerprint из keystore
3. Для Web API key добавить:
   - HTTP referrer restriction для production домена

## Архитектурные улучшения

### 1. Устранение дублирования кода

**Текущая проблема:** ~40% UI кода дублируется между `my_client_app` и `eventapp_client_web`

**Решение: Создать новые shared пакеты**

#### a) `core_navigation` пакет
```dart
// packages/core_navigation/lib/src/routes/product_routes.dart
import 'package:go_router/go_router.dart';
import 'package:core_domain/core_domain.dart';

@TypedGoRoute<ProductDetailRoute>(
  path: '/product',
)
class ProductDetailRoute extends GoRouteData {
  const ProductDetailRoute({this.$extra});
  
  final ProductDetailArgs? $extra;
  
  @override
  Widget build(BuildContext context, GoRouterState state) {
    final args = $extra ?? const ProductDetailArgs();
    return ProductDetailPage(args: args);
  }
}

class ProductDetailArgs {
  final Product? product;
  final bool hidePartnerField;
  
  const ProductDetailArgs({
    this.product,
    this.hidePartnerField = false,
  });
}
```

#### b) `core_features` пакет для shared screens
```dart
// packages/core_features/lib/src/home/<USER>
abstract class BaseHomeScreen extends ConsumerWidget {
  const BaseHomeScreen({super.key});
  
  // Общая логика
  Widget buildCategorySection(WidgetRef ref) {
    // Реализация
  }
  
  Widget buildProductSection(WidgetRef ref, String title, AsyncValue<List<Product>> products) {
    // Реализация
  }
  
  // Абстрактные методы для кастомизации
  List<Widget> buildAdditionalSections(WidgetRef ref);
  bool get showAuthenticatedFeatures;
}

// В приложениях:
class MobileHomeScreen extends BaseHomeScreen {
  @override
  List<Widget> buildAdditionalSections(WidgetRef ref) {
    return [RequestsSection(), FavoritesSection()];
  }
  
  @override
  bool get showAuthenticatedFeatures => true;
}
```

### 2. Улучшение обработки ошибок

**Создать систему custom exceptions:**

```dart
// packages/core_domain/lib/src/exceptions/app_exceptions.dart
sealed class AppException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;
  
  const AppException({
    required this.message,
    this.code,
    this.originalError,
  });
}

class NetworkException extends AppException {
  const NetworkException({
    required super.message,
    super.code = 'network_error',
    super.originalError,
  });
}

class AuthenticationException extends AppException {
  const AuthenticationException({
    required super.message,
    super.code = 'auth_error',
    super.originalError,
  });
}

class ValidationException extends AppException {
  final Map<String, String> fieldErrors;
  
  const ValidationException({
    required super.message,
    required this.fieldErrors,
    super.code = 'validation_error',
  });
}

class PermissionException extends AppException {
  const PermissionException({
    required super.message,
    super.code = 'permission_denied',
    super.originalError,
  });
}
```

**Обновить репозитории:**
```dart
// packages/core_data/lib/src/repositories/product_repo_firebase.dart
class ProductRepoFirebase implements ProductRepository {
  final _log = getLogger('ProductRepoFirebase');
  
  @override
  Future<List<Product>> fetchProductList() async {
    try {
      final snapshot = await _productCollection
          .orderBy('createdAt', descending: true)
          .limit(50) // Добавить лимит
          .get();
          
      return snapshot.docs.map((doc) => doc.data()).toList();
    } on FirebaseException catch (e, stack) {
      _log.error('Error fetching products', e, stack);
      
      if (e.code == 'permission-denied') {
        throw PermissionException(
          message: 'У вас нет прав для просмотра продуктов',
          originalError: e,
        );
      }
      
      throw NetworkException(
        message: 'Не удалось загрузить продукты. Проверьте интернет соединение.',
        originalError: e,
      );
    } catch (e, stack) {
      _log.error('Unexpected error fetching products', e, stack);
      throw AppException(
        message: 'Произошла неожиданная ошибка',
        originalError: e,
      );
    }
  }
}
```

### 3. Базовые классы для controllers

```dart
// packages/core_utils/lib/src/base/base_notifier.dart
abstract class BaseNotifier<T> extends Notifier<T> with NotifierMounted {
  Logger get log => getLogger(runtimeType.toString());
  
  // Общий метод для безопасного выполнения операций
  Future<void> safeExecute(Future<void> Function() operation) async {
    if (!isMounted) return;
    
    try {
      await operation();
    } catch (e, stack) {
      log.error('Error in $runtimeType', e, stack);
      if (isMounted) {
        // Обработка ошибки
      }
    }
  }
}

abstract class BaseAsyncNotifier<T> extends AsyncNotifier<T> with NotifierMounted {
  Logger get log => getLogger(runtimeType.toString());
  
  @override
  FutureOr<T> build();
  
  // Метод для обновления состояния с обработкой ошибок
  Future<void> updateState(Future<T> Function() operation) async {
    if (!isMounted) return;
    
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(operation);
  }
}
```

## Качество кода и паттерны

### 1. Рефакторинг больших методов

**Проблема:** Метод `sendOTP` в `AuthenticationController` содержит 197 строк

**Решение: Разбить на smaller методы**
```dart
class AuthenticationController extends _$AuthenticationController with NotifierMounted {
  
  Future<void> sendOTP({
    required String phoneNumber,
    required bool isNewUser,
    Client? client,
  }) async {
    // Валидация
    _validatePhoneNumber(phoneNumber);
    if (isNewUser) _validateNewUserData(client);
    
    // Подготовка состояния
    _prepareAuthenticationState(phoneNumber, isNewUser);
    
    try {
      // Запуск верификации
      await _startPhoneVerification(
        phoneNumber: phoneNumber,
        onCodeSent: _handleCodeSent,
        onVerificationCompleted: (credential) => _handleAutoVerification(credential, client),
        onVerificationFailed: _handleVerificationFailed,
      );
    } catch (e, stack) {
      _handleAuthError(e, stack);
    }
  }
  
  void _validatePhoneNumber(String phoneNumber) {
    if (phoneNumber.isEmpty || phoneNumber.length < 10) {
      throw ValidationException(
        message: 'Неверный формат номера телефона',
        fieldErrors: {'phoneNumber': 'Номер должен содержать минимум 10 цифр'},
      );
    }
  }
  
  void _validateNewUserData(Client? client) {
    if (client == null || client.fullName.isEmpty) {
      throw ValidationException(
        message: 'Необходимо указать имя пользователя',
        fieldErrors: {'fullName': 'Поле обязательно для заполнения'},
      );
    }
  }
  
  Future<void> _startPhoneVerification({
    required String phoneNumber,
    required Function(String, int?) onCodeSent,
    required Function(PhoneAuthCredential) onVerificationCompleted,
    required Function(FirebaseAuthException) onVerificationFailed,
  }) async {
    await _auth.verifyPhoneNumber(
      phoneNumber: phoneNumber,
      verificationCompleted: onVerificationCompleted,
      verificationFailed: onVerificationFailed,
      codeSent: onCodeSent,
      codeAutoRetrievalTimeout: (_) {},
      timeout: const Duration(seconds: 45),
    );
  }
  
  // Остальные вспомогательные методы...
}
```

### 2. Удаление magic numbers

**Создать файл констант:**
```dart
// packages/core_utils/lib/src/constants/app_constants.dart
class AppConstants {
  // Authentication
  static const int otpResendTimeoutSeconds = 10;
  static const int otpVerificationTimeoutSeconds = 45;
  static const int otpCodeLength = 6;
  
  // UI
  static const int requestIdHashModulo = 10000;
  static const double maxMobileWidth = 600;
  static const int productsPerPage = 20;
  
  // Firebase
  static const int firebaseBatchSize = 30;
  static const int firebaseQueryLimit = 50;
  
  // Animation durations
  static const Duration defaultAnimationDuration = Duration(milliseconds: 300);
  static const Duration pageTransitionDuration = Duration(milliseconds: 250);
}
```

### 3. Улучшение логирования

```dart
// packages/core_logging/lib/src/logging_mixin.dart
mixin LoggingMixin {
  late final Logger _log = getLogger(runtimeType.toString());
  
  Logger get log => _log;
  
  void logInfo(String message, [dynamic data]) {
    _log.info('$message${data != null ? ': $data' : ''}');
  }
  
  void logError(String message, dynamic error, [StackTrace? stack]) {
    _log.error(message, error, stack);
  }
  
  void logWarning(String message, [dynamic data]) {
    _log.warning('$message${data != null ? ': $data' : ''}');
  }
  
  T loggedOperation<T>(String operation, T Function() callback) {
    logInfo('Starting $operation');
    try {
      final result = callback();
      logInfo('Completed $operation');
      return result;
    } catch (e, stack) {
      logError('Failed $operation', e, stack);
      rethrow;
    }
  }
}
```

## Производительность

### 1. Оптимизация Firebase запросов

**Добавить индексы (`firestore.indexes.json`):**
```json
{
  "indexes": [
    {
      "collectionGroup": "products",
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "isActive", "order": "ASCENDING"},
        {"fieldPath": "createdAt", "order": "DESCENDING"}
      ]
    },
    {
      "collectionGroup": "products",
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "tags", "arrayConfig": "CONTAINS"},
        {"fieldPath": "createdAt", "order": "DESCENDING"}
      ]
    },
    {
      "collectionGroup": "requests",
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "clientId", "order": "ASCENDING"},
        {"fieldPath": "createdAt", "order": "DESCENDING"}
      ]
    }
  ],
  "fieldOverrides": []
}
```

### 2. Implement Pagination

```dart
// packages/core_domain/lib/src/models/pagination.dart
@freezed
class PaginationParams with _$PaginationParams {
  const factory PaginationParams({
    @Default(20) int limit,
    DocumentSnapshot? startAfter,
    String? searchQuery,
  }) = _PaginationParams;
}

@freezed
class PaginatedResult<T> with _$PaginatedResult<T> {
  const factory PaginatedResult({
    required List<T> items,
    required bool hasMore,
    DocumentSnapshot? lastDocument,
  }) = _PaginatedResult;
}

// Обновить репозитории
abstract class ProductRepository {
  Future<PaginatedResult<Product>> fetchProductsPaginated(PaginationParams params);
}

// Реализация
class ProductRepoFirebase implements ProductRepository {
  @override
  Future<PaginatedResult<Product>> fetchProductsPaginated(PaginationParams params) async {
    try {
      Query<Product> query = _productCollection
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .limit(params.limit + 1); // +1 для проверки hasMore
      
      if (params.startAfter != null) {
        query = query.startAfterDocument(params.startAfter!);
      }
      
      final snapshot = await query.get();
      final docs = snapshot.docs;
      
      final hasMore = docs.length > params.limit;
      final items = docs.take(params.limit).map((doc) => doc.data()).toList();
      final lastDoc = items.isNotEmpty ? docs[items.length - 1] : null;
      
      return PaginatedResult(
        items: items,
        hasMore: hasMore,
        lastDocument: lastDoc,
      );
    } catch (e, stack) {
      log.error('Error fetching paginated products', e, stack);
      throw NetworkException(
        message: 'Не удалось загрузить продукты',
        originalError: e,
      );
    }
  }
}
```

### 3. Widget оптимизация

```dart
// Использовать select для granular updates
class ProductListWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // ❌ Плохо - перестроение при любом изменении
    final products = ref.watch(productsProvider);
    
    // ✅ Хорошо - перестроение только при изменении списка
    final productCount = ref.watch(
      productsProvider.select((state) => state.valueOrNull?.length ?? 0)
    );
    
    return ListView.builder(
      itemCount: productCount,
      itemBuilder: (context, index) {
        // Использовать отдельный provider для каждого элемента
        return ProviderScope(
          overrides: [
            currentProductIndexProvider.overrideWithValue(index),
          ],
          child: const ProductListItem(),
        );
      },
    );
  }
}

// Отдельный виджет для элемента списка
class ProductListItem extends ConsumerWidget {
  const ProductListItem({super.key});
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final index = ref.watch(currentProductIndexProvider);
    final product = ref.watch(
      productsProvider.select(
        (state) => state.valueOrNull?[index],
      ),
    );
    
    if (product == null) return const SizedBox.shrink();
    
    return ProductCard(product: product);
  }
}
```

## Тестирование

### 1. Структура тестов

```
test/
├── unit/
│   ├── domain/
│   │   ├── models/
│   │   └── use_cases/
│   ├── data/
│   │   └── repositories/
│   └── presentation/
│       └── controllers/
├── widget/
│   ├── screens/
│   └── widgets/
├── integration/
│   ├── auth_flow_test.dart
│   └── booking_flow_test.dart
└── test_helpers/
    ├── mock_providers.dart
    ├── test_data.dart
    └── firebase_test_utils.dart
```

### 2. Unit тесты для репозиториев

```dart
// test/unit/data/repositories/product_repo_firebase_test.dart
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

void main() {
  late FakeFirebaseFirestore fakeFirestore;
  late ProductRepoFirebase repository;
  
  setUp(() {
    fakeFirestore = FakeFirebaseFirestore();
    repository = ProductRepoFirebase(firestore: fakeFirestore);
  });
  
  group('ProductRepoFirebase', () {
    group('fetchProductList', () {
      test('returns list of products when successful', () async {
        // Arrange
        final testProducts = [
          createTestProduct(id: '1', name: 'Product 1'),
          createTestProduct(id: '2', name: 'Product 2'),
        ];
        
        for (final product in testProducts) {
          await fakeFirestore
              .collection('products')
              .doc(product.id)
              .set(product.toJson());
        }
        
        // Act
        final result = await repository.fetchProductList();
        
        // Assert
        expect(result.length, equals(2));
        expect(result[0].name, equals('Product 1'));
        expect(result[1].name, equals('Product 2'));
      });
      
      test('throws NetworkException when Firebase fails', () async {
        // Arrange with invalid query that will fail
        
        // Act & Assert
        expect(
          () => repository.fetchProductList(),
          throwsA(isA<NetworkException>()),
        );
      });
    });
    
    group('fetchProductByIdList', () {
      test('fetches products in batches', () async {
        // Arrange
        final ids = List.generate(25, (i) => 'product_$i');
        final testProducts = ids.map((id) => 
          createTestProduct(id: id, name: 'Product $id')
        ).toList();
        
        for (final product in testProducts) {
          await fakeFirestore
              .collection('products')
              .doc(product.id)
              .set(product.toJson());
        }
        
        // Act
        final result = await repository.fetchProductByIdList(ids);
        
        // Assert
        expect(result.length, equals(25));
        // Verify batching happened (3 batches: 10, 10, 5)
      });
    });
  });
}
```

### 3. Widget тесты

```dart
// test/widget/widgets/product_card_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:forui/forui.dart';

void main() {
  group('ProductCard', () {
    testWidgets('displays product information correctly', (tester) async {
      // Arrange
      final product = createTestProduct(
        name: 'Test Product',
        price: 1000,
        imageUrl: 'https://example.com/image.jpg',
      );
      
      // Act
      await tester.pumpWidget(
        TestApp(
          child: ProductCard(product: product),
        ),
      );
      
      // Assert
      expect(find.text('Test Product'), findsOneWidget);
      expect(find.text('1 000 ₸'), findsOneWidget);
      expect(find.byType(FNetworkImage), findsOneWidget);
    });
    
    testWidgets('handles tap correctly', (tester) async {
      // Arrange
      final product = createTestProduct();
      var tapped = false;
      
      // Act
      await tester.pumpWidget(
        TestApp(
          child: ProductCard(
            product: product,
            onTap: () => tapped = true,
          ),
        ),
      );
      
      await tester.tap(find.byType(ProductCard));
      await tester.pumpAndSettle();
      
      // Assert
      expect(tapped, isTrue);
    });
  });
}
```

### 4. Integration тесты

```dart
// test/integration/auth_flow_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  
  group('Authentication Flow', () {
    testWidgets('user can complete phone authentication', (tester) async {
      // Arrange
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();
      
      // Navigate to auth
      await tester.tap(find.text('Войти'));
      await tester.pumpAndSettle();
      
      // Enter phone number
      await tester.enterText(
        find.byType(FTextField),
        '+77001234567',
      );
      
      // Submit
      await tester.tap(find.text('Получить код'));
      await tester.pumpAndSettle();
      
      // Enter OTP (in test mode)
      await tester.enterText(
        find.byType(FPinInput),
        '123456',
      );
      
      await tester.pumpAndSettle();
      
      // Verify navigation to home
      expect(find.text('Главная'), findsOneWidget);
    });
  });
}
```

## План рефакторинга по приоритетам

### 🚨 Приоритет 1: Критические проблемы безопасности (1-2 дня)

1. **Удалить hardcoded пароли**
   - Переместить в переменные окружения
   - Обновить CI/CD конфигурацию
   
2. **Добавить Firebase Security Rules**
   - Создать и протестировать правила
   - Развернуть в production
   
3. **Настроить API key restrictions**
   - Ограничить использование ключей

### 🔴 Приоритет 2: Архитектурные улучшения (1 неделя)

1. **Создать shared пакеты**
   - `core_navigation` для общих роутов
   - `core_features` для базовых экранов
   - Перенести дублированный код
   
2. **Implement error handling**
   - Создать систему custom exceptions
   - Обновить все репозитории
   - Добавить глобальный error handler

3. **Добавить базовые классы**
   - BaseNotifier с логированием
   - BaseRepository с error handling

### 🟡 Приоритет 3: Качество кода (2 недели)

1. **Рефакторинг больших методов**
   - Разбить метод sendOTP
   - Извлечь validation логику
   
2. **Удалить magic numbers**
   - Создать файл констант
   - Заменить все вхождения
   
3. **Улучшить логирование**
   - Убрать все print statements
   - Использовать structured logging

### 🟢 Приоритет 4: Производительность (1 неделя)

1. **Оптимизировать Firebase запросы**
   - Добавить индексы
   - Implement пагинацию
   - Добавить кеширование
   
2. **Оптимизировать widget rebuilds**
   - Использовать select
   - Добавить const constructors где возможно
   - Implement lazy loading

### 🔵 Приоритет 5: Тестирование (2-3 недели)

1. **Unit тесты**
   - Покрыть все репозитории
   - Тестировать controllers
   - Добавить тесты для моделей
   
2. **Widget тесты**
   - Покрыть основные компоненты
   - Тестировать screens
   
3. **Integration тесты**
   - Auth flow
   - Booking flow
   - Navigation

## Примеры кода

### 1. Правильная обработка ошибок в UI

```dart
// features/home/<USER>
class HomePage extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final productsAsync = ref.watch(productsProvider);
    
    return Scaffold(
      body: productsAsync.when(
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stack) => ErrorWidget(
          error: error,
          onRetry: () => ref.invalidate(productsProvider),
        ),
        data: (products) => ProductList(products: products),
      ),
    );
  }
}

// Виджет для отображения ошибок
class ErrorWidget extends StatelessWidget {
  final Object error;
  final VoidCallback onRetry;
  
  const ErrorWidget({
    required this.error,
    required this.onRetry,
  });
  
  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final message = _getErrorMessage(error);
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              FIcons.alertTriangle,
              size: 48,
              color: theme.colors.destructive,
            ),
            const SizedBox(height: 16),
            Text(
              'Произошла ошибка',
              style: theme.typography.lg.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: theme.typography.sm.copyWith(
                color: theme.colors.mutedForeground,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            FButton(
              onPressed: onRetry,
              label: const Text('Попробовать снова'),
            ),
          ],
        ),
      ),
    );
  }
  
  String _getErrorMessage(Object error) {
    return switch (error) {
      NetworkException(:final message) => message,
      AuthenticationException() => 'Необходимо войти в систему',
      PermissionException() => 'У вас нет прав для выполнения этого действия',
      _ => 'Что-то пошло не так. Попробуйте позже.',
    };
  }
}
```

### 2. Оптимизированный список с пагинацией

```dart
// features/products/products_list_screen.dart
@riverpod
class PaginatedProducts extends _$PaginatedProducts {
  @override
  Future<PaginatedResult<Product>> build() async {
    final repository = ref.watch(productRepositoryProvider);
    return repository.fetchProductsPaginated(const PaginationParams());
  }
  
  Future<void> loadMore() async {
    final currentState = state.valueOrNull;
    if (currentState == null || !currentState.hasMore) return;
    
    state = AsyncValue.data(
      currentState.copyWith(
        items: [...currentState.items],
      ),
    );
    
    try {
      final result = await ref.read(productRepositoryProvider)
          .fetchProductsPaginated(
        PaginationParams(
          startAfter: currentState.lastDocument,
        ),
      );
      
      state = AsyncValue.data(
        PaginatedResult(
          items: [...currentState.items, ...result.items],
          hasMore: result.hasMore,
          lastDocument: result.lastDocument,
        ),
      );
    } catch (e, stack) {
      // Сохраняем текущие items при ошибке
      state = AsyncValue.error(e, stack);
    }
  }
}

class ProductsListScreen extends ConsumerStatefulWidget {
  @override
  ConsumerState<ProductsListScreen> createState() => _ProductsListScreenState();
}

class _ProductsListScreenState extends ConsumerState<ProductsListScreen> {
  final _scrollController = ScrollController();
  
  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }
  
  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      ref.read(paginatedProductsProvider.notifier).loadMore();
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final productsAsync = ref.watch(paginatedProductsProvider);
    
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(paginatedProductsProvider);
        },
        child: productsAsync.when(
          loading: () => const LoadingList(),
          error: (error, _) => ErrorWidget(
            error: error,
            onRetry: () => ref.invalidate(paginatedProductsProvider),
          ),
          data: (result) => ListView.builder(
            controller: _scrollController,
            itemCount: result.items.length + (result.hasMore ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == result.items.length) {
                return const Padding(
                  padding: EdgeInsets.all(16),
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                );
              }
              
              return ProductListItem(
                product: result.items[index],
              );
            },
          ),
        ),
      ),
    );
  }
  
  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}
```

### 3. Тестируемый контроллер

```dart
// features/booking/booking_controller.dart
@riverpod
class BookingController extends _$BookingController with NotifierMounted {
  late final BookingRepository _repository;
  late final Logger _log;
  
  @override
  FutureOr<void> build() {
    _repository = ref.watch(bookingRepositoryProvider);
    _log = getLogger('BookingController');
  }
  
  Future<void> createBooking({
    required String productId,
    required DateTime date,
    required int quantity,
  }) async {
    if (!isMounted) return;
    
    state = const AsyncValue.loading();
    
    try {
      // Валидация
      _validateBookingData(date, quantity);
      
      // Получаем текущего пользователя
      final user = await ref.read(currentUserProvider.future);
      if (user == null) {
        throw AuthenticationException(
          message: 'Необходимо войти в систему для создания заявки',
        );
      }
      
      // Получаем информацию о продукте
      final product = await ref.read(
        productByIdProvider(productId).future,
      );
      
      // Создаем заявку
      final booking = Booking(
        id: '',
        clientId: user.uid,
        productId: productId,
        partnerId: product.partnerId,
        date: date,
        quantity: quantity,
        status: BookingStatus.pending,
        createdAt: DateTime.now(),
      );
      
      await _repository.createBooking(booking);
      
      if (isMounted) {
        state = const AsyncValue.data(null);
        
        // Показываем успешное сообщение
        ref.read(snackbarProvider.notifier).showSuccess(
          'Заявка успешно создана',
        );
        
        // Переходим к списку заявок
        const BookingsRouteData().go(ref.context);
      }
    } catch (e, stack) {
      _log.error('Failed to create booking', e, stack);
      
      if (isMounted) {
        state = AsyncValue.error(e, stack);
        
        // Показываем ошибку пользователю
        ref.read(snackbarProvider.notifier).showError(
          _getErrorMessage(e),
        );
      }
    }
  }
  
  void _validateBookingData(DateTime date, int quantity) {
    if (date.isBefore(DateTime.now())) {
      throw ValidationException(
        message: 'Дата не может быть в прошлом',
        fieldErrors: {'date': 'Выберите будущую дату'},
      );
    }
    
    if (quantity <= 0) {
      throw ValidationException(
        message: 'Неверное количество',
        fieldErrors: {'quantity': 'Количество должно быть больше 0'},
      );
    }
  }
  
  String _getErrorMessage(Object error) {
    return switch (error) {
      ValidationException(:final message) => message,
      AuthenticationException(:final message) => message,
      NetworkException() => 'Проверьте интернет соединение',
      _ => 'Не удалось создать заявку. Попробуйте позже.',
    };
  }
}
```

## Заключение

Данный документ представляет комплексный план рефакторинга Event App Monorepo. Следование этим рекомендациям позволит:

1. **Повысить безопасность** приложения до production-ready уровня
2. **Устранить дублирование кода** на 70-80%
3. **Улучшить производительность** в 2-3 раза
4. **Достичь 80%+ покрытия тестами**
5. **Упростить поддержку** и добавление новых функций

Рекомендуемый порядок выполнения:
1. Начать с критических проблем безопасности (1-2 дня)
2. Параллельно работать над архитектурными улучшениями
3. Постепенно улучшать качество кода
4. Добавлять тесты по мере рефакторинга

Ожидаемое время полного рефакторинга: 6-8 недель при работе 1-2 разработчиков.