# 📝 Примеры рефакторинга кода: До и После

Этот документ содержит конкретные примеры проблемного кода из проекта и их улучшенные версии после рефакторинга.

## 1. Обработка ошибок в репозиториях

### ❌ Текущий код (плохо)

```dart
// packages/core_data/lib/src/repositories/product_repo_firebase.dart
Future<List<Product>> fetchProductList() async {
  try {
    final snapshot = await _productCollection
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .get();

    final products = snapshot.docs.map((doc) => doc.data()).toList();
    return products;
  } catch (e) {
    print('Error fetching services: $e'); // Использует print!
    rethrow; // Пробрасывает raw exception
  }
}
```

### ✅ После рефакторинга (хорошо)

```dart
// packages/core_data/lib/src/repositories/product_repo_firebase.dart
class ProductRepoFirebase implements ProductRepository with LoggingMixin {
  static const _collectionName = 'products';
  static const _defaultLimit = 50;
  
  late final CollectionReference<Product> _productCollection;
  
  ProductRepoFirebase({FirebaseFirestore? firestore}) {
    _productCollection = (firestore ?? FirebaseFirestore.instance)
        .collection(_collectionName)
        .withConverter<Product>(
          fromFirestore: (snapshot, _) => Product.fromJson(snapshot.data()!),
          toFirestore: (product, _) => product.toJson(),
        );
  }
  
  @override
  Future<List<Product>> fetchProductList() async {
    return loggedOperation('fetchProductList', () async {
      try {
        final snapshot = await _productCollection
            .where('isActive', isEqualTo: true)
            .orderBy('createdAt', descending: true)
            .limit(_defaultLimit)
            .get();

        logInfo('Fetched ${snapshot.docs.length} products');
        return snapshot.docs.map((doc) => doc.data()).toList();
        
      } on FirebaseException catch (e, stack) {
        logError('Firebase error fetching products', e, stack);
        
        if (e.code == 'permission-denied') {
          throw PermissionException(
            message: 'У вас нет прав для просмотра продуктов',
            originalError: e,
          );
        }
        
        if (e.code == 'unavailable') {
          throw NetworkException(
            message: 'Сервис временно недоступен. Попробуйте позже.',
            originalError: e,
          );
        }
        
        throw RepositoryException(
          message: 'Не удалось загрузить список продуктов',
          code: 'fetch_products_failed',
          originalError: e,
        );
      } catch (e, stack) {
        logError('Unexpected error fetching products', e, stack);
        throw RepositoryException(
          message: 'Произошла неожиданная ошибка при загрузке продуктов',
          originalError: e,
        );
      }
    });
  }
}
```

## 2. Большие методы в контроллерах

### ❌ Текущий код (плохо)

```dart
// apps/my_client_app/lib/features/authentication/controller/authentication_controller.dart
Future<void> sendOTP({
  required String phoneNumber,
  required bool isNewUser,
  Client? client,
}) async {
  // 197 строк кода в одном методе!
  final completer = Completer<void>();
  Timer? timeoutTimer;
  
  try {
    if (!isMounted) return;
    
    state = state.copyWith(
      status: AuthenticationStatus.sendingOTP,
      error: null,
      verificationId: null,
      phoneNumber: phoneNumber,
      isNewUser: isNewUser,
      resendToken: null,
      client: client,
    );

    // Множество вложенных callback'ов
    await _auth.verifyPhoneNumber(
      phoneNumber: phoneNumber,
      verificationCompleted: (PhoneAuthCredential credential) async {
        // 50+ строк кода
      },
      verificationFailed: (FirebaseAuthException e) {
        // 20+ строк кода
      },
      codeSent: (String verificationId, int? resendToken) {
        // 30+ строк кода
      },
      codeAutoRetrievalTimeout: (String verificationId) {
        // Еще код
      },
      timeout: verificationTimeout,
    );
    
    // И так далее...
  } catch (e) {
    // Обработка ошибок
  }
}
```

### ✅ После рефакторинга (хорошо)

```dart
// apps/my_client_app/lib/features/authentication/controller/authentication_controller.dart
@riverpod
class AuthenticationController extends _$AuthenticationController 
    with NotifierMounted, LoggingMixin {
  
  static const _otpResendTimeout = Duration(seconds: 10);
  static const _verificationTimeout = Duration(seconds: 45);
  
  late final FirebaseAuth _auth;
  late final ClientRepository _clientRepo;
  
  Timer? _resendTimer;
  
  @override
  AuthenticationState build() {
    _auth = ref.watch(firebaseAuthProvider);
    _clientRepo = ref.watch(clientRepositoryProvider);
    
    ref.onDispose(() {
      _resendTimer?.cancel();
    });
    
    return const AuthenticationState();
  }
  
  Future<void> sendOTP({
    required String phoneNumber,
    required bool isNewUser,
    Client? client,
  }) async {
    if (!isMounted) return;
    
    try {
      // Валидация входных данных
      _validateOtpRequest(phoneNumber, isNewUser, client);
      
      // Обновление состояния
      _updateStateForOtpSending(phoneNumber, isNewUser, client);
      
      // Запуск верификации
      await _startPhoneVerification(phoneNumber);
      
    } catch (e, stack) {
      _handleOtpError(e, stack);
    }
  }
  
  void _validateOtpRequest(String phoneNumber, bool isNewUser, Client? client) {
    if (!_isValidPhoneNumber(phoneNumber)) {
      throw ValidationException(
        message: 'Неверный формат номера телефона',
        fieldErrors: {'phoneNumber': 'Номер должен быть в формате +7XXXXXXXXXX'},
      );
    }
    
    if (isNewUser && (client == null || client.fullName.trim().isEmpty)) {
      throw ValidationException(
        message: 'Для регистрации необходимо указать имя',
        fieldErrors: {'fullName': 'Имя не может быть пустым'},
      );
    }
  }
  
  bool _isValidPhoneNumber(String phoneNumber) {
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
    return RegExp(r'^\+7\d{10}$').hasMatch(cleanNumber);
  }
  
  void _updateStateForOtpSending(String phoneNumber, bool isNewUser, Client? client) {
    if (!isMounted) return;
    
    state = state.copyWith(
      status: AuthenticationStatus.sendingOTP,
      error: null,
      verificationId: null,
      phoneNumber: phoneNumber,
      isNewUser: isNewUser,
      resendToken: null,
      client: client,
    );
  }
  
  Future<void> _startPhoneVerification(String phoneNumber) async {
    logInfo('Starting phone verification for: ${_maskPhoneNumber(phoneNumber)}');
    
    await _auth.verifyPhoneNumber(
      phoneNumber: phoneNumber,
      verificationCompleted: _onVerificationCompleted,
      verificationFailed: _onVerificationFailed,
      codeSent: _onCodeSent,
      codeAutoRetrievalTimeout: _onCodeAutoRetrievalTimeout,
      timeout: _verificationTimeout,
    );
  }
  
  Future<void> _onVerificationCompleted(PhoneAuthCredential credential) async {
    logInfo('Auto-verification completed');
    
    if (!isMounted) return;
    
    state = state.copyWith(
      status: AuthenticationStatus.verifyingOTP,
    );
    
    try {
      final userCredential = await _auth.signInWithCredential(credential);
      await _handleSuccessfulAuth(userCredential);
    } catch (e, stack) {
      _handleAuthError(e, stack);
    }
  }
  
  void _onVerificationFailed(FirebaseAuthException e) {
    logError('Verification failed', e);
    
    if (!isMounted) return;
    
    final errorMessage = _getVerificationErrorMessage(e);
    
    state = state.copyWith(
      status: AuthenticationStatus.error,
      error: errorMessage,
    );
  }
  
  void _onCodeSent(String verificationId, int? resendToken) {
    logInfo('OTP code sent successfully');
    
    if (!isMounted) return;
    
    state = state.copyWith(
      status: AuthenticationStatus.otpSent,
      verificationId: verificationId,
      resendToken: resendToken,
      canResendOtp: false,
    );
    
    _startResendTimer();
  }
  
  void _onCodeAutoRetrievalTimeout(String verificationId) {
    logInfo('Auto retrieval timeout');
    
    if (!isMounted) return;
    
    if (state.verificationId == null) {
      state = state.copyWith(verificationId: verificationId);
    }
  }
  
  void _startResendTimer() {
    _resendTimer?.cancel();
    
    var secondsRemaining = _otpResendTimeout.inSeconds;
    
    _resendTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!isMounted) {
        timer.cancel();
        return;
      }
      
      secondsRemaining--;
      
      state = state.copyWith(
        resendSecondsRemaining: secondsRemaining,
        canResendOtp: secondsRemaining <= 0,
      );
      
      if (secondsRemaining <= 0) {
        timer.cancel();
      }
    });
  }
  
  String _maskPhoneNumber(String phoneNumber) {
    if (phoneNumber.length < 4) return phoneNumber;
    return phoneNumber.substring(0, phoneNumber.length - 4) + '****';
  }
  
  String _getVerificationErrorMessage(FirebaseAuthException e) {
    return switch (e.code) {
      'invalid-phone-number' => 'Неверный формат номера телефона',
      'too-many-requests' => 'Слишком много попыток. Попробуйте позже.',
      'quota-exceeded' => 'Превышен лимит SMS. Попробуйте позже.',
      'network-request-failed' => 'Ошибка сети. Проверьте интернет соединение.',
      _ => 'Не удалось отправить код. Попробуйте позже.',
    };
  }
  
  Future<void> _handleAuthError(Object error, StackTrace stack) async {
    logError('Authentication error', error, stack);
    
    if (!isMounted) return;
    
    final errorMessage = switch (error) {
      FirebaseAuthException e => _getAuthErrorMessage(e),
      ValidationException(:final message) => message,
      _ => 'Произошла ошибка при входе',
    };
    
    state = state.copyWith(
      status: AuthenticationStatus.error,
      error: errorMessage,
    );
  }
  
  @override
  void dispose() {
    _resendTimer?.cancel();
    super.dispose();
  }
}
```

## 3. Magic Numbers и константы

### ❌ Текущий код (плохо)

```dart
// apps/my_client_app/lib/features/requests/widgets/booking_details_section.dart
Text(
  '#${request.id.hashCode.abs() % 10000}', // Magic number!
  style: theme.typography.sm.copyWith(
    color: theme.colors.mutedForeground,
  ),
),

// apps/my_client_app/lib/features/authentication/controller/authentication_controller.dart
static const int RESEND_TIMEOUT = 10; // Не в общих константах
const verificationTimeout = Duration(seconds: 45); // Hardcoded

// apps/eventapp_client_web/lib/main.dart
constraints: const BoxConstraints(maxWidth: 600), // Magic number
```

### ✅ После рефакторинга (хорошо)

```dart
// packages/core_utils/lib/src/constants/app_constants.dart
class AppConstants {
  AppConstants._();
  
  // Authentication
  static const Duration otpResendTimeout = Duration(seconds: 10);
  static const Duration otpVerificationTimeout = Duration(seconds: 45);
  static const int otpCodeLength = 6;
  static const String phoneNumberPattern = r'^\+7\d{10}$';
  
  // UI Layout
  static const double maxMobileWidth = 600;
  static const double defaultPadding = 16;
  static const double largePadding = 24;
  static const double smallPadding = 8;
  
  // Business Logic
  static const int requestIdHashModulo = 10000;
  static const int maxProductsPerPage = 20;
  static const int productsGridCrossAxisCount = 2;
  
  // Firebase
  static const int firebaseBatchSize = 30;
  static const int firebaseWhereInLimit = 10;
  static const int defaultQueryLimit = 50;
  
  // Animations
  static const Duration fastAnimation = Duration(milliseconds: 200);
  static const Duration normalAnimation = Duration(milliseconds: 300);
  static const Duration slowAnimation = Duration(milliseconds: 500);
}

// Использование:
Text(
  '#${request.id.hashCode.abs() % AppConstants.requestIdHashModulo}',
  style: theme.typography.sm.copyWith(
    color: theme.colors.mutedForeground,
  ),
),

// В main.dart
constraints: const BoxConstraints(maxWidth: AppConstants.maxMobileWidth),

// В authentication_controller.dart
static const _otpResendTimeout = AppConstants.otpResendTimeout;
static const _verificationTimeout = AppConstants.otpVerificationTimeout;
```

## 4. Оптимизация Firebase запросов

### ❌ Текущий код (плохо)

```dart
// packages/core_data/lib/src/repositories/product_repo_firebase.dart
Future<List<Product>> fetchProductByIdList(List<String> idList) async {
  final List<Product> products = [];
  
  // Неэффективно - отдельный запрос для каждого ID!
  for (String id in idList) {
    try {
      final doc = await _productCollection.doc(id).get();
      if (doc.exists) {
        products.add(doc.data()!);
      }
    } catch (e) {
      print('Error fetching service with id $id: $e');
    }
  }
  
  return products;
}

// Нет лимитов в запросах
Future<List<Product>> fetchProductsByPartner(String partnerId) async {
  try {
    final snapshot = await _productCollection
        .where('partnerId', isEqualTo: partnerId)
        .get(); // Может вернуть тысячи документов!
    
    return snapshot.docs.map((doc) => doc.data()).toList();
  } catch (e) {
    print('Error fetching products by partner: $e');
    rethrow;
  }
}
```

### ✅ После рефакторинга (хорошо)

```dart
// packages/core_data/lib/src/repositories/product_repo_firebase.dart
class ProductRepoFirebase implements ProductRepository with LoggingMixin {
  static const _batchSize = AppConstants.firebaseWhereInLimit;
  
  @override
  Future<List<Product>> fetchProductByIdList(List<String> idList) async {
    if (idList.isEmpty) return [];
    
    return loggedOperation('fetchProductByIdList', () async {
      try {
        final products = <Product>[];
        
        // Разбиваем на батчи по 10 (лимит Firestore для whereIn)
        for (var i = 0; i < idList.length; i += _batchSize) {
          final batch = idList.skip(i).take(_batchSize).toList();
          
          final snapshot = await _productCollection
              .where(FieldPath.documentId, whereIn: batch)
              .get();
          
          products.addAll(
            snapshot.docs.map((doc) => doc.data()),
          );
          
          logInfo('Fetched batch ${i ~/ _batchSize + 1} with ${snapshot.docs.length} products');
        }
        
        return products;
      } on FirebaseException catch (e, stack) {
        logError('Failed to fetch products by ID list', e, stack);
        throw RepositoryException(
          message: 'Не удалось загрузить выбранные продукты',
          code: 'fetch_by_ids_failed',
          originalError: e,
        );
      }
    });
  }
  
  @override
  Future<PaginatedResult<Product>> fetchProductsByPartner(
    String partnerId, {
    PaginationParams? pagination,
  }) async {
    final params = pagination ?? const PaginationParams();
    
    return loggedOperation('fetchProductsByPartner', () async {
      try {
        Query<Product> query = _productCollection
            .where('partnerId', isEqualTo: partnerId)
            .where('isActive', isEqualTo: true)
            .orderBy('createdAt', descending: true)
            .limit(params.limit + 1); // +1 для проверки hasMore
        
        if (params.startAfter != null) {
          query = query.startAfterDocument(params.startAfter!);
        }
        
        final snapshot = await query.get();
        final docs = snapshot.docs;
        
        final hasMore = docs.length > params.limit;
        final items = docs.take(params.limit).map((doc) => doc.data()).toList();
        final lastDoc = items.isNotEmpty ? docs[items.length - 1] : null;
        
        logInfo('Fetched ${items.length} products for partner $partnerId');
        
        return PaginatedResult(
          items: items,
          hasMore: hasMore,
          lastDocument: lastDoc,
        );
      } on FirebaseException catch (e, stack) {
        logError('Failed to fetch products by partner', e, stack);
        throw RepositoryException(
          message: 'Не удалось загрузить продукты партнера',
          code: 'fetch_by_partner_failed',
          originalError: e,
        );
      }
    });
  }
  
  @override
  Stream<List<Product>> watchProductsByTag(String tag, {int? limit}) {
    return _productCollection
        .where('tags', arrayContains: tag)
        .where('isActive', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .limit(limit ?? AppConstants.defaultQueryLimit)
        .snapshots()
        .map((snapshot) => snapshot.docs.map((doc) => doc.data()).toList())
        .handleError((error, stack) {
          logError('Error in products by tag stream', error, stack);
          throw RepositoryException(
            message: 'Ошибка при загрузке продуктов',
            originalError: error,
          );
        });
  }
}
```

## 5. Улучшение State Management

### ❌ Текущий код (плохо)

```dart
// apps/my_client_app/lib/features/home/<USER>/home_controller.dart
@Riverpod(keepAlive: false)
class HomeController extends Notifier<void> {
  @override
  void build() {
    // No initial state needed
  }

  void onProductTap(BuildContext context, Product product) {
    ProductDetailRouteData(
      $extra: ProductDetailArgs(
        product: product,
        hidePartnerField: false,
      ),
    ).push(context);
  }
  
  // Нет обработки ошибок, нет логирования
}

// Неоптимальное использование providers
final newProductsValue = ref.watch(newProductsStreamProvider);
final popularProductsValue = ref.watch(productsByTagStreamProvider('popular'));
final allproductsValue = ref.watch(productListStreamProvider);
// Все три вызова вызывают rebuild всего виджета!
```

### ✅ После рефакторинга (хорошо)

```dart
// apps/my_client_app/lib/features/home/<USER>/home_controller.dart
@riverpod
class HomeController extends _$HomeController with NotifierMounted, LoggingMixin {
  @override
  HomeState build() {
    // Подписываемся на необходимые providers с оптимизацией
    ref.listen(connectivityProvider, (_, connectivity) {
      if (connectivity == ConnectivityResult.none) {
        _showOfflineMessage();
      }
    });
    
    return const HomeState();
  }
  
  Future<void> onProductTap(Product product) async {
    if (!isMounted) return;
    
    logInfo('Product tapped: ${product.id}');
    
    try {
      // Трекинг аналитики
      await ref.read(analyticsProvider).logProductView(product);
      
      // Навигация
      if (!isMounted) return;
      
      await ProductDetailRouteData(
        $extra: ProductDetailArgs(
          product: product,
          hidePartnerField: false,
        ),
      ).push(ref.context);
      
    } catch (e, stack) {
      logError('Failed to navigate to product detail', e, stack);
      
      if (isMounted) {
        ref.read(snackbarProvider.notifier).showError(
          'Не удалось открыть детали продукта',
        );
      }
    }
  }
  
  Future<void> refreshData() async {
    if (!isMounted) return;
    
    logInfo('Refreshing home data');
    
    state = state.copyWith(isRefreshing: true);
    
    try {
      // Инвалидируем все необходимые providers
      await Future.wait([
        ref.refresh(newProductsStreamProvider.future),
        ref.refresh(productsByTagStreamProvider('popular').future),
        ref.refresh(categoriesStreamProvider.future),
      ]);
      
      if (isMounted) {
        state = state.copyWith(isRefreshing: false);
      }
    } catch (e, stack) {
      logError('Failed to refresh home data', e, stack);
      
      if (isMounted) {
        state = state.copyWith(isRefreshing: false);
        ref.read(snackbarProvider.notifier).showError(
          'Не удалось обновить данные',
        );
      }
    }
  }
  
  void _showOfflineMessage() {
    if (!isMounted) return;
    
    ref.read(snackbarProvider.notifier).showWarning(
      'Вы офлайн. Показаны кешированные данные.',
    );
  }
}

// Оптимизированное использование providers с select
class HomePage extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Используем select для granular updates
    final hasNewProducts = ref.watch(
      newProductsStreamProvider.select(
        (value) => value.valueOrNull?.isNotEmpty ?? false,
      ),
    );
    
    final popularProductsCount = ref.watch(
      productsByTagStreamProvider('popular').select(
        (value) => value.valueOrNull?.length ?? 0,
      ),
    );
    
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () => ref.read(homeControllerProvider.notifier).refreshData(),
        child: CustomScrollView(
          slivers: [
            if (hasNewProducts) const NewProductsSection(),
            if (popularProductsCount > 0) const PopularProductsSection(),
            const AllProductsSection(),
          ],
        ),
      ),
    );
  }
}
```

## 6. Улучшение Widget композиции

### ❌ Текущий код (плохо)

```dart
// Прямое использование цветов вместо темы
Container(
  color: Colors.orange, // Hardcoded цвет!
  child: Text('Status'),
),

// Отсутствие переиспользуемых компонентов
class ProductDetailPage extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 500+ строк кода в одном виджете!
    return Scaffold(
      body: Column(
        children: [
          // Inline виджеты без извлечения
          Container(
            height: 200,
            child: Stack(
              children: [
                // Еще 50 строк...
              ],
            ),
          ),
          // И так далее...
        ],
      ),
    );
  }
}
```

### ✅ После рефакторинга (хорошо)

```dart
// packages/core_ui/lib/src/widgets/status_badge.dart
class StatusBadge extends StatelessWidget {
  final String status;
  final StatusType type;
  
  const StatusBadge({
    super.key,
    required this.status,
    required this.type,
  });
  
  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    
    final (backgroundColor, textColor) = switch (type) {
      StatusType.success => (theme.colors.success, theme.colors.onSuccess),
      StatusType.warning => (theme.colors.warning, theme.colors.onWarning),
      StatusType.error => (theme.colors.destructive, theme.colors.onDestructive),
      StatusType.info => (theme.colors.info, theme.colors.onInfo),
    };
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.smallPadding,
        vertical: AppConstants.smallPadding / 2,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        status,
        style: theme.typography.xs.copyWith(
          color: textColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}

// apps/my_client_app/lib/features/productDetail/product_detail_page.dart
class ProductDetailPage extends ConsumerWidget {
  final ProductDetailArgs args;
  
  const ProductDetailPage({
    super.key,
    required this.args,
  });
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final product = args.product;
    if (product == null) {
      return const ProductNotFoundScreen();
    }
    
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          ProductImageSliver(product: product),
          SliverToBoxAdapter(
            child: ProductDetailsContent(
              product: product,
              hidePartnerField: args.hidePartnerField,
            ),
          ),
        ],
      ),
      bottomNavigationBar: ProductBottomBar(product: product),
    );
  }
}

// Отдельные виджеты для каждой секции
class ProductImageSliver extends StatelessWidget {
  final Product product;
  
  const ProductImageSliver({
    super.key,
    required this.product,
  });
  
  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      expandedHeight: 200,
      pinned: true,
      flexibleSpace: FlexibleSpaceBar(
        background: ProductImageGallery(
          images: product.images,
        ),
      ),
    );
  }
}

class ProductDetailsContent extends StatelessWidget {
  final Product product;
  final bool hidePartnerField;
  
  const ProductDetailsContent({
    super.key,
    required this.product,
    required this.hidePartnerField,
  });
  
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ProductHeader(product: product),
          const SizedBox(height: AppConstants.defaultPadding),
          ProductPriceSection(product: product),
          const SizedBox(height: AppConstants.defaultPadding),
          ProductDescriptionSection(description: product.description),
          if (!hidePartnerField) ...[
            const SizedBox(height: AppConstants.defaultPadding),
            ProductPartnerSection(partnerId: product.partnerId),
          ],
          const SizedBox(height: AppConstants.largePadding),
        ],
      ),
    );
  }
}
```

## 7. Добавление тестов

### ❌ Текущий код (без тестов)

```dart
// Нет тестов для критических функций
class BookingRepository {
  Future<void> createBooking(Booking booking) async {
    // Логика без тестов
  }
}
```

### ✅ После рефакторинга (с тестами)

```dart
// test/unit/data/repositories/booking_repository_test.dart
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockLogger extends Mock implements Logger {}

void main() {
  late FakeFirebaseFirestore fakeFirestore;
  late BookingRepository repository;
  late MockLogger mockLogger;
  
  setUp(() {
    fakeFirestore = FakeFirebaseFirestore();
    mockLogger = MockLogger();
    repository = BookingRepository(
      firestore: fakeFirestore,
      logger: mockLogger,
    );
  });
  
  group('BookingRepository', () {
    group('createBooking', () {
      test('successfully creates booking with valid data', () async {
        // Arrange
        final booking = createTestBooking(
          clientId: 'client123',
          productId: 'product456',
          date: DateTime.now().add(const Duration(days: 1)),
        );
        
        // Act
        final result = await repository.createBooking(booking);
        
        // Assert
        expect(result.id, isNotEmpty);
        expect(result.status, equals(BookingStatus.pending));
        
        // Verify document was created in Firestore
        final doc = await fakeFirestore
            .collection('bookings')
            .doc(result.id)
            .get();
        
        expect(doc.exists, isTrue);
        expect(doc.data()?['clientId'], equals('client123'));
      });
      
      test('throws ValidationException for past date', () async {
        // Arrange
        final booking = createTestBooking(
          date: DateTime.now().subtract(const Duration(days: 1)),
        );
        
        // Act & Assert
        expect(
          () => repository.createBooking(booking),
          throwsA(
            isA<ValidationException>().having(
              (e) => e.fieldErrors['date'],
              'date error',
              contains('прошлом'),
            ),
          ),
        );
      });
      
      test('throws ValidationException for invalid quantity', () async {
        // Arrange
        final booking = createTestBooking(quantity: 0);
        
        // Act & Assert
        expect(
          () => repository.createBooking(booking),
          throwsA(
            isA<ValidationException>().having(
              (e) => e.fieldErrors['quantity'],
              'quantity error',
              contains('больше 0'),
            ),
          ),
        );
      });
      
      test('throws RepositoryException on Firestore error', () async {
        // Arrange
        final booking = createTestBooking();
        
        // Simulate Firestore error by closing the instance
        await fakeFirestore.terminate();
        
        // Act & Assert
        expect(
          () => repository.createBooking(booking),
          throwsA(isA<RepositoryException>()),
        );
        
        // Verify error was logged
        verify(() => mockLogger.error(
          any(),
          any(),
          any(),
        )).called(1);
      });
    });
    
    group('updateBookingStatus', () {
      test('successfully updates status with valid transition', () async {
        // Arrange
        final bookingId = 'booking123';
        await fakeFirestore
            .collection('bookings')
            .doc(bookingId)
            .set({
          'id': bookingId,
          'status': BookingStatus.pending.name,
          'updatedAt': FieldValue.serverTimestamp(),
        });
        
        // Act
        await repository.updateBookingStatus(
          bookingId,
          BookingStatus.confirmed,
        );
        
        // Assert
        final updated = await fakeFirestore
            .collection('bookings')
            .doc(bookingId)
            .get();
        
        expect(updated.data()?['status'], equals('confirmed'));
      });
      
      test('throws ValidationException for invalid status transition', () async {
        // Arrange
        final bookingId = 'booking123';
        await fakeFirestore
            .collection('bookings')
            .doc(bookingId)
            .set({
          'id': bookingId,
          'status': BookingStatus.completed.name,
        });
        
        // Act & Assert
        expect(
          () => repository.updateBookingStatus(
            bookingId,
            BookingStatus.pending,
          ),
          throwsA(
            isA<ValidationException>().having(
              (e) => e.message,
              'message',
              contains('Невозможно изменить статус'),
            ),
          ),
        );
      });
    });
  });
}

// test/test_helpers/test_data.dart
Booking createTestBooking({
  String? id,
  String? clientId,
  String? productId,
  DateTime? date,
  int quantity = 1,
  BookingStatus status = BookingStatus.pending,
}) {
  return Booking(
    id: id ?? '',
    clientId: clientId ?? 'test_client',
    productId: productId ?? 'test_product',
    partnerId: 'test_partner',
    date: date ?? DateTime.now().add(const Duration(days: 7)),
    quantity: quantity,
    status: status,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );
}
```

## Заключение

Эти примеры демонстрируют основные паттерны рефакторинга, которые следует применить во всей кодовой базе:

1. **Правильная обработка ошибок** с custom exceptions и логированием
2. **Разбиение больших методов** на маленькие, тестируемые функции
3. **Извлечение констант** в централизованное место
4. **Оптимизация запросов** с батчингом и пагинацией
5. **Улучшение state management** с правильным использованием Riverpod
6. **Композиция виджетов** с переиспользуемыми компонентами
7. **Добавление тестов** для всей критической функциональности

Применение этих паттернов повысит качество, производительность и поддерживаемость кода.