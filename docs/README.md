# 📚 Документация Event App Monorepo

## Навигация по документации

### 🔥 Активные задачи (active/)
Текущие задачи и активная работа над проектом

- **[TODO.md](./active/TODO.md)** - Основной список задач разработки
- **[REQUEST_DETAIL_TODO.md](./active/REQUEST_DETAIL_TODO.md)** - Задачи по улучшению Request Detail
- **[TECHNICAL_DEBT.md](./active/TECHNICAL_DEBT.md)** - Технический долг и необходимые улучшения

### 📅 Планирование (planning/)
Планы развития и архитектурные решения

- **[ROADMAP.md](./planning/ROADMAP.md)** - Дорожная карта развития проекта
- **[REQUEST_DETAIL_REDESIGN_PLAN.md](./planning/REQUEST_DETAIL_REDESIGN_PLAN.md)** - План редизайна экрана деталей заявки
- **[MVP_IMPLEMENTATION_ANALYSIS.md](./planning/MVP_IMPLEMENTATION_ANALYSIS.md)** - Анализ реализации MVP функций
- **[request-requirements.md](./planning/request-requirements.md)** - Требования к функциональности заявок

### 📖 Справочники (reference/)
Руководства и справочная информация

- **[FORUI_GUIDE.md](./reference/FORUI_GUIDE.md)** - Руководство по использованию Forui UI библиотеки
- **[APP_COMPARISON.md](./reference/APP_COMPARISON.md)** - Сравнение функциональности приложений
- **[MONOREPO_DEPENDENCIES.md](./reference/MONOREPO_DEPENDENCIES.md)** - Диаграмма зависимостей пакетов
- **[MVP_PROGRESS_REPORT.md](./reference/MVP_PROGRESS_REPORT.md)** - Отчет о прогрессе MVP

### 🔧 Рефакторинг (refactoring/)
Анализ кода и рекомендации по улучшению

- **[REFACTORING_EXECUTIVE_SUMMARY.md](./refactoring/REFACTORING_EXECUTIVE_SUMMARY.md)** - 📊 Краткий обзор для руководства
- **[REFACTORING_RECOMMENDATIONS.md](./refactoring/REFACTORING_RECOMMENDATIONS.md)** - 🔧 Детальные рекомендации по рефакторингу
- **[REFACTORING_CODE_EXAMPLES.md](./refactoring/REFACTORING_CODE_EXAMPLES.md)** - 📝 Примеры кода: До и После

### 🗄️ Архив (archive/)
Завершенные этапы и устаревшие документы

- **[buyer-app-mvp-tasks.md](./archive/buyer-app-mvp-tasks.md)** - MVP задачи (завершено)

### 🎨 Ресурсы (assets/)
Изображения, примеры кода и другие материалы

- Примеры реализации StatefulShellRoute
- Скриншоты и диаграммы
- Примеры кода

---

## 🚀 Быстрый старт

1. **Для разработчиков** - начните с [CLAUDE.md](../CLAUDE.md) в корне проекта
2. **Текущие задачи** - смотрите [TODO.md](./active/TODO.md)
3. **Архитектура** - изучите [MONOREPO_DEPENDENCIES.md](./reference/MONOREPO_DEPENDENCIES.md)

## 📝 Структура документации

```
docs/
├── README.md          # Этот файл
├── active/           # Активные задачи и работа
├── planning/         # Планы и стратегии
├── reference/        # Справочники и руководства
├── refactoring/      # Анализ и рекомендации по рефакторингу
├── archive/          # Завершенные документы
└── assets/           # Изображения и примеры
```

## 🔄 Обновление документации

- Документация обновляется по мере развития проекта
- При завершении задач перемещайте документы из `active/` в `archive/`
- Новые планы добавляйте в `planning/`
- Руководства и справочники размещайте в `reference/`

---

**Последнее обновление:** Январь 2025