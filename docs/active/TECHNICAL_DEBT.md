# Technical Debt

## UI/UX Design

### Section Dividers Spacing
- [ ] Implement asymmetric padding for section dividers (less space above, more below)
- [ ] Current: SDivider with uniform spacing
- [ ] Target: Follow iOS/Material guidelines - 8pt above, 16-20pt below divider
- [ ] Alternative: Use divider height property to include padding (Airbnb approach)
- [ ] Affects all section dividers across the app for consistent visual hierarchy

## Request Model

### Data Access Issues
- [ ] Product price requires additional query through eventID → Product relationship
- [ ] No direct price information in Request model

## Request Detail Screen

### Temporary Solutions & Hacks
- [ ] **HistoryTimeline timestamps** - Использует DateTime.now() вместо реальных timestamps
  - Местоположение: `history_timeline.dart:87`
  - Влияние: Неверное отображение времени событий
  - Решение: Добавить реальные timestamps в модель Request

- [ ] **Order number generation** - Использует hashCode вместо уникального ID
  - Текущее: `request.id.hashCode % 10000`
  - Проблема: Возможны коллизии, непредсказуемые номера
  - Решение: Генерировать уникальные номера при создании заявки

### Unimplemented Business Logic in Existing Components
- [ ] **ActionButtons methods** - UI готов, но методы не реализованы
  - [ ] `_cancelRequest()` - пустая заглушка
  - [ ] `_contactOrganizer()` - пустая заглушка  
  - [ ] `_repeatOrder()` - метод удален, но функциональность может потребоваться
  - Местоположение: `action_buttons.dart`
  - Влияние: Кнопки не выполняют заявленные действия

### Architectural Issues
- [ ] **Product Price Access** - Нет прямого доступа к цене из Request
  - Проблема: Требуется дополнительный запрос через eventID → Product
  - Влияние: Лишние запросы к базе, задержки в UI
  - Решение: Денормализовать данные или использовать GraphQL

