# Задачи разработки

## 🚧 Активные задачи разработки

### 🌐 Веб-приложение (eventapp_client_web)

#### Высокий приоритет
- [ ] **Функция скачивания приложения**
  - Местоположение: `apps/eventapp_client_web/lib/features/partner/partner_detail_page.dart:125`
  - Описание: Реализовать умный призыв к действию скачивания с определением платформы
  - Оценка: 2-3 дня
  - Зависимости: Ссылки на App Store/Play Store

- [ ] **SEO оптимизация**
  - Добавить правильные мета-теги для лучшей индексации поисковиками
  - Реализовать структурированные данные для продуктов/партнеров
  - Добавить генерацию карты сайта
  - Оценка: 1-2 дня

- [ ] **Оптимизация производительности**
  - Реализовать ленивую загрузку изображений
  - Добавить правильные заголовки кэширования
  - Оптимизировать размер пакета
  - Оценка: 2 дня

#### Средний приоритет
- [ ] **Интеграция аналитики**
  - Добавить Google Analytics/Firebase Analytics
  - Отслеживать путь пользователя веб→мобильная конверсия
  - Отслеживание событий для ключевых действий пользователей
  - Оценка: 1 день

- [ ] **Обработка ошибок**
  - Добавить правильные границы ошибок
  - Реализовать механизмы повтора для неудачных API вызовов
  - Дружественные пользователю сообщения об ошибках
  - Оценка: 1-2 дня

### 📱 Мобильное приложение (my_client_app)

#### Высокий приоритет
- [ ] **Поддержка прямых ссылок**
  - Обработка входящих ссылок из веб-версии
  - Навигация к конкретным страницам партнеров/продуктов
  - Запасной вариант для недействительных/просроченных ссылок
  - Оценка: 2-3 дня

- [ ] **Категории push-уведомлений**
  - Подтверждения бронирования
  - Обновления партнеров
  - Рекламные уведомления
  - Оценка: 1-2 дня

#### Средний приоритет
- [ ] **Режим без интернета**
  - Кэширование избранных партнеров и продуктов
  - Просмотр истории бронирований без интернета
  - Синхронизация при восстановлении соединения
  - Оценка: 3-4 дня

- [ ] **Биометрическая аутентификация**
  - Вход через Face ID/Touch ID/отпечаток пальца
  - Безопасное хранение токенов аутентификации
  - Запасной вариант на PIN/пароль
  - Оценка: 2 дня

### 🏗️ Архитектура и инфраструктура

#### Высокий приоритет
- [ ] **Стандартизация обработки ошибок**
  - Реализовать единообразную обработку ошибок в обоих приложениях
  - Добавить правильное логирование и отчеты о сбоях
  - Дружественные пользователю сообщения об ошибках
  - Оценка: 2-3 дня

- [ ] **Оптимизация ответов API**
  - Реализовать правильную пагинацию
  - Добавить стратегии кэширования данных
  - Оптимизировать производительность запросов
  - Оценка: 2-3 дня

#### Средний приоритет
- [ ] **Улучшение библиотеки компонентов**
  - Извлечь больше общих компонентов в core_ui
  - Добавить полную документацию компонентов
  - Реализовать систему дизайн-токенов
  - Оценка: 3-4 дня

- [ ] **Инфраструктура тестирования**
  - Добавить полные модульные тесты
  - Реализовать интеграционные тесты
  - Настроить автоматическое тестирование в CI/CD
  - Оценка: 4-5 дней

### 🎨 Улучшения UI/UX

#### Высокий приоритет
- [ ] **Состояния загрузки**
  - Заменить временный тестовый текст в разделе "О партнере"
  - Добавить скелетоны загрузки
  - Реализовать плавные переходы загрузки
  - Оценка: 1-2 дня

- [ ] **Доступность**
  - Добавить правильные семантические метки
  - Реализовать поддержку программ чтения с экрана
  - Протестировать с инструментами доступности
  - Оценка: 2-3 дня

#### Средний приоритет
- [ ] **Микро-взаимодействия**
  - Добавить тактильную обратную связь для действий
  - Реализовать плавные анимации
  - Отполировать состояния кнопок и переходы
  - Оценка: 2 дня

- [ ] **Поддержка темной темы**
  - Реализовать варианты темной темы
  - Добавить функцию переключения темы
  - Протестировать все компоненты в темной теме
  - Оценка: 3-4 дня

---

## 🔍 TODO в коде (В кодовой базе)

> Запустите `rg "TODO|FIXME|HACK" --type dart` чтобы найти все TODO в коде

### Недавно добавленные
- `apps/eventapp_client_web/lib/features/partner/partner_detail_page.dart:125`
  - **TODO**: Реализовать функцию скачивания приложения
  - **Приоритет**: Высокий
  - **Контекст**: Пользователь нажимает на иконку избранного в веб-версии

- `apps/my_client_app/lib/src/features/partner/partner_detail_page.dart:74`
  - **TODO**: Убрать жестко заданный тестовый текст для раздела "О партнере"
  - **Приоритет**: Средний
  - **Контекст**: Заменить на реальные данные partner.about

### Устаревшие TODO
- Найти с помощью: `rg "TODO" --type dart --line-number`
- Просматривать ежемесячно и конвертировать в правильные задачи

---

## 📋 Процесс и обслуживание

### Еженедельные задачи
- [ ] Просмотреть и обновить этот список TODO
- [ ] Проверить новые TODO в коде
- [ ] Обновить прогресс активных задач

### Ежемесячные задачи
- [ ] Просмотреть завершенные задачи и архивировать
- [ ] Приоритизировать элементы бэклога
- [ ] Обновить оценки на основе фактического времени выполнения
- [ ] Синхронизировать с этапами ROADMAP.md

### Квартальные задачи
- [ ] Большой обзор архитектуры
- [ ] Аудит производительности
- [ ] Оценка безопасности
- [ ] Обновление зависимостей

---

## 📊 Легенда статуса задач

- [ ] **Не начато** - Задача еще не начата
- 🚧 **В процессе** - В настоящее время выполняется
- ✅ **Завершено** - Задача закончена и протестирована
- ❌ **Отменено** - Задача больше не нужна
- ⏸️ **Приостановлено** - Задача приостановлена в ожидании зависимостей

---

**Последнее обновление**: Январь 2025  
**Следующий обзор**: Следующая неделя