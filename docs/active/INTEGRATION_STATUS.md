# 📊 Статус интеграции seller app

## ✅ Завершено

### Этап 0: Удаление FVM и настройка Melos (Завершено)
- [x] Удалены все упоминания FVM из CLAUDE.md
- [x] Заменены команды `fvm flutter` на `flutter`  
- [x] Установлен melos глобально (версия 6.3.3)
- [x] Добавлен melos в PATH системы
- [x] Выполнен `melos bootstrap` - все 9 пакетов найдены
- [x] Проверен `melos list` - parnter2024 корректно обнаружено

**Результат:** Melos правильно видит seller app как часть монорепозитория

## 🟡 В процессе

*Ожидает выбора следующего этапа*

## ⏳ Ожидает выполнения

### Этап 1: Критическая безопасность  
- [ ] Удаление hardcoded паролей из build.gradle
- [ ] Создание keystore.properties
- [ ] Firebase Security Rules
- [ ] Инициализация Firebase App Check

### Этап 2: Переименование и очистка
- [ ] Переименование parnter2024 → eventapp_seller_app
- [ ] Удаление неиспользуемых зависимостей
- [ ] Исправление предупреждений анализатора

### Этап 3: Архитектурная интеграция
- [ ] Интеграция с core пакетами
- [ ] Удаление дублированных моделей
- [ ] Замена репозиториев на core_data

## 📋 Следующие шаги

**Готово к работе:** Выберите конкретный пункт из любого этапа для выполнения.

**Рекомендация:** Начать с безопасности (Этап 1.1) или архитектурной интеграции (Этап 3.1).