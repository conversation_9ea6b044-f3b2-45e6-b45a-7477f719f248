# Отчёт о миграции репозиториев partner2024 в core_data (15.06.2025)

## 1. Цель
Проверить фактическое состояние миграции репозиториев приложения `partner2024` в общий пакет `core_data`, обнаружить возможные дублирования/конфликты и дать рекомендации.

## 2. Структура папки `apps/parnter2024/lib/app/repo`
```
client_repo/    ← дублирование **удалено** ✅
product_repo/   ← обёртки с уникальным префиксом ✅
partner_repo/   ← обёртки с уникальным префиксом ✅
category/       ← пусто, миграция завершена ✅
fcmtoken_repo/  ← партнёр-специфичная логика (оставляем) ✅
```

## 3. Анализ репозиториев
| Репозиторий | Статус миграции | Дублирование/конфликт | Итог |
|-------------|-----------------|-----------------------|------|
| **Client**  | Реализация `ClientRepoFirebase` и провайдеры полностью присутствуют в `core_data` | Локальные файлы `client_repo.dart` и `firebase_cleint_repo.dart` создавали дублирование провайдеров (`fetchClient…`) | Локальные файлы удалены. Приложение использует `firebaseClientRepoProvider` и связанные провайдеры из `core_data`. |
| **Product** | Основная логика в `core_data` (`product_repo_provider.dart`) | В partner2024 сохранены ТОЛЬКО обёртки `partnerFetchProductList` и `partnerProductListStream` c уникальным префиксом | Конфликтов нет. |
| **Partner** | Базовая реализация — `core_data` (`partner_repo_provider.dart`) | В partner2024 сохранена обёртка `partnerSelfStream` с уникальным префиксом, использует `partnerStreamProviderProvider` | Конфликтов нет. |
| **Category** | Полностью мигрирована в `core_data` | Каталог пуст ⇒ дублирования нет | ОК |
| **FCM-Token** | Бизнес-специфичная логика продавца | Не мигрируется умышленно | ОК |

## 4. Проверка импортов
* Поиск по коду подтвердил отсутствие ссылок на удалённые файлы или провайдеры `clientRepoProvider`, `FetchClientList`, `FetchClient`, `FetchClientListById` в партнёрском коде.
* Все текущие вызовы (например, в *clients_section.dart*, *request_detail_notifier.dart*, *request_card.dart*) обращаются к одноимённым провайдерам из `core_data` — конфликтов имён нет.

## 5. Рекомендации
1. **Единое правило префиксов.**  Любая партнёр-специфичная обёртка должна иметь префикс `partner*` и НЕ дублировать имена провайдеров из `core_data`.
2. **CI-проверка.**  Добавить в CI шаг, проверяющий отсутствие локальных файлов репозиториев с базовыми именами (`*_repo.dart`) внутри `apps/parnter2024/lib/app/repo`, кроме явно разрешённых обёрток.
3. **Документация.**  Обновить `PARTNER2024_INTEGRATION_PLAN.md`, отметив завершение миграции ClientRepository и уточнив статус Product/Partner.
4. **Синхронизация версий.**  При изменениях в `core_data` провайдеров необходимо проверять, не появляются ли новые имена, дублирующие партнёрские обёртки.

## 6. Заключение
На текущий момент репозитории `Client`, `Product`, `Partner`, `Category` корректно мигрированы к `core_data`. В партнёрском приложении остались только лёгкие обёртки с уникальными префиксами, конфликты провайдеров устранены.

> Миграция репозиториев partner2024 в общий слой данных **завершена**, ошибок не обнаружено. 