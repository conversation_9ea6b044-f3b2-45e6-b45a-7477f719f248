# 🚨 План интеграции приложения продавца в монорепозиторий

## Анализ текущего состояния

### Критические проблемы безопасности ⚠️
- **Hardcoded пароли** в `apps/parnter2024/android/app/build.gradle`:
  ```gradle
  storePassword "Samat2024!"
  keyPassword "Samat2024!"
  ```
- **Keystore файлы в git** репозитории (`seller_key.jks`)
- **Отсутствие Firebase Security Rules**
- **Firebase App Check не инициализирован**

### Архитектурные проблемы
- **Дублирование моделей** с `core_domain`:
  - Product, Request, Partner, Category существуют в обеих местах
  - Разные поля в моделях (tags vs eventRefList)
- **Не использует core пакеты** монорепозитория
- **Собственные репозитории** вместо `core_data`
- **Неправильное название** "parnter2024" (должно быть "partner")

### Технические долги
- Смешение паттернов (flutter_riverpod + hooks_riverpod)
- Отсутствие тестов (0% покрытие)
- Много неиспользуемых зависимостей (~2MB лишнего веса)
- 30+ предупреждений анализатора

## Этапы интеграции

### 🔴 Этап 1: Критическая безопасность (НЕМЕДЛЕННО - 2-3 часа)

#### 1.1 Исправление hardcoded паролей
- [ ] Удалить пароли из `android/app/build.gradle`
- [ ] Создать `android/keystore.properties` (добавить в .gitignore)
- [ ] Обновить build.gradle для использования properties
- [ ] Проверить что keystore файлы не в git

#### 1.2 Firebase Security Rules
- [ ] Создать `firestore.rules` с правилами доступа
- [ ] Создать `storage.rules` для Firebase Storage
- [ ] Развернуть правила в Firebase Console

#### 1.3 Инициализация Firebase App Check
- [ ] Добавить инициализацию в `main.dart`
- [ ] Настроить reCAPTCHA для веб

### 🟡 Этап 2: Переименование и структура (4-6 часов)

#### 2.1 Переименование приложения
- [ ] Переименовать `apps/parnter2024` → `apps/eventapp_seller_app`
- [ ] Обновить `name` в pubspec.yaml
- [ ] Обновить applicationId в android/app/build.gradle
- [ ] Обновить bundle identifier в iOS (если есть)

#### 2.2 Обновление конфигурации
- [ ] Обновить Firebase configuration для нового package name
- [ ] Обновить Shorebird configuration
- [ ] Проверить и обновить все ссылки на старое название

#### 2.3 Обновление melos.yaml
- [ ] Добавить новое приложение в melos configuration
- [ ] Проверить что все команды работают

### 🟢 Этап 3: Интеграция с core пакетами (1-2 дня)

#### 3.1 Обновление зависимостей
- [ ] Обновить `pubspec.yaml`:
  ```yaml
  dependencies:
    core_domain:
      path: ../../packages/core_domain
    core_data:
      path: ../../packages/core_data
    core_ui:
      path: ../../packages/core_ui
    core_theme:
      path: ../../packages/core_theme
    core_utils:
      path: ../../packages/core_utils
    core_logging:
      path: ../../packages/core_logging
  ```

#### 3.2 Удаление дублированных моделей
- [ ] Удалить `lib/app/repo/product_repo/product_model.dart`
- [ ] Удалить `lib/app/repo/partner_repo/partner_model.dart`
- [ ] Удалить `lib/app/repo/client_repo/client_model.dart`
- [ ] Удалить `lib/app/repo/category/category_model.dart`
- [ ] Удалить `lib/app/repo/request_repo/domain/request_model.dart`

#### 3.3 Замена на core модели
- [ ] Заменить импорты на `package:core_domain/core_domain.dart`
- [ ] Проверить совместимость полей моделей
- [ ] Обновить сериализацию если нужно

#### 3.4 Интеграция репозиториев
- [ ] Заменить локальные репозитории на `core_data`
- [ ] Удалить дублированные Firebase реализации
- [ ] Обновить providers для использования core репозиториев

#### 3.5 UI интеграция
- [ ] Заменить кастомные компоненты на `core_ui`
- [ ] Использовать `core_theme` для стилизации
- [ ] Обновить Forui компоненты до единой версии

### 🔵 Этап 4: Рефакторинг и стандартизация (2-3 дня)

#### 4.1 Приведение к стандартам CLAUDE.md
- [ ] Добавить `NotifierMounted` mixin ко всем async контроллерам
- [ ] Использовать `AsyncValue.guard()` для Firebase операций
- [ ] Обновить error handling паттерны
- [ ] Добавить proper логирование через `core_logging`

#### 4.2 Навигация
- [ ] Обновить на typed routes из core_navigation (если создан)
- [ ] Унифицировать роутинг с остальными приложениями
- [ ] Проверить deep linking и navigation stack

#### 4.3 Очистка зависимостей
- [ ] Удалить неиспользуемые зависимости:
  ```yaml
  # Удалить:
  flutter_form_builder: ^10.0.1
  form_builder_image_picker: ^4.1.0
  form_builder_validators: ^11.0.0
  font_awesome_flutter: ^10.7.0
  flex_color_scheme: ^8.0.1
  hooks_riverpod: ^2.6.1  # использовать только flutter_riverpod
  flutter_hooks: ^0.20.5
  ```

#### 4.4 Code generation
- [ ] Запустить `melos run build:all`
- [ ] Проверить что все generated файлы обновлены
- [ ] Убедиться что нет конфликтов

#### 4.5 Тестирование
- [ ] Создать базовую структуру тестов
- [ ] Добавить unit тесты для критических контроллеров
- [ ] Проверить что приложение запускается и основные функции работают

## Чек-листы для проверки

### После Этапа 1 - Безопасность ✅
- [ ] Нет hardcoded паролей в коде
- [ ] Keystore.properties в .gitignore
- [ ] Firebase Security Rules активны
- [ ] App Check инициализирован

### После Этапа 2 - Переименование ✅
- [ ] Приложение переименовано в eventapp_seller_app
- [ ] Package name обновлен
- [ ] Melos команды работают
- [ ] Firebase configuration корректный

### После Этапа 3 - Интеграция ✅
- [ ] Использует все core пакеты
- [ ] Нет дублированных моделей
- [ ] Репозитории из core_data
- [ ] UI компоненты из core_ui

### После Этапа 4 - Рефакторинг ✅
- [ ] Соответствует стандартам CLAUDE.md
- [ ] NotifierMounted везде используется
- [ ] Typed routes настроены
- [ ] Зависимости очищены
- [ ] Приложение стабильно работает

## Оценка времени

| Этап | Время | Приоритет |
|------|-------|-----------|
| 1. Безопасность | 2-3 часа | 🔴 Критично |
| 2. Переименование | 4-6 часов | 🟡 Высокий |
| 3. Интеграция | 1-2 дня | 🟢 Средний |
| 4. Рефакторинг | 2-3 дня | 🔵 Низкий |

**Общее время:** 4-6 дней  
**Рекомендация:** Начать немедленно с Этапа 1

## Риски и митигация

### Высокие риски
1. **Потеря данных при переименовании** → Создать backup перед началом
2. **Конфликты моделей** → Тщательно сравнить поля перед заменой
3. **Сломанная функциональность** → Поэтапное тестирование после каждого шага

### Средние риски
1. **Firebase конфигурация** → Проверить совместимость с существующими правилами
2. **Зависимости версий** → Использовать lock файлы для стабильности

## Команды для выполнения

```bash
# После каждого этапа:
melos bootstrap
melos run build:all
melos run analyze:all

# Для тестирования:
cd apps/eventapp_seller_app && fvm flutter run

# Для проверки безопасности:
rg "password|key|secret" --type dart
```

---

**Статус:** Готов к выполнению  
**Последнее обновление:** $(date)  
**Следующий шаг:** Ожидание указания конкретного пункта для выполнения