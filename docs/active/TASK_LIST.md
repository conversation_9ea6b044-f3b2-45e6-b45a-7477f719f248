# TODO List

This document tracks all active tasks in a simple list format.



## Tasks

- [x] ниже будет рекомендация от gemini. думай очень глубоко, ознакомся, проведи анализ. если совет хороший, то напиши отчет где ты опишешь свое профессиональное мнение, детальное. если есть варианты то предлагай. ничего не меняй. напиши детальный отчет в отдельном файле чтобы было читабельно.
"Рекомендация №3: Разделение ответственности между UI и State Management (Принцип "Состояние управляет, UI реагирует")
Суть проблемы: В некоторых частях вашего кода (например, event_edit_page.dart, confirm_tab.dart) логика, отвечающая за действия пользователя (сохранение, удаление), напрямую вызывает UI-эффекты, такие как ScaffoldMessenger.of(context).showSnackBar(...) или showDialog(...). Это смешивает бизнес-логику с логикой отображения.
Почему это важно в монорепозитории: Когда бизнес-логика "чистая" (не зависит от BuildContext и не вызывает UI-эффекты), её становится тривиально тестировать unit-тестами. Её также можно легко переиспользовать в другом приложении (например, в будущем админ-панели на Flutter Web), которому может потребоваться другая логика отображения фидбека (например, desktop-уведомления вместо снэк-баров).
Что конкретно сделать:
Сделайте Notifier-ы ответственными только за состояние.
Ваш RequestsNotifier — прекрасный пример для подражания! В нем есть специальное поле feedback, которое хранит информацию о результате операции (RequestFeedbackState.success или RequestFeedbackState.error). UI слушает это поле и сам решает, как показать фидбек.
Примените этот паттерн к другим контроллерам:
EditEventController: Вместо того чтобы просто менять state на AsyncError, который UI должен "поймать", добавьте в него отдельное состояние для фидбека.
AddEventController: Вместо того чтобы UI в confirm_tab.dart отслеживал всю цепочку isLoading -> hasError, контроллер должен выставлять понятное состояние: bool isCreating, Object? error, а UI просто реагировать на эти флаги.
Очистите UI от вызова логики фидбека из хуков и контроллеров.
В event_edit_page.dart:
Сейчас: Хук handleSave вызывает ref.read(editEventControllerProvider.notifier).updateEvent(...), а useEffect слушает провайдер и вызывает _showErrorDialog или ScaffoldMessenger.
Как должно быть: handleSave просто вызывает метод нотифаера. EditEventController обновляет свое состояние (например, feedbackState). А виджет EventEditPage через ref.listen следит за feedbackState и сам решает, какой диалог или снэк-бар показать. Логика обновления данных полностью отделена от её отображения.
В confirm_tab.dart:
Сейчас: Длинная цепочка ref.listen с вложенной логикой pop -> showErrorDialog или pop -> showSnackBar -> pop.
Как должно быть: AddEventController должен управлять состоянием isCreating и error. Виджет ConfirmTab строит UI на основе этих простых флагов. Для навигации и снэк-баров используется ref.listen на отдельное "событийное" состояние в контроллере (например, OneTimeEvent.creationSuccess).
Результат: Вы получите тестируемые, переиспользуемые и чистые Notifier-ы, а UI-код станет декларативным и предсказуемым."

- [x] проверь этот файл. можно ли вынести apps/parnter2024/lib/app/common/enum_status.dart
- [x] првоерь этот файл. какую функцию выполняет, можно ли вынести или удалить. apps/parnter2024/lib/app/common/providers/firebase_providers.dart [MIGRATED]
- [x] проверь и этот файл apps/parnter2024/lib/app/utils/notifier_mounted.dart [MIGRATED]
- [x] возможно этот файл для темы тоже не используется. необходимо определить и реализвать самое лучшее решение apps/parnter2024/lib/app/utils/theme/app_theme.dart [DELETED]
- [x] apps/parnter2024/lib/theme эту папку я создавал временно. импортировал стили forui чтобы понять как реализовано в пакете. Теоритический не используется. проведи анализ, выясни используется или нет. если нет то можно удалять. прими самое лучшее решение. [KEEP] 
- [x] apps/parnter2024/lib/theme определи какие файлы(темы) не используются. как профессионал реализуй самое лучшее решение. если надо будет то удаляй [CLEANED]



## Guidelines
- Use `- [ ]` for incomplete tasks.
- Use `- [x]` for completed tasks.
- Add priority and deadline in parentheses if needed.

## Adding New Tasks
1. Add a new bullet point with `- [ ]`.
2. Include details like priority and deadline if relevant.

## Completion Reports

### 2025-01-14: enum_status.dart Analysis
**Task**: проверь этот файл. можно ли вынести apps/parnter2024/lib/app/common/enum_status.dart

**Analysis**: The `enum_status.dart` file contains a simple `Status { none, loading, success, error }` enum that is NOT used anywhere in the codebase. The authentication system uses its own `AuthStatus` enum defined directly in `auth_provider.dart` with values `{ initial, loading, sentOTP, loggedIn, error }`.

**Files searched**: 
- No imports of `enum_status.dart` found
- No usage of `Status.none`, `Status.loading`, `Status.success`, `Status.error` found
- Authentication files use `AuthStatus` enum instead

**Decision**: **DELETE** the file - it's unused dead code. The authentication system has its own domain-specific status enum that is more appropriate.

### 2025-01-14: firebase_providers.dart Analysis
**Task**: првоерь этот файл. какую функцию выполняет, можно ли вынести или удалить. apps/parnter2024/lib/app/common/providers/firebase_providers.dart

**Analysis**: The file provides Riverpod providers for Firebase services (FirebaseFirestore, FirebaseStorage, FirebaseAuth). However, there's duplication with core_data package which has its own `firebase_providers.dart` with only FirebaseFirestore.

**Current usage**:
- Used in 3 files: add_event_controller.dart, auth_provider.dart, auth_repo.dart
- Some files already use `hide firebaseFirestoreProvider` due to conflicts with core_data

**Decision**: **KEEP** but enhance core_data providers. The partner app needs FirebaseAuth and FirebaseStorage providers that don't exist in core_data yet. Should eventually migrate to shared providers in core_data.

**MIGRATION COMPLETED**: Enhanced core_data firebase_providers.dart with FirebaseAuth and FirebaseStorage providers using @riverpod annotation. Updated all 3 usage files to import from core_data instead of local providers. Deleted local firebase_providers.dart and generated files. Build_runner completed successfully with 118 outputs.

### 2025-01-14: notifier_mounted.dart Analysis
**Task**: проверь и этот файл apps/parnter2024/lib/app/utils/notifier_mounted.dart

**Analysis**: The file contains NotifierMounted mixin for lifecycle safety in Riverpod notifiers. Found IDENTICAL implementation already exists in core_utils package at `src/mixins/notifier_mounted.dart` and is exported through core_utils.dart.

**Files using NotifierMounted**: 
- 6 controller files (5 active imports + 1 commented)
- profile_notifier.dart already imports from core_utils correctly

**MIGRATION COMPLETED**: Updated all 5 active imports to use `import 'package:core_utils/core_utils.dart' show NotifierMounted;` instead of local import. Deleted duplicate local file. The shared implementation in core_utils is already being used correctly.

### 2025-01-14: app_theme.dart Analysis  
**Task**: возможно этот файл для темы тоже не используется. необходимо определить и реализвать самое лучшее решение apps/parnter2024/lib/app/utils/theme/app_theme.dart

**Analysis**: The file defines FlexColorScheme-based themes but is NOT used anywhere in the codebase. The app uses ForUI theming system accessed via `context.theme` instead. However, the `/theme` directory IS used - contains ForUI tile styles imported by selected_date_list.dart.

**Current status**:
- app_theme.dart: UNUSED (no imports found)
- /theme directory: USED (contains ForUI tile styles, has 1 active import)
- App uses ForUI theming throughout, not Material themes

**Decision**: **DELETE** app_theme.dart only - unused FlexColorScheme configuration. **KEEP** /theme directory as it contains active ForUI tile styles.

### 2025-01-14: /theme Directory Analysis
**Task**: apps/parnter2024/lib/theme эту папку я создавал временно. импортировал стили forui чтобы понять как реализовано в пакете. Теоритический не используется. проведи анализ, выясни используется или нет. если нет то можно удалять. прими самое лучшее решение.

**Analysis**: The `/theme` directory IS actively used in the codebase, not just experimental code.

**Active usage found**:
- `tile_styles/primary_tile_style.dart` - Used in `selected_date_list.dart` for custom ForUI tile styling
- `calendar_styles.dart` (in app/common/theme/) - Used by calendar components
- Contains working ForUI style customizations including PrimaryTileStyle and CustomContentStyle classes

**Files in directory**:
- `tile_styles/`: Active custom ForUI tile style implementations
- Other files: May be experimental but directory contains production code

**Decision**: **KEEP** the directory - it contains active ForUI customizations used by production components, not just experimental code.

### 2025-01-14: /theme Directory Cleanup  
**Task**: apps/parnter2024/lib/theme определи какие файлы(темы) не используются. как профессионал реализуй самое лучшее решение. если надо будет то удаляй

**Analysis**: Found 5 unused ForUI CLI-generated theme files that are experimental/template code, not used in production.

**Files analyzed**:
- ✅ `tile_styles/` - KEEP (actively used by selected_date_list.dart)
- ❌ `button_style.dart` - DELETE (unused ForUI CLI template)
- ❌ `theme.dart` - DELETE (unused custom theme definition)  
- ❌ `tile_group_style.dart` - DELETE (unused ForUI CLI template)
- ❌ `tile_style.dart` - DELETE (unused ForUI CLI template)
- ❌ `tile_style copy.dart` - DELETE (duplicate file with bad naming)

**CLEANUP COMPLETED**: Deleted 5 unused theme files, kept only active tile_styles directory. No analysis errors introduced. Directory now contains only production-used custom ForUI styles.