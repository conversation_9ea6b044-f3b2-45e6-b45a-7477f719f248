# План интеграции partner2024 в монорепозиторий

## Обзор
Интеграция приложения partner2024 (продавец) в существующий Flutter монорепозиторий с использованием общих пакетов.

## 1. Анализ моделей данных

### 1.1 Полностью идентичные модели ✅
Эти модели можно сразу заменить на версии из `core_domain`:
- ✅ **Client** - все поля совпадают
- ✅ **Request** - все поля совпадают

### 1.2 Модели с небольшими различиями ✅

#### Product ✅
- **partner2024**: отсутствует поле `tags: List<String>`
- **Действие**: Добавить поле `tags` в partner2024 или сделать его опциональным в core_domain
- **Выполнено**: Добавлено поле tags в partner2024, модель синхронизирована

#### Event ✅
- **partner2024**: есть дополнительное поле `requestIdList: List<String>` и extension `EventExtensions`
- **Действие**: Добавить `requestIdList` в core_domain и перенести extension
- **Выполнено**: Добавлено поле и extension в core_domain, модель синхронизирована

#### Partner ✅
- **partner2024**: `eventRefList`, `productRefList`
- **core_domain**: `serviceRefList`, `serviceTypeRefList`
- **Действие**: Требуется анализ бизнес-логики для выбора правильных полей
- **Выполнено**: Изменены поля в core_domain на `eventRefList` и `productRefList` для единообразия

#### Category ✅
- **core_domain**: есть дополнительные поля `order: int` и `isActive: bool`
- **Действие**: Добавить эти поля в partner2024 с дефолтными значениями
- **Выполнено**: Добавлены поля order и isActive в partner2024, модель синхронизирована

## 2. Репозитории

### 2.1 Анализ репозиториев
После детального анализа выявлено:

#### Успешно мигрированы:
- ✅ CategoryRepository → Использует core_data ✅
- ✅ ClientRepository → Методы добавлены в core_data ✅
- ✅ EventRepository → Методы добавлены в core_data ✅
- ✅ PartnerRepository → Методы добавлены в core_data ✅
- ✅ ProductRepository → Методы добавлены в core_data ✅
- ✅ RequestRepository → Методы добавлены в core_data ✅
  - **⚠️ ОШИБКА (13.06.2025)**: Оставили wrapper providers в `request_repo.dart` с конфликтующими именами
  - **Проблема**: Провайдер `requestStreamProvider` дублировал имя из core_data, вызывая циклическую зависимость
  - **Решение**: Полностью удалили `request_repo.dart`, используем core_data напрямую
  - **Урок**: Wrapper providers должны иметь уникальные имена (префикс `partner*`) или не создаваться вообще
- ✅ StorageRepository → Загрузка изображений партнеров и продуктов ✅

#### ProductRepository
- **⚠️ ОШИБКА (14.06.2025)**: В `apps/parnter2024/lib/app/repo/product_repo/product_repo.dart` остались wrapper-providers с теми же именами, что и в core_data (`productRepoProvider`, `productListStreamProvider`, `fetchProductListProvider`).
- **Последствия**: цикл зависимостей и падение приложения при `hot-reload` (`ProviderNotFoundException`).
- **Исправление**: wrapper удалён; созданы уникальные обёртки `partnerProductListStreamProvider` и `partnerFetchProductListProvider` с авто-инъекцией `partnerID`; все вызовы переименованы.
- **Урок**: обёртки должны иметь уникальный префикс (`partner*`) и не дублировать имена из общих пакетов.

**Вывод**: Большинство репозиториев имеют специфичную для partner2024 бизнес-логику и должны быть сохранены. Они уже используют модели из core_domain (Phase 1 ✅).

### 2.2 Расширение репозиториев для partner2024

#### ClientRepository
Варианты решения:
1. **Расширить core_data** - Добавить метод `fetchClientListById` в IClientRepository и core_data
2. **Создать PartnerClientRepository** - Наследовать от core_data и добавить специфичные методы
3. **Оставить локальную реализацию** - Использовать core_domain модели, но сохранить специфичную логику

#### EventRepository  
Особенности partner2024:
- Фильтрация по `partnerRef` (текущий пользователь)
- CRUD операции (add, update, delete)
- Batch операции
- Получение DocumentReference

Рекомендация: Создать `PartnerEventRepository` который расширяет базовую функциональность.

### 2.3 Провайдеры
Использовать готовые Riverpod провайдеры из core_data где возможно, создать специфичные для partner2024 где необходимо.

## 3. UI компоненты

### 3.1 Компоненты для удаления (есть в core_ui) - ЗАВЕРШЕНО ✅
- ✅ `async_value_widget.dart` - удален, все импорты обновлены на core_ui (20 файлов)
- ✅ `error_message_widget.dart` - удален, все импорты обновлены на core_ui (2 файла)
- ✅ `primary_button.dart` - удален, все импорты обновлены на core_ui (6 файлов)
- ✅ `outline_button.dart` - удален, миграция на SecondaryButton/FButton завершена

**Дата завершения**: 13.06.2025  
**Результат**: Устранено дублирование кода, все компоненты используют общие версии из core_ui

### 3.2 Компоненты для добавления в core_ui
Результаты глубокого анализа и миграции (14.06.2025):

**✅ УСПЕШНО МИГРИРОВАНО:**
- ✅ `form_save_button.dart` → `core_ui/lib/widgets/buttons/form_save_button.dart`
  - Универсальная кнопка сохранения с поддержкой фиксированного позиционирования
  - Заменены импорты в 2 файлах partner2024
  - Удален оригинальный файл из partner2024

- ✅ `image_handler.dart` → `core_utils/lib/src/image/image_handler.dart`
  - Устранено дублирование между partner2024 и my_client_app
  - Использована улучшенная версия из partner2024 (лучшее качество)
  - Обновлены импорты в обоих приложениях
  - Удалены дублированные файлы

**❌ НЕ МИГРИРОВАНО (по результатам анализа):**
- ❌ `my_form_text_field.dart` - требует доработки для универсального использования
- ❌ `bottom_action_button.dart` - слишком domain-специфичен для partner app
- ❌ `validators.dart` - базовая реализация с hardcoded Russian strings

### 3.3 Специфичные компоненты (остаются в partner2024)
- Все виджеты в features/
- Кастомные карточки событий
- Специфичная для продавца навигация

## 4. Утилиты и сервисы

### 4.1 Перенести в core_utils ЗАВЕРШЕНО ✅
- ✅ `notifier_mounted.dart` - уже есть в core_utils
- 📦 `datetime_extension.dart` - специфичная русская локаль, оставлена локально (см. примечание ниже)
- ✅ `string_extensions.dart` - перенесена в core_utils (15.06.2025)

> **Примечание 15.06.2025**  
> Общий `datetime_extension.dart` уже существует в core_utils.  
> Партнёрская версия отличается фиксированной русской локалью и `shortDayOfWeek`.  
> Решение: оставить локально как `datetime_extension_ru.dart`; возможна дальнейшая локализация через intl, но не критично для MVP.

### 4.2 Оставить в partner2024
- `push_notification_helper.dart` - специфично для приложения
- `talker_service.dart` - конфигурация логирования
- `fcm_token_repo.dart` - работа с FCM токенами

## 5. Enums

### Уже есть в core_domain:
- ✅ `duration_type_enum.dart` → `enum_duration_type.dart`
- ✅ `request_status.dart` → `enum_request_status.dart`

### Специфичные для partner2024:
- `reject_reason.dart` - причины отклонения (возможно стоит перенести в core_domain)

## 6. План миграции

### Фаза 1: Подготовка (1-2 дня)
1. Создать ветку для интеграции
2. Обновить pubspec.yaml - добавить зависимости от core пакетов
3. Запустить `melos bootstrap`

### Фаза 2: Модели и репозитории (2-3 дня)
1. Синхронизировать различающиеся модели
2. Обновить все импорты моделей на core_domain
3. Удалить локальные модели
4. Заменить репозитории на core_data
5. Обновить провайдеры

### Фаза 3: UI компоненты (1-2 дня)
1. Заменить дублирующиеся компоненты на core_ui
2. Перенести полезные компоненты в core_ui
3. Обновить импорты

### Фаза 4: Тестирование (2-3 дня)
1. Запустить code generation
2. Исправить ошибки компиляции
3. Протестировать основные сценарии
4. Исправить проблемы с навигацией

### Фаза 5: Очистка (1 день)
1. Удалить неиспользуемый код
2. Обновить документацию
3. Провести финальное тестирование

## 7. Ожидаемые результаты

### Фактические результаты после анализа:
- ✅ 100% моделей данных мигрированы на core_domain
- ✅ 7 из 7 репозиториев мигрированы на core_data (все репозитории!)
- ✅ Фаза 2 полностью завершена
- 🔄 UI компоненты и утилиты еще не анализировались (Фаза 3)

### Преимущества:
- Единая кодовая база для моделей
- Централизованная работа с Firebase
- Переиспользование UI компонентов
- Упрощенная поддержка
- Консистентность между приложениями

## 8. Риски и проблемы

1. **Различия в моделях Partner** - требуется анализ бизнес-логики
2. **Специфичные расширения** - например, EventExtensions
3. **Возможные конфликты версий** пакетов
4. **Необходимость рефакторинга** существующего кода

## 9. Приоритеты

1. 🔴 **Критично**: Синхронизация моделей данных
2. 🟡 **Важно**: Миграция на общие репозитории
3. 🟢 **Желательно**: Перенос UI компонентов в core_ui

## Следующие шаги

1. Обсудить различия в модели Partner
2. Определить, какие UI компоненты стоит сделать общими
3. Создать план тестирования
4. Начать с Фазы 1

## 10. Уроки и рекомендации после миграции

### Архитектурные решения (13.06.2025)

1. **Wrapper Providers**: 
   - ❌ НЕ создавать wrapper providers с теми же именами, что в core_data
   - ✅ Использовать уникальные имена с префиксом (например, `partnerRequestStream` вместо `requestStream`)
   - ✅ Или вообще не создавать wrapper, если нет partner-специфичной логики

2. **Паттерн для partner-специфичных провайдеров**:
   - Смотреть пример в `partner_event_providers.dart` - правильная реализация
   - Wrapper нужен только когда требуется автоматическая инъекция partnerID
   - Если логика одинаковая - использовать core_data напрямую

3. **Проверка после миграции**:
   - Убедиться, что нет конфликтов имен провайдеров
   - Проверить отсутствие циклических зависимостей
   - Запустить `flutter analyze` для выявления проблем
4. **Дублирование имён провайдеров**:
   - Ошибка повторно проявилась на `ProductRepository`. Вывод: **любой** wrapper-provider обязан иметь уникальный префикс (`partner*`) вне зависимости от сущности.

5. **Реорганизация провайдеров (14.06.2025)**:
   - **Проблема**: Путаница из-за организации - директория `/app/repo` содержала провайдеры, а не репозитории
   - **Решение**: 
     - Удалена директория `/app/repo`
     - Все wrapper-провайдеры объединены в `/app/common/providers/partner_auto_inject_providers.dart`
     - Добавлена документация в README.md
     - Все провайдеры теперь имеют префикс `partner` для ясности
   - **Результат**: Четкая структура, понятное назначение файлов, единообразное именование

- ✅ PartnerRepository → Методы добавлены в core_data ✅
  - **✅ ИСПРАВЛЕНО (14.06.2025)**: Wrapper-провайдеры перемещены в `partner_auto_inject_providers.dart`