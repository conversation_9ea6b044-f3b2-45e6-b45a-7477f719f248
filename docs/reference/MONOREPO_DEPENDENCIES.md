# Monorepo Dependency Mapping

This document outlines the dependencies between the applications and packages within the monorepo, based on the `pubspec.yaml` files.

## Dependency Graph

```mermaid
graph TD
    eventapp_client_web --> core_utils
    eventapp_client_web --> core_domain
    eventapp_client_web --> core_data
    eventapp_client_web --> core_logging
    eventapp_client_web --> core_ui
    eventapp_client_web --> core_theme

    my_client_app --> core_utils
    my_client_app --> core_domain
    my_client_app --> core_data
    my_client_app --> core_logging
    my_client_app --> core_ui
    my_client_app --> core_theme

    core_data --> core_domain
    core_ui --> core_domain
    core_ui --> core_utils
    core_ui --> core_logging
```

## Summary of Dependencies

**Applications:**

- `apps/eventapp_client_web` depends on: `core_utils`, `core_domain`, `core_data`, `core_logging`, `core_ui`, `core_theme`
- `apps/my_client_app` depends on: `core_utils`, `core_domain`, `core_data`, `core_logging`, `core_ui`, `core_theme`

**Packages:**

- `packages/core_data` depends on: `core_domain`
- `packages/core_domain` has no local dependencies.
- `packages/core_logging` has no local dependencies.
- `packages/core_theme` has no local dependencies.
- `packages/core_ui` depends on: `core_domain`, `core_utils`, `core_logging`
- `packages/core_utils` has no local dependencies.

This mapping provides a clear overview of how the different components interact within the monorepo.
