# Forui Guide

## Quick Reference

### Theme Access
```dart
final theme = context.theme;
```

### Colors
```dart
theme.colors.primary
theme.colors.secondary
theme.colors.background
theme.colors.foreground
theme.colors.mutedForeground
theme.colors.border
theme.colors.error
theme.colors.success
theme.colors.muted
theme.colors.destructive
```

### Typography
```dart
theme.typography.xl4   // 36px
theme.typography.xl3   // 30px
theme.typography.xl2   // 22px
theme.typography.xl    // 20px
theme.typography.lg    // 18px
theme.typography.base  // 16px
theme.typography.sm    // 14px
theme.typography.xs    // 12px
```

### Icons
```dart
// Forui provides many built-in icons
// Usage: FIcons.iconName
Icon(FIcons.home)
Icon(FIcons.user)
// ... and many more
```

## Common Components

### FButton
```dart
// Basic button
FButton(
  onPress: () {},
  child: Text('Button'),
)

// Icon button
FButton.icon(
  onPress: () {},
  child: FIcon(FIcons.settings),
)
```

### FCard
```dart
FCard(
  child: Padding(
    padding: EdgeInsets.all(16),
    child: Text('Content'),
  ),
)

// With custom styling
FCard(
  style: theme.cardStyle.copyWith(
    contentStyle: theme.cardStyle.contentStyle.copyWith(
      padding: EdgeInsets.zero,
    ),
  ),
  child: YourContent(),
)
```

### FSheet (Bottom Sheet)
```dart
// Show bottom sheet
showFSheet(
  context: context,
  side: FLayout.btt, // Bottom-to-top
  builder: (context) => YourSheetContent(),
);
```

### FTextField
```dart
FTextField(
  controller: controller,
  label: Text('Label'),
  hint: Text('Hint'),
)
```

## Styling Patterns

### Always use copyWith()
```dart
// ✅ Correct
FCard(
  style: theme.cardStyle.copyWith(
    contentStyle: theme.cardStyle.contentStyle.copyWith(
      padding: EdgeInsets.zero,
    ),
  ),
)

// ❌ Wrong - will cause required parameter errors
FCard(
  style: FCardStyle(
    contentStyle: FCardContentStyle(
      padding: EdgeInsets.zero,
    ),
  ),
)
```

### Text Style Mappings from shadcn_ui

| Old (shadcn_ui) | New (Forui) | Additional Styling |
|-----------------|-------------|-------------------|
| `textTheme.lead` | `theme.typography.xl` | `.copyWith(color: theme.colors.mutedForeground)` |
| `textTheme.large` | `theme.typography.lg` | `.copyWith(fontWeight: FontWeight.w600)` |
| `textTheme.base` | `theme.typography.base` | - |
| `textTheme.small` | `theme.typography.sm` | `.copyWith(fontWeight: FontWeight.w500)` |
| `textTheme.muted` | `theme.typography.sm` | `.copyWith(color: theme.colors.mutedForeground)` |

## App Setup
```dart
MaterialApp(
  builder: (context, child) => FTheme(
    data: FThemes.zinc.light,
    child: child!,
  ),
)
```