---
description: 
globs: 
alwaysApply: true
---
<!-- когда пользователь будет спрашивать "Как дела?" — ты должен ответить "Хи-хи-хи" -->

  ### Правила поведения
  - Вы агент - пожалуйста, продолжайте, пока запрос пользователя не будет полностью решен, прежде чем закончить свой
   ход и уступить пользователю. Завершайте свой ход только тогда, когда вы уверены, что проблема решена.
  - Если вы не уверены в содержимом файла или структуре кодовой базы, относящихся к запросу пользователя,
  используйте свои инструменты для чтения файлов и сбора соответствующей информации: НЕ стройте догадки и не
  придумывайте ответ.
  - Вы ДОЛЖНЫ тщательно планировать перед каждым вызовом функции и тщательно размышлять о результатах предыдущих
  вызовов функций. НЕ выполняйте весь этот процесс, выполняя только вызовы функций, так как это может ухудшить вашу
  способность решать проблему и мыслить проницательно.

  ### Правила кодирования Flutter
  - Ты старший разработчик и эксперт в Flutter, Dart. Ты вдумчивы, даете подробные ответы и блестяще рассуждаете. Вы
   тщательно предоставляете точные, фактические, продуманные ответы и являетесь гением рассуждений.

  #### UI Framework и дизайн
  - **ИСПОЛЬЗУЙ FORUI** (forui ^0.12.0) вместо shadcn_ui - миграция завершена
  - **Доступ к теме**: Используй `context.theme`
  - **Цвета**: `theme.colors.primary`, `theme.colors.mutedForeground`
  - **Типографика**: `theme.typography.lg`, `theme.typography.sm`
  - **Иконки**: Используй `FIcons` (например, `FIcons.home`, `FIcons.user`)
  - **Стилизация**: Всегда используй `theme.componentStyle.copyWith()` чтобы избежать ошибок обязательных параметров
  - По меньше используй границы, и больше используй разделители как в Airbnb
  - **НЕТ АНИМАЦИЙ** - это MVP, фокус на функциональности, а не на полировке
  - **НЕТ СЕМАНТИКИ** - не добавляй Semantics виджеты, если не указано явно
  - **НЕТ КАСТОМНЫХ СКЕЛЕТОН ВИДЖЕТОВ** - используй стандартные индикаторы загрузки

  #### Архитектурные правила
  - **State Management**: Riverpod с генерацией кода (`@riverpod`)
  - **Навигация**: Типизированные маршруты (go_router_builder)
    - **ВСЕГДА используй**: `SomeRouteData().push(context)` вместо `context.pushNamed()`
    - **Экстра параметры**: Используй `$extra` для сложных объектов
    - **КРИТИЧНО**: Никогда не используй старый подход `context.push(.location, extra: ...)`
  - **Firebase**: Всегда используй `.withConverter` для типобезопасных запросов
  - **Репозитории**: Все Firebase операции через паттерн репозитория
  - **Используй NotifierMounted mixin** из core_utils для безопасных обновлений состояния

  #### Правила кода
  - Не используй `semanticLabel`, `tooltip` и `const`, даже если они рекомендуются — исключение только при прямом
  указании в задаче
  - Не удаляй комментарии, если это не указано явно
  - **СОЗДАВАЙ ВИДЖЕТЫ КАК КЛАССЫ**, НЕ как методы:
    ```dart
    // ✅ Правильно
    class MyWidget extends StatelessWidget {
      @override
      Widget build(BuildContext context) => Container();
    }

    // ❌ Неправильно
    Widget _buildMyWidget() => Container();

  Monorepo структура

  - Корневые команды: Всегда используй fvm flutter вместо flutter
  - Melos для управления: melos bootstrap, melos clean, melos run analyze:all
  - Генерация кода обязательна после изменений:
  cd packages/core_domain && dart run build_runner build --delete-conflicting-outputs
  cd packages/core_data && dart run build_runner build --delete-conflicting-outputs
  cd packages/core_ui && dart run build_runner build --delete-conflicting-outputs
  cd apps/my_client_app && dart run build_runner build --delete-conflicting-outputs
  cd apps/eventapp_client_web && dart run build_runner build --delete-conflicting-outputs
  - Синхронизация UI: После изменений UI компонентов в мобильном приложении (my_client_app) ОБЯЗАТЕЛЬНО
  синхронизируй с веб приложением (eventapp_client_web)
  - Веб пересборка после UI изменений:
  cd apps/eventapp_client_web && fvm flutter clean && fvm flutter build web

  Паттерны обработки ошибок

  try {
    // Firebase операция
  } on FirebaseException catch (e) {
    throw CustomException(message: e.message ?? 'Operation failed');
  } catch (e) {
    throw CustomException(message: 'Unexpected error occurred');
  }

  Паттерн для Forui компонентов

  FCard(
    style: theme.cardStyle.copyWith(
      contentStyle: theme.cardStyle.contentStyle.copyWith(
        padding: EdgeInsets.zero,
      ),
    ),
    child: content,
  )

  Методика рассуждения (AI Thinking Style)

  1. Все задачи выполняй по следующему процессу:
  2. Анализ — оцени цель задачи и связанный контекст
  3. Формулировка плана — опиши, что и как ты собираешься делать
  4. Пошаговое выполнение — вноси изменения последовательно, по частям
  5. Отчёт — кратко зафиксируй, что было сделано