# Dart/Flutter
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
build/
*.lock
*.g.dart
*.freezed.dart
*.config.dart

# IDE
.idea/
.vscode/
*.iml

# Melos
.melos_tool/
pubspec_overrides.yaml

# iOS/XCode
**/ios/Flutter/.last_build_id
**/ios/Flutter/ephemeral
**/ios/Flutter/App.framework
**/ios/Flutter/Flutter.framework
**/ios/Flutter/Generated.xcconfig
**/ios/Flutter/flutter_export_environment.sh
**/ios/Pods/
**/ios/.symlinks/
**/ios/Runner.xcworkspace/xcshareddata/
**/ios/Runner.xcworkspace/xcuserdata/
**/ios/**/GeneratedPluginRegistrant.*
**/macos/**/GeneratedPluginRegistrant.*

# Android
**/android/**/gradle-wrapper.jar
**/android/.gradle
**/android/captures/
**/android/gradlew
**/android/gradlew.bat
**/android/local.properties
**/android/**/GeneratedPluginRegistrant.java
**/android/key.properties
**/android/.idea/
**/android/app/debug
**/android/app/profile
**/android/app/release

# Coverage
coverage/
*.lcov

# Firebase
.firebase/
firebase-debug.log

# macOS
.DS_Store

# Windows
Thumbs.db

# FVM Version Cache
.fvm/

# Documentation temporary files
docs/assets/