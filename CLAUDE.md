# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Quick Start

This is a Flutter monorepo for an event management platform with three apps:
- **Customer Apps**: Mobile (my_client_app) and Web (eventapp_client_web)
- **Partner App**: Seller management (parnter2024)

**Most common tasks:**
```bash
# After any model/provider/route changes, run code generation:
cd packages/core_domain && dart run build_runner build --delete-conflicting-outputs
cd apps/my_client_app && dart run build_runner build --delete-conflicting-outputs

# Run the customer mobile app:
cd apps/my_client_app && flutter run

# Check code quality:
melos run analyze:all
```

## Commands

### Initial Setup
```bash
# Install melos globally for monorepo management
dart pub global activate melos

# Bootstrap all packages (run after cloning or adding dependencies)
melos bootstrap

# Run code generation for all packages
melos run build:all
```

### Development Commands
```bash
# Run apps
cd apps/my_client_app && flutter run              # Customer mobile app
cd apps/eventapp_client_web && flutter run -d chrome  # Customer web app
cd apps/parnter2024 && flutter run                # Partner/seller app

# Run with verbose logging (partner app)
cd apps/parnter2024 && flutter run --dart-define=TALKER_ENABLED=true

# Analyze all packages
melos run analyze:all

# Format all packages
melos run format:all

# Run tests
cd apps/my_client_app && flutter test
cd packages/core_domain && flutter test
flutter test test/path/to/test.dart  # Single test
flutter test --coverage              # With coverage

# Clean and rebuild
melos clean && melos bootstrap
```

### Code Generation (Required after model/provider/route changes)
```bash
# Due to compatibility issues, run build_runner per package:
cd packages/core_domain && dart run build_runner build --delete-conflicting-outputs
cd packages/core_data && dart run build_runner build --delete-conflicting-outputs  
cd packages/core_ui && dart run build_runner build --delete-conflicting-outputs
cd apps/my_client_app && dart run build_runner build --delete-conflicting-outputs
cd apps/eventapp_client_web && dart run build_runner build --delete-conflicting-outputs
cd apps/parnter2024 && dart run build_runner build --delete-conflicting-outputs
```

### Build & Deployment
```bash
# Android
cd apps/my_client_app && flutter build apk --release
cd apps/my_client_app && flutter build appbundle --release
cd apps/parnter2024 && flutter build appbundle

# iOS (Mac required, my_client_app only)
cd apps/my_client_app && flutter build ipa

# Web (CRITICAL: clean before build to prevent icon desync)
cd apps/eventapp_client_web && flutter clean && flutter build web

# Deploy
shorebird patch android  # OTA updates for mobile apps
firebase deploy --only hosting  # Web app deployment
```

## Architecture

This is a Flutter monorepo using clean architecture with three apps sharing common packages:

### Apps
- **my_client_app**: Full-featured customer mobile app (iOS/Android)
- **eventapp_client_web**: Simplified customer web catalog (browse-only, max width 600px)
- **parnter2024**: Partner/seller management app (Android only)

### Core Packages
- **core_domain**: Business entities and repository interfaces
- **core_data**: Firebase repository implementations
- **core_ui**: Shared UI components
- **core_theme**: ForUI theme configuration
- **core_utils**: Extensions and utilities
- **core_logging**: Talker-based logging

### Key Architectural Patterns

1. **Repository Pattern with Partner-Specific Extensions**
   ```
   UI → Provider → Repository Interface → Firebase Implementation
                          ↓
                  Partner-specific wrapper providers (parnter2024)
   ```

2. **State Management**: Riverpod with code generation
   - Use `@riverpod` annotation
   - Handle AsyncValue states properly
   - Use `NotifierMounted` mixin for lifecycle safety in partner app

3. **Partner App Integration Pattern**
   - All repositories migrated to core_data with partner-specific extensions
   - Wrapper providers in `partner_event_providers.dart` auto-inject partner ID
   - Example:
   ```dart
   // Don't use core_data providers directly in partner app
   ref.watch(eventListStreamProvider)  // Auto-filters by current partner
   ```

4. **Navigation**: GoRouter with typed routes
   - Use `SomeRouteData().push(context)` for type-safe navigation
   - Never use `context.push(.location, extra: ...)`

5. **UI Framework**: ForUI
   - Access theme: `context.theme`
   - Use `copyWith()` to avoid required parameter errors

## Task Management Workflow

**CRITICAL: Always check the task list first before starting any work**

1. **Check Task List**: Read `docs/active/TASK_LIST.md` to see pending tasks
2. **Select Top Task**: Take the topmost uncompleted task (marked with `- [ ]`)
3. **Plan & Execute**: Create a plan using TodoWrite tool and execute the task
4. **Report & Complete**: Write a completion report at the bottom of TASK_LIST.md and mark task as completed (`- [x]`)

### Task List Location
- Primary task list: `docs/active/TASK_LIST.md`
- Follow the format: `- [ ]` for incomplete, `- [x]` for completed
- Always add completion reports with date and details

## Critical Development Notes

### Repository Migration Status (Partner App)
- ✅ All 7 repositories migrated to core_data
- Partner-specific logic handled via method extensions and wrapper providers
- StorageRepository marked complete but implementation details unclear

### Common Gotchas
- Run build_runner after ANY model/provider/route changes
- Web app MUST clean before build to prevent icon issues
- Partner app uses wrapper providers, not direct core_data providers
- Import conflicts resolved with `hide` directives
- Build runner compatibility issue: Must run per package, not via melos
- Test coverage <1% - be extremely careful with changes

### Security Issues (CRITICAL)
- **Exposed Passwords**: `android/app/build.gradle` contains hardcoded keystore passwords
- **Missing Rules**: No `firestore.rules` or `storage.rules` files
- **App Check**: Not initialized despite being a dependency

### UI Development Rules
- NO ANIMATIONS (MVP constraint)
- NO SEMANTICS (unless requested)
- NO CUSTOM SKELETONS (use CircularProgressIndicator)
- MINIMAL STYLING (Airbnb-style minimalist)

## Testing & Code Quality

```bash
# Lint and analyze
melos run analyze:all
dart run custom_lint

# Check for TODOs/FIXMEs
rg "TODO|FIXME|HACK" --type dart
```

### Code Review Checklist
- No print statements (use logging)
- Proper error handling with try-catch
- AsyncValue states handled (loading, error, data)
- Repository pattern followed
- Typed routes used
- No hardcoded strings

## Key Documentation

Essential docs in `docs/`:
- `active/PARTNER2024_INTEGRATION_PLAN.md` - Repository migration status
- `active/TODO.md` - Current tasks
- `active/TECHNICAL_DEBT.md` - Known issues
- `refactoring/` - Security issues and recommendations

## Firebase Configuration
- Project ID: `event-app-b25e1`
- Shorebird App IDs:
  - my_client_app: `36e306fd-897d-456b-a702-918b910a13c6`
  - parnter2024: `51965aea-f13a-4492-a28a-5ea085593399`

## Additional Notes

### Package Dependencies
- ForUI 0.12.0 (UI framework)
- Riverpod 2.6.1+ (state management)
- GoRouter 15.1.2+ (navigation)
- Firebase suite (backend)
- Shorebird (OTA updates)

### Development Workflow Pattern
- Some developers use numbered todo files in `docs/todo/` for task management
- Partner app has its own CLAUDE.md with specific integration notes
- Repository migration from local to core packages is complete for partner app