name: event_app_monorepo
packages:
  - apps/**
  - packages/**

command:
  version:
    generateChangelog: true
    updateDependencies: true
    
  bootstrap:
    runPubGetInParallel: true

scripts:
  analyze:all:
    run: melos run analyze --no-select
    description: Запуск анализатора для всех пакетов

  analyze:
    run: dart analyze .
    description: Анализ текущего пакета
    packageFilters:
      dirExists: lib

  format:all:
    run: melos run format --no-select
    description: Форматирование всех пакетов

  format:
    run: dart format .
    description: Форматирование текущего пакета
    packageFilters:
      dirExists: lib

  build:all:
    run: melos run build --no-select
    description: Запуск build_runner во всех пакетах

  build:
    run: dart run build_runner build --delete-conflicting-outputs
    description: Запуск build_runner в текущем пакете
    packageFilters:
      devDependsOn: build_runner

  clean:all:
    run: melos clean && melos bootstrap
    description: Очистка и переустановка всех зависимостей

  clean:core:
    run: melos clean
    description: Очистка core пакетов
    packageFilters:
      scope: "*core*"

  dependencies:get:
    run: flutter pub get
    description: Получение зависимостей для пакета
    packageFilters:
      dirExists: lib

  version:beta:
    run: melos version --prerelease beta
    description: Обновление версий с beta тегом

  version:graduate:
    run: melos version --graduate
    description: Релиз новой стабильной версии

environment:
  sdk: ">=3.0.0 <4.0.0"
  flutter: ">=3.19.0"

ide:
  intellij:
    enabled: false

packageFilters:
  flutter:
    - packages/core_data
    - packages/core_utils
    - apps/**
  ignorePrivate: true
