import 'package:flutter/material.dart';
import 'package:core_domain/core_domain.dart';
import 'package:core_theme/core_theme.dart';
import 'package:core_ui/core_ui.dart';
import 'package:forui/forui.dart';

// *** Основная версия статус бейджа с цветным фоном
class StatusBadge extends StatelessWidget {
  const StatusBadge({super.key, required this.request});
  final Request request;

  String _getStatusText(Request request) {
    switch (request.status) {
      case RequestStatus.pending:
        return 'Ожидает подтверждения';
      case RequestStatus.accepted:
        return 'Подтверждено';
      case RequestStatus.declined:
        return 'Отклонено';
      case RequestStatus.canceled:
        if (request.cancelInitiator != null) {
          return request.cancelInitiator!.description;
        }
        return 'Отменено';
    }
  }

  Color _getStatusColor(Request request, FThemeData theme) {
    return switch (request.status) {
      RequestStatus.pending => Colors.orange,
      RequestStatus.accepted => Colors.green[800]!,
      RequestStatus.declined => theme.colors.destructive,
      RequestStatus.canceled => theme.colors.destructive,
    };
  }

  Color _getStatusBackgroundColor(Request request) {
    return switch (request.status) {
      RequestStatus.pending => const Color(0xFFFFF4E5), // Light orange
      RequestStatus.accepted => const Color(0xFFE5F4E5), // Light green
      RequestStatus.declined => const Color(0xFFFFE5E5), // Light red
      RequestStatus.canceled => const Color(0xFFFFE5E5), // Light red
    };
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final String statusText = _getStatusText(request);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
        decoration: BoxDecoration(
          color: _getStatusBackgroundColor(request),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: _getStatusColor(request, theme).withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              switch (request.status) {
                RequestStatus.pending => FIcons.clock,
                RequestStatus.accepted => FIcons.check,
                RequestStatus.declined => FIcons.x,
                RequestStatus.canceled => FIcons.x,
              },
              size: 20,
              color: _getStatusColor(request, theme),
            ),
            gapW8,
            Text(
              statusText,
              style: theme.typography.base.copyWith(
                color: _getStatusColor(request, theme),
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// *** Компактная версия для использования в деталях заявки
class StatusBadgeCompact extends StatelessWidget {
  const StatusBadgeCompact({super.key, required this.request});
  final Request request;

  String _getStatusText(Request request) {
    switch (request.status) {
      case RequestStatus.pending:
        return 'Ожидает подтверждения';
      case RequestStatus.accepted:
        return 'Подтверждено';
      case RequestStatus.declined:
        return 'Отклонено';
      case RequestStatus.canceled:
        if (request.cancelInitiator != null) {
          return request.cancelInitiator!.description;
        }
        return 'Отменено';
    }
  }

  Color _getStatusColor(Request request, FThemeData theme) {
    return switch (request.status) {
      RequestStatus.pending => Colors.orange,
      RequestStatus.accepted => Colors.green[800]!,
      RequestStatus.declined => theme.colors.destructive,
      RequestStatus.canceled => theme.colors.destructive,
    };
  }

  Color _getStatusBackgroundColor(Request request) {
    return switch (request.status) {
      RequestStatus.pending => const Color(0xFFFFF4E5), // Light orange
      RequestStatus.accepted => const Color(0xFFE5F4E5), // Light green
      RequestStatus.declined => const Color(0xFFFFE5E5), // Light red
      RequestStatus.canceled => const Color(0xFFFFE5E5), // Light red
    };
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final String statusText = _getStatusText(request);

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 12,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: _getStatusBackgroundColor(request),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            switch (request.status) {
              RequestStatus.pending => FIcons.clock,
              RequestStatus.accepted => FIcons.check,
              RequestStatus.declined => FIcons.x,
              RequestStatus.canceled => FIcons.x,
            },
            size: 16,
            color: _getStatusColor(request, theme),
          ),
          gapW4,
          Text(
            statusText,
            style: theme.typography.sm.copyWith(
              color: _getStatusColor(request, theme),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

// *** Альтернативная версия с градиентом (для будущего использования)
enum StatusBadgeStyle {
  compact, // Для списков заявок
}

class StatusBadgeGradient extends StatelessWidget {
  const StatusBadgeGradient({
    super.key,
    required this.request,
    this.style = StatusBadgeStyle.compact,
  });

  final Request request;
  final StatusBadgeStyle style;

  String _getStatusText(Request request) {
    switch (request.status) {
      case RequestStatus.pending:
        return 'Ожидает подтверждения';
      case RequestStatus.accepted:
        return 'Подтверждено';
      case RequestStatus.declined:
        return 'Отклонено';
      case RequestStatus.canceled:
        if (request.cancelInitiator != null) {
          return request.cancelInitiator!.description;
        }
        return 'Отменено';
    }
  }

  Color _getStatusColor(Request request, FThemeData theme) {
    return switch (request.status) {
      RequestStatus.pending => Colors.orange,
      RequestStatus.accepted => Colors.green[800]!,
      RequestStatus.declined => theme.colors.destructive,
      RequestStatus.canceled => theme.colors.destructive,
    };
  }

  Gradient _getStatusGradient(Request request) {
    return switch (request.status) {
      RequestStatus.pending => LinearGradient(
          colors: [Colors.orange.withValues(alpha: 0.8), Colors.orange],
        ),
      RequestStatus.accepted => LinearGradient(
          colors: [Colors.green.withValues(alpha: 0.8), Colors.green],
        ),
      RequestStatus.declined => LinearGradient(
          colors: [Colors.red.withValues(alpha: 0.8), Colors.red],
        ),
      RequestStatus.canceled => LinearGradient(
          colors: [Colors.red.withValues(alpha: 0.8), Colors.red],
        ),
    };
  }

  IconData _getStatusIcon(Request request) {
    return switch (request.status) {
      RequestStatus.pending => FIcons.clock,
      RequestStatus.accepted => FIcons.check,
      RequestStatus.declined => FIcons.x,
      RequestStatus.canceled => FIcons.x,
    };
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final statusText = _getStatusText(request);
    final statusColor = _getStatusColor(request, theme);
    final statusIcon = _getStatusIcon(request);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        gradient: _getStatusGradient(request),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: statusColor.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Icon(
            statusIcon,
            size: 18,
            color: Colors.white,
          ),
          const SizedBox(width: 10),
          Text(
            statusText,
            style: theme.typography.base.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w500,
              fontSize: 15,
            ),
          ),
        ],
      ),
    );
  }
}