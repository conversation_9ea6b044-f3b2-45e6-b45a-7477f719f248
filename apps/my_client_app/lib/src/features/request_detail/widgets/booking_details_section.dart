import 'package:core_ui/constants/app_sizes.dart';
import 'package:flutter/material.dart';
import 'package:core_domain/core_domain.dart';
import 'package:core_theme/core_theme.dart';
import 'package:core_utils/core_utils.dart';
import 'package:forui/forui.dart';
import 'status_badge.dart';

/// Секция деталей бронирования с информацией о заявке
class BookingDetailsSection extends StatelessWidget {
  const BookingDetailsSection({super.key, required this.request});
  final Request request;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Детали заявки',
            style: theme.typography.base.copyWith(
              fontWeight: FontWeight.w600,
              height: 1,
            ),
          ),
          gapH24,

          // *** Номер заявки
          _buildDetailRow(
            'Номер заявки:',
            '#${request.id.hashCode.abs() % 10000}',
            theme,
          ),
          gapH12,

          // *** Дата подачи
          _buildDetailRow(
            'Подана:',
            request.createdAt != null
                ? request.createdAt!.format('d MMMM, HH:mm')
                : request.dateStart.format('d MMMM, HH:mm'),
            theme,
          ),
          gapH12,

          // *** Количество участников
          _buildDetailRow(
            'Участников:',
            '${request.slots} ${_getParticipantsText(request.slots)}',
            theme,
          ),
          gapH12,

          // *** Сумма (TECHNICAL_DEBT: totalAmount отсутствует в модели Request)
          // TODO: Получать через Product когда добавят поле totalAmount

          // *** Статус
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                width: 140,
                child: Text(
                  'Статус:',
                  style: theme.typography.sm.copyWith(
                    color: theme.colors.mutedForeground,
                  ),
                ),
              ),
              StatusBadgeCompact(request: request),
            ],
          ),

          // *** Причина отклонения (если есть)
          if (request.status == RequestStatus.declined &&
              request.rejectReason != null) ...[
            gapH12,
            _buildDetailRow(
              'Причина:',
              request.rejectReason!,
              theme,
              isError: true,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    String label,
    String value,
    FThemeData theme, {
    bool isError = false,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 140,
          child: Text(
            label,
            style: theme.typography.sm.copyWith(
              color: theme.colors.mutedForeground,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: theme.typography.sm.copyWith(
              fontWeight: FontWeight.w500,
              color: isError ? theme.colors.destructive : null,
            ),
          ),
        ),
      ],
    );
  }

  String _getParticipantsText(int slots) {
    if (slots == 1) return 'человек';
    if (slots >= 2 && slots <= 4) return 'человека';
    return 'человек';
  }
}
