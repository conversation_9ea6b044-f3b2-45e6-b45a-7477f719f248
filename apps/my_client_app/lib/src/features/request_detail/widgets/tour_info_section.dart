import 'package:flutter/material.dart';
import 'package:core_domain/core_domain.dart';
import 'package:core_theme/core_theme.dart';
import 'package:core_utils/core_utils.dart';
import 'package:forui/forui.dart';
import 'package:core_ui/core_ui.dart';
import 'package:my_client_app/src/features/productDetail/product_detail_args.dart';
import 'package:my_client_app/src/routing/typed_routes.dart';

/// Секция информации о туре - горизонтальный макет с фото слева
class TourInfoSection extends StatelessWidget {
  const TourInfoSection({super.key, required this.request});
  final Request request;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Информация о туре',
            style: theme.typography.base.copyWith(
              fontWeight: FontWeight.w600,
              height: 1,
            ),
          ),
          gapH24,
          // *** Кликабельная карточка тура
          InkWell(
            onTap: () {
              // Создаем объект Product из данных Request для навигации
              final product = Product(
                id: request.eventID,
                name: request.eventName,
                description: '', // Request не содержит описание
                price: 0, // Request не содержит цену
                imageUrl: request.imageURL,
                slots: request.slots,
                duration:
                    1, // Временное значение, так как Request не содержит duration
                durationType: DurationType.hour, // Временное значение
                partnerRef: request.partnerID,
                partnerName: request.partnerName,
              );

              ProductDetailRouteData(
                $extra: ProductDetailArgs(
                  product: product,
                  hidePartnerField: false,
                ),
              ).push(context);
            },
            borderRadius: theme.style.borderRadius,
            child: IntrinsicHeight(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // *** Фото
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: theme.colors.muted,
                      borderRadius: theme.style.borderRadius,
                    ),
                    child: request.imageURL != null
                        ? MyNetworkImage(
                            imageUrl: request.imageURL!,
                            width: 80,
                            height: 80,
                            fit: BoxFit.cover,
                            cacheKey: 'request_tour_${request.id}',
                            borderRadius: theme.style.borderRadius,
                          )
                        : Icon(
                            FIcons.image,
                            color: theme.colors.mutedForeground,
                            size: 32,
                          ),
                  ),
                  gapW16,
                  // *** Информация
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          request.eventName,
                          style: theme.typography.lg.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        gapH4,
                        Text(
                          request.dateStart.format('d MMMM, EEE'),
                          style: theme.typography.sm.copyWith(
                            color: theme.colors.mutedForeground,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // *** Иконка навигации
                  Center(
                    child: Icon(
                      FIcons.chevronRight,
                      size: 20,
                      color:
                          theme.colors.mutedForeground.withValues(alpha: 0.5),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
