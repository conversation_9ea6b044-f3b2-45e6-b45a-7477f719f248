import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:core_domain/core_domain.dart';
import 'package:core_data/core_data.dart';
import 'package:core_theme/core_theme.dart';
import 'package:core_ui/core_ui.dart';
import 'package:forui/forui.dart';
import 'package:my_client_app/src/routing/typed_routes.dart';

/// Секция информации об организаторе
class PartnerSection extends ConsumerWidget {
  const PartnerSection({super.key, required this.request});
  final Request request;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = context.theme;
    final partnerValue = ref.watch(fetchPartnerProvider(request.partnerID));

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // *** Заголовок
          Text(
            'Организатор',
            style: theme.typography.base.copyWith(
              fontWeight: FontWeight.w600,
              height: 1,
            ),
          ),
          gapH24,

          // *** Контент
          AsyncValueWidget(
            value: partnerValue,
            data: (partner) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // *** Карточка организатора
                  InkWell(
                    onTap: () => PartnerDetailRouteData(
                      partnerID: partner.id,
                    ).push(context),
                    borderRadius: theme.style.borderRadius,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: IntrinsicHeight(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // *** Фото партнера
                            Container(
                              width: 48,
                              height: 48,
                              decoration: BoxDecoration(
                                color: theme.colors.muted,
                                shape: BoxShape.circle,
                              ),
                              child: partner.imageURL != null
                                  ? ClipOval(
                                      child: MyNetworkImage(
                                        imageUrl: partner.imageURL!,
                                        width: 48,
                                        height: 48,
                                        fit: BoxFit.cover,
                                        cacheKey: 'partner_${partner.id}',
                                      ),
                                    )
                                  : Icon(
                                      FIcons.user,
                                      color: theme.colors.mutedForeground,
                                      size: 24,
                                    ),
                            ),
                            gapW12,

                            // *** Информация
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    partner.name,
                                    style: theme.typography.base.copyWith(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  Text(
                                    '27 услуг',
                                    style: theme.typography.sm.copyWith(
                                      color: theme.colors.mutedForeground,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            // *** Иконка навигации
                            Center(
                              child: Icon(
                                FIcons.chevronRight,
                                size: 20,
                                color: theme.colors.mutedForeground
                                    .withValues(alpha: 0.5),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}