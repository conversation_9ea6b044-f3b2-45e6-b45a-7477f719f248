import 'package:flutter/material.dart';
import 'package:core_domain/core_domain.dart';
import 'package:core_theme/core_theme.dart';
import 'package:core_utils/core_utils.dart';
import 'package:core_ui/core_ui.dart';
import 'package:forui/forui.dart';

// *** История изменений с цветными точками по статусу
// Минималистичный дизайн с улучшенным spacing
class HistoryTimeline extends StatelessWidget {
  const HistoryTimeline({super.key, required this.request});
  final Request request;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'История изменений',
            style: theme.typography.base.copyWith(
              fontWeight: FontWeight.w600,
              height: 1,
            ),
          ),
          gapH24,
          // *** События
          _buildHistoryItem(
            time: request.createdAt ?? request.dateStart,
            text: 'Создана',
            color: theme.colors.mutedForeground,
            theme: theme,
          ),
          gapH16, // Увеличенный gap
          _buildHistoryItem(
            time: request.createdAt ?? request.dateStart,
            text: _getStatusChangeText(request),
            color: _getStatusColor(request, theme),
            theme: theme,
          ),
          if (request.status == RequestStatus.declined && request.rejectReason != null) ...[
            gapH16,
            _buildHistoryItem(
              time: DateTime.now(), // TODO: нужна реальная дата изменения
              text: 'Причина: ${request.rejectReason}',
              color: theme.colors.destructive,
              theme: theme,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildHistoryItem({
    required DateTime time,
    required String text,
    required Color color,
    required FThemeData theme,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center, // Центрируем вертикально
      children: [
        // *** Время с увеличенной шириной
        SizedBox(
          width: 90,
          child: Text(
            time.format('dd.MM HH:mm'),
            style: theme.typography.sm.copyWith(
              color: theme.colors.mutedForeground,
            ),
          ),
        ),
        gapW8, // Небольшой отступ после времени
        // *** Увеличенная точка
        Container(
          width: 10,
          height: 10,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        gapW12, // Отступ между точкой и текстом
        // *** Текст
        Expanded(
          child: Text(
            text,
            style: theme.typography.sm.copyWith(
              color: theme.colors.foreground,
            ),
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(Request request, FThemeData theme) {
    return switch (request.status) {
      RequestStatus.pending => Colors.orange,
      RequestStatus.accepted => Colors.green[800]!,
      RequestStatus.declined => theme.colors.destructive,
      RequestStatus.canceled => theme.colors.destructive,
    };
  }

  String _getStatusChangeText(Request request) {
    switch (request.status) {
      case RequestStatus.pending:
        return 'Ожидание подтверждения';
      case RequestStatus.accepted:
        return 'Подтверждена организатором';
      case RequestStatus.declined:
        return 'Отклонена организатором';
      case RequestStatus.canceled:
        if (request.cancelInitiator != null) {
          switch (request.cancelInitiator!) {
            case CancelInitiator.client:
              return 'Отменена вами';
            case CancelInitiator.partner:
              return 'Отменена организатором';
            case CancelInitiator.system:
              return 'Отменена системой';
          }
        }
        return 'Отменена';
    }
  }
}