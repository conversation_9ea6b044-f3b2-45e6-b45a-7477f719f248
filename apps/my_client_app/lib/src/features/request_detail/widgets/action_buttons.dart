import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:core_domain/core_domain.dart';
import 'package:core_theme/core_theme.dart';
import 'package:core_ui/core_ui.dart';
import 'package:forui/forui.dart';
import '../../requests/controller/request_controller.dart';

/// Секция кнопок действий для заявки
class ActionButtons extends ConsumerWidget {
  const ActionButtons({super.key, required this.request});
  final Request request;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = context.theme;

    // Watch controller state for loading indication
    final controllerState =
        ref.watch(requestControllerProvider(RequestControllerID.detail));
    final isLoading = controllerState.isLoading;

    // Listen to controller state changes for user feedback
    ref.listen(requestControllerProvider(RequestControllerID.detail),
        (prev, next) {
      next.whenOrNull(
        data: (action) {
          // Показываем снэкбар только для отмены, не для удаления
          // (при удалении происходит навигация и контекст уничтожается)
          if (action == RequestAction.cancel) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Заявка отменена')),
            );
          }
        },
        error: (error, stackTrace) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Произошла ошибка: $error')),
          );
        },
      );
    });

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colors.background,
        border: Border(
          top: BorderSide(
            color: theme.colors.border,
            width: 1,
          ),
        ),
      ),
      child: _buildButtonsForStatus(
          context, request.status, ref, isLoading, theme),
    );
  }

  Widget _buildButtonsForStatus(BuildContext context, RequestStatus status,
      WidgetRef ref, bool isLoading, FThemeData theme) {
    switch (status) {
      case RequestStatus.pending:
        // Заявка ожидает подтверждения
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            FButton(
              onPress: isLoading
                  ? null
                  : () {
                      _showCancelDialog(context, ref);
                    },
              style: FButtonStyle.destructive,
              child: isLoading
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: theme.colors.destructiveForeground,
                          ),
                        ),
                        gapW8,
                        Text('Отменяется...'),
                      ],
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(FIcons.x, size: 16),
                        gapW8,
                        Text('Отменить заявку'),
                      ],
                    ),
            ),
          ],
        );

      case RequestStatus.accepted:
        // Заявка подтверждена
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            FButton(
              onPress: isLoading
                  ? null
                  : () {
                      _showCancelDialog(context, ref);
                    },
              style: FButtonStyle.destructive,
              child: isLoading
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: theme.colors.destructiveForeground,
                          ),
                        ),
                        gapW8,
                        Text('Отменяется...'),
                      ],
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(FIcons.x, size: 16),
                        gapW8,
                        Text('Отменить'),
                      ],
                    ),
            ),
          ],
        );

      case RequestStatus.declined:
        // Заявка отклонена
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            FButton(
              onPress: isLoading
                  ? null
                  : () {
                      _showDeleteDialog(context, ref);
                    },
              style: FButtonStyle.destructive,
              child: isLoading
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: theme.colors.destructiveForeground,
                          ),
                        ),
                        gapW8,
                        Text('Удаляется...'),
                      ],
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(FIcons.trash2, size: 16),
                        gapW8,
                        Text('Удалить заявку'),
                      ],
                    ),
            ),
          ],
        );

      case RequestStatus.canceled:
        // Заявка отменена
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            FButton(
              onPress: isLoading
                  ? null
                  : () {
                      _showDeleteDialog(context, ref);
                    },
              style: FButtonStyle.destructive,
              child: isLoading
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: theme.colors.destructiveForeground,
                          ),
                        ),
                        gapW8,
                        Text('Удаляется...'),
                      ],
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(FIcons.trash2, size: 16),
                        gapW8,
                        Text('Удалить заявку'),
                      ],
                    ),
            ),
          ],
        );
    }
  }

  void _showCancelDialog(BuildContext context, WidgetRef ref) {
    showAdaptiveDialog(
      context: context,
      barrierDismissible: false, // Prevent dismissing during loading
      builder: (context) => Consumer(
        builder: (context, consumerRef, child) {
          final controllerState = consumerRef
              .watch(requestControllerProvider(RequestControllerID.detail));
          final isLoading = controllerState.isLoading;

          return FDialog(
            title: Text('Отменить заявку'),
            body: Text('Вы уверены, что хотите отменить эту заявку?'),
            actions: [
              FButton(
                style: FButtonStyle.outline,
                onPress: isLoading ? null : () => Navigator.of(context).pop(),
                child: Text('Нет'),
              ),
              FButton(
                style: FButtonStyle.destructive,
                onPress: isLoading
                    ? null
                    : () {
                        Navigator.of(context).pop();
                        _cancelRequest(context, ref);
                      },
                child: Text('Да, отменить'),
              ),
            ],
          );
        },
      ),
    );
  }

  void _cancelRequest(BuildContext context, WidgetRef ref) {
    ref
        .read(requestControllerProvider(RequestControllerID.detail).notifier)
        .cancelRequest(request);
  }

  void _showDeleteDialog(BuildContext context, WidgetRef ref) {
    showAdaptiveDialog(
      context: context,
      barrierDismissible: false, // Prevent dismissing during loading
      builder: (context) => Consumer(
        builder: (context, consumerRef, child) {
          final controllerState = consumerRef
              .watch(requestControllerProvider(RequestControllerID.detail));
          final isLoading = controllerState.isLoading;

          return FDialog(
            title: Text('Удалить заявку'),
            body: Text('Вы уверены, что хотите удалить эту заявку?'),
            actions: [
              FButton(
                style: FButtonStyle.outline,
                onPress: isLoading ? null : () => Navigator.of(context).pop(),
                child: Text('Отмена'),
              ),
              FButton(
                style: FButtonStyle.destructive,
                onPress: isLoading
                    ? null
                    : () {
                        Navigator.of(context).pop();
                        _deleteRequest(context, ref);
                      },
                child: Text('Удалить'),
              ),
            ],
          );
        },
      ),
    );
  }

  void _deleteRequest(BuildContext context, WidgetRef ref) {
    ref
        .read(requestControllerProvider(RequestControllerID.detail).notifier)
        .deleteRequest(request, goBack: true);
  }
}
