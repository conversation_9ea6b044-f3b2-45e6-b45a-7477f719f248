import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:my_client_app/src/routing/typed_routes.dart';
import 'package:core_domain/core_domain.dart';
import 'package:core_data/core_data.dart';
import 'package:core_ui/core_ui.dart';
import 'package:core_theme/core_theme.dart';
import 'package:forui/forui.dart';
import 'widgets/widgets.dart';

class RequestDetailScreen extends ConsumerWidget {
  const RequestDetailScreen({super.key, required this.request1});
  final Request request1;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = context.theme;

    // *** Providers (используем те же, что и в старой версии)
    final requestValue = ref.watch(requestStreamProvider(request1.id));

    // Автоматическая навигация при удалении заявки
    ref.listen(requestStreamProvider(request1.id), (previous, next) {
      next.whenOrNull(
        data: (request) {
          if (request == null && context.mounted) {
            // Заявка удалена, вернуться назад
            if (context.canPop()) {
              context.pop();
            } else {
              // Если не можем вернуться, идем на экран заявок
              const RequestRouteData().go(context);
            }
          }
        },
      );
    });

    return AsyncValueWidget(
      value: requestValue,
      data: (currentRequest) {
        // Проверяем, не удалена ли заявка
        if (currentRequest == null) {
          // Показываем индикатор загрузки пока идет навигация
          return Scaffold(
            backgroundColor: theme.colors.background,
            body: Center(child: CircularProgressIndicator()),
          );
        }
        return Scaffold(
          backgroundColor: theme.colors.background,
          appBar: AppBar(
            backgroundColor: theme.colors.background,
            elevation: 0,
            leading: IconButton(
              onPressed: () {
                // Проверяем, можем ли мы вернуться назад
                if (context.canPop()) {
                  context.pop();
                } else {
                  // Если не можем вернуться, идем на экран заявок
                  const RequestRouteData().go(context);
                }
              },
              icon: Icon(
                FIcons.chevronLeft,
                color: theme.colors.foreground,
              ),
            ),
            title: Text(
              'Детали заявки',
              style: theme.typography.lg.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            centerTitle: true,
          ),
          body: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // *** Status Badge
                      StatusBadge(request: currentRequest),

                      SDivider(
                        thickness: 10,
                        color: theme.colors.muted,
                        verticalPadding: 30,
                      ),

                      // *** Tour Info Section,

                      TourInfoSection(request: currentRequest),

                      SDivider(
                        thickness: 10,
                        color: theme.colors.muted,
                        verticalPadding: 30,
                      ),

                      // *** Booking Details Section
                      BookingDetailsSection(request: currentRequest),

                      SDivider(
                        thickness: 10,
                        color: theme.colors.muted,
                        verticalPadding: 30,
                      ),

                      // *** Partner Section
                      PartnerSection(request: currentRequest),

                      SDivider(
                        thickness: 10,
                        color: theme.colors.muted,
                        verticalPadding: 30,
                      ),

                      // *** History Timeline
                      HistoryTimeline(request: currentRequest),
                      
                      gapH48,
                    ],
                  ),
                ),
              ),

              // *** Action Buttons Section
              ActionButtons(request: currentRequest),
            ],
          ),
        );
      },
    );
  }
}
