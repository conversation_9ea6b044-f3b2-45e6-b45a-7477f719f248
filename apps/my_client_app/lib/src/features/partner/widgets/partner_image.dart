import 'package:core_ui/widgets/common/images/my_network_image.dart';
import 'package:flutter/material.dart';
import 'package:core_domain/core_domain.dart';

class PartnerImage extends StatelessWidget {
  const PartnerImage({super.key, required this.partner});
  final Partner partner;

  @override
  Widget build(BuildContext context) {
    return Align(
        alignment: Alignment.center,
        child: MyNetworkImage(
          imageUrl: partner.imageURL ?? '',
          height: 150,
          width: 150,
          borderRadius: BorderRadius.circular(100),
        ));
  }
}
