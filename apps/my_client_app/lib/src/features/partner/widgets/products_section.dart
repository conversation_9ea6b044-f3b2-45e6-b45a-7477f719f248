import 'package:core_theme/core_theme.dart';
import 'package:core_ui/constants/app_sizes.dart';
import 'package:core_ui/widgets/common/async_value_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:core_domain/core_domain.dart';
import 'package:core_data/core_data.dart';
import 'package:core_ui/widgets/product_card/product_card.dart';
import 'package:my_client_app/src/features/productDetail/product_detail_args.dart';
import 'package:my_client_app/src/routing/typed_routes.dart';

class ProductsSection extends ConsumerWidget {
  const ProductsSection({
    super.key,
    required this.serviceTypeListID,
    required this.partner,
    this.fromPartnerDetail = false,
  });
  final List<String> serviceTypeListID;
  final Partner partner;
  final bool fromPartnerDetail;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final productListValue =
        ref.watch(productListPartnerFutureProvider(partner.id));
    final theme = context.theme;

    // *** Widget
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Text(
            'Услуги',
            style: theme.typography.lg,
          ),
        ),
        gapH16,
        AsyncValueWidget(
            value: productListValue,
            data: (productList) {
              return productList.isNotEmpty
                  ? SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Padding(
                        padding: const EdgeInsets.only(left: 20, bottom: 20),
                        child: Row(
                          children: [
                            ...List.generate(productList.length, (index) {
                              return Padding(
                                padding: const EdgeInsets.only(right: 20),
                                child: ProductCard(
                                  product: productList[index],
                                  fromPartnerDetail: fromPartnerDetail,
                                  onCardTap: () => ProductDetailRouteData(
                                    $extra: ProductDetailArgs(
                                        hidePartnerField: true,
                                        product: productList[index]),
                                  ).push(context),
                                ),
                              );
                            })
                          ],
                        ),
                      ),
                    )
                  : Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Center(
                        child: FCard(
                          //description: Center(child: Icon(LucideIcons.info)),
                          child: Text("У  партнера пока нет доступных услуг.",
                              style: theme.typography.xl.copyWith(
                                color: theme.colors.mutedForeground,
                              ),
                              textAlign: TextAlign.center),
                        ),
                      ),
                    );
            }),
      ],
    );
  }
}
