import 'package:core_theme/core_theme.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

/// Карточка для отображения контактной информации партнера в минималистичном стиле Airbnb.
///
/// Особенности:
/// - Чистый, минималистичный дизайн
/// - Отображает иконку и значение
/// - Поддерживает действия при нажатии и долгом нажатии
class PartnerContactCard extends StatelessWidget {
  final IconData icon;
  final String value;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final bool showArrow;

  const PartnerContactCard({
    super.key,
    required this.icon,
    required this.value,
    this.onTap,
    this.onLongPress,
    this.showArrow = true,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = context.theme.typography;

    return InkWell(
      onTap: onTap,
      onLongPress: onLongPress,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.symmetric(
            vertical: 4, horizontal: 0), // Увеличенные отступы
        child: Row(
          children: [
            Icon(
              icon,
              size: 18, // Немного увеличен размер
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                value,
                style: textTheme.base,
                // style: textTheme.bodyMedium?.copyWith(
                //   fontWeight: FontWeight.w400,
                // ),
              ),
            ),
            if (showArrow)
              Icon(
                Icons.chevron_right,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  /// Форматирует телефонный номер для лучшей читаемости
  static String formatPhoneNumber(String phoneNumber) {
    // Простое форматирование для казахстанских номеров
    if (phoneNumber.startsWith('+7') && phoneNumber.length == 12) {
      return '${phoneNumber.substring(0, 2)} (${phoneNumber.substring(2, 5)}) ${phoneNumber.substring(5, 8)}-${phoneNumber.substring(8, 10)}-${phoneNumber.substring(10, 12)}';
    }
    return phoneNumber;
  }

  /// Создает карточку для телефонного номера с возможностью звонка
  static PartnerContactCard phone({
    required String phoneNumber,
    required BuildContext context,
  }) {
    return PartnerContactCard(
        icon: FIcons.phone,
        value: formatPhoneNumber(phoneNumber),
        onTap: () => _launchURL('tel:$phoneNumber'),
        onLongPress: () => _copyToClipboard(context, phoneNumber));
  }

  /// Создает карточку для Instagram с возможностью перехода
  static PartnerContactCard instagram({
    required String? instagramHandle,
    required BuildContext context,
  }) {
    final handle = instagramHandle ?? '';
    // Добавляем символ @ перед именем пользователя для лучшей узнаваемости
    final displayHandle = handle.isNotEmpty ? '@$handle' : '';

    return PartnerContactCard(
        icon: FontAwesomeIcons.instagram,
        value: displayHandle,
        onTap: () => _launchURL('https://instagram.com/$handle'),
        onLongPress: () => _copyToClipboard(context, handle));
  }

  /// Создает карточку для адреса (без возможности открытия карты)
  static PartnerContactCard address({
    required String? address,
    required BuildContext context,
  }) {
    final addressValue = address ?? '';

    return PartnerContactCard(
      icon: Icons.location_on_outlined,
      value: addressValue,
      showArrow: false,
      onLongPress: () => _copyToClipboard(context, addressValue),
    );
  }

  /// Создает карточку для описания
  static PartnerContactCard about({
    required String? about,
    required BuildContext context,
  }) {
    final aboutValue = about ?? '';

    return PartnerContactCard(
      icon: Icons.info_outline,
      value: aboutValue,
      showArrow: false,
    );
  }
}

/// Открывает URL в браузере или соответствующем приложении
Future<void> _launchURL(String url) async {
  final uri = Uri.parse(url);
  if (await canLaunchUrl(uri)) {
    // Выбираем режим запуска в зависимости от типа ссылки
    if (url.startsWith('tel:')) {
      // Для телефонных ссылок используем внешнее приложение
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else if (url.contains('instagram.com')) {
      // Для Instagram тоже используем внешнее приложение, если возможно
      try {
        // Сначала пробуем открыть в приложении Instagram
        final instagramUri =
            Uri.parse('instagram://user?username=${uri.pathSegments.last}');
        if (await canLaunchUrl(instagramUri)) {
          await launchUrl(instagramUri, mode: LaunchMode.externalApplication);
        } else {
          // Если не получилось, открываем в браузере
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        }
      } catch (e) {
        // В случае ошибки открываем в браузере
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      }
    } else {
      // Для других ссылок используем стандартный режим
      await launchUrl(uri, mode: LaunchMode.platformDefault);
    }
  }
}

/// Копирует текст в буфер обмена и показывает уведомление
void _copyToClipboard(BuildContext context, String text) {
  Clipboard.setData(ClipboardData(text: text));
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text('Скопировано: $text'),
      duration: Duration(seconds: 2),
    ),
  );
}
