import 'package:core_theme/core_theme.dart';
import 'package:core_ui/constants/app_sizes.dart';
import 'package:flutter/material.dart';
import 'package:core_domain/core_domain.dart';
import 'package:my_client_app/src/features/partner/widgets/partner_image.dart';

/// Enhanced partner header section with better visual hierarchy
class PartnerHeaderSection extends StatelessWidget {
  final Partner partner;

  const PartnerHeaderSection({
    super.key,
    required this.partner,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return Column(
      children: [
        // Partner image with better spacing
        PartnerImage(partner: partner),
        gapH24,

        // Partner name with improved typography
        Text(
          partner.name,
          style: theme.typography.xl2.copyWith(
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
        ),

        // Product count as subtitle
        if (partner.productRefList.isNotEmpty) ...[
          gapH8,
          Text(
            '${partner.productRefList.length} категории услуг',
            style: theme.typography.sm.copyWith(
              color: theme.colors.mutedForeground,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}
