import 'package:core_theme/core_theme.dart';
import 'package:core_ui/constants/app_sizes.dart';
import 'package:core_ui/widgets/common/async_value_widget.dart';
import 'package:core_ui/widgets/product_card/product_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:core_domain/core_domain.dart';
import 'package:core_data/core_data.dart';
import 'package:my_client_app/src/features/productDetail/product_detail_args.dart';
import 'package:my_client_app/src/routing/typed_routes.dart';

/// Enhanced products section with better layout and visual hierarchy
class ProductsSectionEnhanced extends ConsumerWidget {
  const ProductsSectionEnhanced({
    super.key,
    required this.serviceTypeListID,
    required this.partner,
    this.fromPartnerDetail = false,
  });

  final List<String> serviceTypeListID;
  final Partner partner;
  final bool fromPartnerDetail;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final productListValue =
        ref.watch(productListPartnerFutureProvider(partner.id));
    final theme = context.theme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header with count
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Услуги',
                style: theme.typography.lg.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              AsyncValueWidget(
                value: productListValue,
                data: (productList) => productList.isNotEmpty
                    ? Text(
                        '${productList.length}',
                        style: theme.typography.sm.copyWith(
                          color: theme.colors.mutedForeground,
                        ),
                      )
                    : SizedBox.shrink(),
              ),
            ],
          ),
        ),
        gapH20,

        // Products list
        AsyncValueWidget(
          value: productListValue,
          data: (productList) {
            if (productList.isEmpty) {
              return _ProductsEmptyState();
            }

            // Vertical list layout
            return ListView.builder(
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              padding: const EdgeInsets.symmetric(horizontal: 20),
              itemCount: productList.length,
              itemBuilder: (context, index) {
                final product = productList[index];
                return Padding(
                  padding: EdgeInsets.only(
                    bottom: index < productList.length - 1 ? 16 : 0,
                  ),
                  child: ProductCard(
                    product: product,
                    fromPartnerDetail: fromPartnerDetail,
                    onCardTap: () => ProductDetailRouteData(
                      $extra: ProductDetailArgs(
                        hidePartnerField: true,
                        product: product,
                      ),
                    ).push(context),
                  ),
                );
              },
            );
          },
        ),

        // Bottom spacing
        gapH32,
      ],
    );
  }
}

/// Empty state widget for when partner has no products
class _ProductsEmptyState extends StatelessWidget {
  const _ProductsEmptyState();

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: FCard(
        style: theme.cardStyle.copyWith(
          contentStyle: theme.cardStyle.contentStyle.copyWith(
            padding: EdgeInsets.all(32),
          ),
        ),
        child: Column(
          children: [
            Icon(
              FIcons.package2,
              size: 48,
              color: theme.colors.mutedForeground.withValues(alpha: 0.5),
            ),
            gapH16,
            Text(
              'У партнера пока нет доступных услуг',
              style: theme.typography.base.copyWith(
                color: theme.colors.mutedForeground,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            gapH8,
            Text(
              'Возможно, они появятся позже',
              style: theme.typography.sm.copyWith(
                color: theme.colors.mutedForeground.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
