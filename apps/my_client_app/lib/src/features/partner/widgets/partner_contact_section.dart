import 'package:core_theme/core_theme.dart';
import 'package:core_ui/constants/app_sizes.dart';
import 'package:flutter/material.dart';
import 'package:core_domain/core_domain.dart';
import 'partner_contact_card.dart';

/// Секция с контактной информацией партнера в минималистичном стиле Airbnb
class PartnerContactSection extends StatelessWidget {
  final Partner partner;

  const PartnerContactSection({
    super.key,
    required this.partner,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    return Center(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Заголовок секции удален, чтобы избежать дублирования
          Text(
            'Контактная информация',
            style: theme.typography.lg,
          ),
          gapH16,

          // Телефон (всегда отображается)
          PartnerContactCard.phone(
            phoneNumber: partner.phoneNumber,
            context: context,
          ),

          // Разделитель после телефона
          // _buildDivider(),

          // Instagram (отображается только если есть)
          if (partner.instagramm != null && partner.instagramm!.isNotEmpty) ...[
            PartnerContactCard.instagram(
              instagramHandle: partner.instagramm,
              context: context,
            ),
            // _buildDivider(),
          ],

          // Адрес (отображается только если есть)
          if (partner.address != null && partner.address!.isNotEmpty) ...[
            PartnerContactCard.address(
              address: partner.address,
              context: context,
            ),
            // _buildDivider(),
          ],

          // Описание (отображается только если есть)
          if (partner.about != null && partner.about!.isNotEmpty)
            PartnerContactCard.about(
              about: partner.about,
              context: context,
            ),
        ],
      ),
    );
  }
}
