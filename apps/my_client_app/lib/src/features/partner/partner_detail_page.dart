import 'package:core_theme/core_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:my_client_app/src/features/partner/controller/partner_detail_controller.dart';
import 'package:my_client_app/src/features/partner/widgets/partner_header_section.dart';
import 'package:my_client_app/src/features/partner/widgets/partner_contact_section_enhanced.dart';
import 'package:my_client_app/src/features/partner/widgets/products_section_enhanced.dart';
import 'package:core_data/core_data.dart';
import 'package:core_ui/core_ui.dart';

class PartnerDetailPage extends ConsumerWidget {
  const PartnerDetailPage({super.key, required this.partnerID});
  final String partnerID;
  // Константы для отступов
  static const labelH = gapH32;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final partnerValue = ref.watch(fetchPartnerProvider(partnerID));
    final clientValue = ref.watch(watchClientProvider);
    final theme = context.theme;
    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colors.background,
        elevation: 0,
        actions: [
          AsyncValueWidget(
              value: clientValue,
              data: (client) {
                final isFavorite =
                    client.favoritePartnerIDList.contains(partnerID);
                return IconButton(
                  icon: Icon(
                    isFavorite ? Icons.favorite : Icons.favorite_border,
                    color:
                        isFavorite ? Colors.red : theme.colors.mutedForeground,
                  ),
                  onPressed: () {
                    ref
                        .read(partnerDetailControllerProvider.notifier)
                        .addpartnerToFavorite(partnerID);
                  },
                );
              }),
        ],
      ),
      body: AsyncValueWidget(
        value: partnerValue,
        data: (partner) {
          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Enhanced header section with partner info
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: PartnerHeaderSection(partner: partner),
                ),

                // Elegant divider
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Divider(
                    color: theme.colors.border,
                    thickness: 1,
                  ),
                ),
                gapH32,

                // Enhanced contact information section
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: PartnerContactSectionEnhanced(
                    partner: partner.copyWith(
                      about:
                          'Наша компания специализируется на организации и проведении мероприятий любого масштаба и формата. Мы предоставляем полный спектр услуг от концепции до реализации: разработка сценария, техническое обеспечение, кейтеринг, декорирование, развлекательная программа. За более чем 10 лет работы мы организовали свыше 500 успешных мероприятий, включая корпоративные праздники, свадьбы, дни рождения, конференции и выставки. Наша команда состоит из опытных профессионалов, которые внимательно относятся к каждой детали и всегда стремятся превзойти ожидания клиентов. Мы гордимся индивидуальным подходом к каждому проекту и гарантируем высокое качество услуг.',
                    ),
                  ),
                ),

                // Another divider
                Padding(
                  padding: const EdgeInsets.fromLTRB(20, 32, 20, 32),
                  child: Divider(
                    color: theme.colors.border,
                    thickness: 1,
                  ),
                ),

                // Enhanced products section
                ProductsSectionEnhanced(
                  serviceTypeListID: partner.productRefList,
                  partner: partner,
                  fromPartnerDetail: true,
                ),

                // Bottom padding for better scroll experience
                gapH64,
              ],
            ),
          );
        },
      ),
    );
  }
}
