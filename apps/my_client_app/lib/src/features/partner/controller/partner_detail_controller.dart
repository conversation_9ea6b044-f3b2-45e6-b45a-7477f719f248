import 'package:core_utils/core_utils.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:core_data/core_data.dart';
part 'partner_detail_controller.g.dart';

@riverpod
class PartnerDetailController extends _$PartnerDetailController
    with NotifierMounted {
  @override
  FutureOr<void> build() {
    ref.onDispose(setUnmounted);
  }

  Future<void> addpartnerToFavorite(String partnerID) async {
    state = AsyncLoading();
    final client = await ref.read(watchClientProvider.future);

    final List<String> updatedList = List.from(client.favoritePartnerIDList);

    if (updatedList.contains(partnerID)) {
      updatedList.remove(partnerID);
    } else {
      updatedList.add(partnerID);
    }

    await ref
        .read(firebaseClientRepoProvider)
        .updateClient(client.copyWith(favoritePartnerIDList: updatedList));
  }
}
