import 'package:core_ui/core_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:my_client_app/src/features/authentication/controller/authentication_controller.dart';
import 'package:my_client_app/src/features/authentication/controller/authentication_state.dart';
import 'package:my_client_app/src/features/authentication/my_pinput.dart';

class OTPVerifyScreen extends ConsumerStatefulWidget {
  const OTPVerifyScreen({super.key});

  @override
  ConsumerState<OTPVerifyScreen> createState() => _OTPVerifyScreenState();
}

class _OTPVerifyScreenState extends ConsumerState<OTPVerifyScreen> {
  late final TextEditingController _pinController;

  @override
  void initState() {
    super.initState();
    _pinController = TextEditingController();
  }

  @override
  void dispose() {
    _pinController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
            onPressed: () {
              ref.read(authenticationControllerProvider.notifier).resetState();
              context.pop();
            },
            icon: Icon(Icons.arrow_back_outlined)),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Введите код',
              style: TextStyle(fontSize: 36, fontWeight: FontWeight.bold),
            ),
            PhoneNumberDisplay(),
            gapH32,
            MyPinPut(
              pinController: _pinController,
              onCompleted: (pin) {
                ref
                    .read(authenticationControllerProvider.notifier)
                    .verifyOTP(pin);
              },
            ),
            gapH32,
            VerifyButton(pinController: _pinController),
            gapH16,
            ResendTimer(
              onResendRequested: () {
                _pinController.clear();
              },
            ),
          ],
        ),
      ),
    );
  }
}

class PhoneNumberDisplay extends ConsumerWidget {
  const PhoneNumberDisplay({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final phoneNumber = ref.watch(
        authenticationControllerProvider.select((state) => state.phoneNumber));
    return Text(
      'Мы отправили смс с кодом активации на номер  \n$phoneNumber',
    );
  }
}

class VerifyButton extends ConsumerWidget {
  const VerifyButton({
    super.key,
    required this.pinController,
  });

  final TextEditingController pinController;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isLoading = ref.watch(authenticationControllerProvider
        .select((state) => state.status == StateStatus.loading));

    return PrimaryButton(
      text: 'Проверить код',
      isLoading: isLoading,
      onPressed: () {
        if (pinController.text.length != 6) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Пожалуйста, введите 6-значный код'),
            ),
          );
          return;
        }

        ref
            .read(authenticationControllerProvider.notifier)
            .verifyOTP(pinController.text);
      },
    );
  }
}

class ResendTimer extends ConsumerWidget {
  const ResendTimer({
    super.key,
    required this.onResendRequested,
  });

  final VoidCallback onResendRequested;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(authenticationControllerProvider);
    final timer = state.resendTimer;
    final isLoading = state.status == StateStatus.loading;

    return Center(
      child: timer != null
          ? Text(
              'Повторная отправка через $timer сек',
              style: TextStyle(color: Colors.grey),
            )
          : TextButton(
              onPressed: state.canResend && !isLoading
                  ? () {
                      onResendRequested(); // Вызываем callback перед запросом
                      ref
                          .read(authenticationControllerProvider.notifier)
                          .resendOTP();
                    }
                  : null,
              child: Text('Отправить код повторно'),
            ),
    );
  }
}
