import 'package:core_data/core_data.dart';
import 'package:core_domain/core_domain.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'auth_providers.g.dart';

@riverpod
FirebaseAuth firebaseAuth(Ref ref) {
  return FirebaseAuth.instance;
}

@riverpod
IClientRepository firebaseClientRepository(Ref ref) {
  return ref.watch(firebaseClientRepoProvider);
}
