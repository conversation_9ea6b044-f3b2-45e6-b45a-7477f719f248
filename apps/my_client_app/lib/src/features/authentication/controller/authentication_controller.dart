import 'dart:async';
import 'dart:developer' as dev;
import 'package:core_domain/core_domain.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:my_client_app/src/features/authentication/controller/authentication_state.dart';
import 'package:my_client_app/src/features/authentication/exceptions/phone_auth_exceptions.dart';
import 'package:my_client_app/src/features/authentication/providers/auth_providers.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
part 'authentication_controller.g.dart';

@riverpod
class AuthenticationController extends _$AuthenticationController {
  static const int RESEND_TIMEOUT = 10;
  Timer? _resendTimer;
  late final FirebaseAuth _auth;
  late final IClientRepository _clientRepo;

  @override
  AuthenticationState build() {
    _auth = ref.watch(firebaseAuthProvider);
    _clientRepo = ref.watch(firebaseClientRepositoryProvider);

    ref.onDispose(() {
      _resendTimer?.cancel();
    });
    return AuthenticationState();
  }

  void resetState() {
    _resendTimer?.cancel();
    state = AuthenticationState();
  }

  void startResendTimer() {
    int timeLeft = RESEND_TIMEOUT;
    state = state.copyWith(resendTimer: timeLeft, canResend: false);

    _resendTimer?.cancel();
    _resendTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      timeLeft--;
      if (timeLeft <= 0) {
        timer.cancel();
        state = state.copyWith(resendTimer: null, canResend: true);
      } else {
        state = state.copyWith(resendTimer: timeLeft);
      }
    });
  }

  Future<void> resendOTP() async {
    if (!state.canResend) return;
    await sendOTP(state.phoneNumber, isResend: true);
  }

  Future<void> sendOTP(String phoneNumber, {bool isResend = false}) async {
    // Флаг, показывающий, сработал ли уже какой-либо колбэк
    bool callbackTriggered = false;
    // Таймер для отслеживания таймаута
    Timer? timeoutTimer;

    try {
      if (!isResend) {
        state = state.copyWith(
            status: StateStatus.loading,
            phoneNumber: phoneNumber,
            errorMessage: null); // Сбрасываем предыдущую ошибку
      } else {
        state = state.copyWith(
            status: StateStatus.loading,
            errorMessage: null); // Сбрасываем предыдущую ошибку
      }

      // Устанавливаем таймаут (например, 45 секунд)
      const verificationTimeout = Duration(seconds: 45);
      timeoutTimer = Timer(verificationTimeout, () {
        // Этот код выполнится, если ни один колбэк не сработал за 45 секунд
        if (!callbackTriggered && state.status == StateStatus.loading) {
          dev.log(
              'Таймаут ожидания ответа Firebase verifyPhoneNumber (${verificationTimeout.inSeconds} сек).',
              name: 'Auth',
              error: 'Timeout');
          state = state.copyWith(
            status: StateStatus.error,
            errorMessage:
                'Не удалось отправить код подтверждения. Пожалуйста, попробуйте еще раз позже. (Таймаут)',
          );
        }
      });

      dev.log('Вызов _auth.verifyPhoneNumber для $phoneNumber',
          name: 'Auth'); // Лог перед вызовом
      await _auth.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        timeout:
            verificationTimeout, // Передаем таймаут также в Firebase (хотя наш таймер надежнее)

        codeSent: (verificationId, forceResendingToken) {
          dev.log(
              'Callback codeSent сработал. verificationId: $verificationId, token: $forceResendingToken',
              name: 'Auth');
          callbackTriggered = true; // Отмечаем, что колбэк сработал
          timeoutTimer?.cancel(); // Отменяем наш ручной таймаут
          if (state.status == StateStatus.loading) {
            // Проверяем, что мы все еще в ожидании
            state = state.copyWith(
                status: StateStatus.sentOTP, verificationId: verificationId);
            startResendTimer();
          } else {
            dev.log(
                'Callback codeSent сработал, но state уже не loading (${state.status}). Игнорируем.',
                name: 'Auth');
          }
        },

        verificationCompleted: (PhoneAuthCredential credential) async {
          dev.log('Callback verificationCompleted сработал.', name: 'Auth');
          callbackTriggered = true;
          timeoutTimer?.cancel();
          // Не меняем статус на loading здесь, чтобы не прерывать ввод кода вручную, если он уже идет
          try {
            dev.log(
                'Попытка автоматической верификации из verificationCompleted',
                name: 'Auth');
            await signInWithPhone(credential);
            // Устанавливаем loggedIn только если не было ошибки и signIn успешен
            if (state.status != StateStatus.error) {
              state = state.copyWith(status: StateStatus.loggedIn);
              dev.log(
                  'Автоматическая верификация успешна (verificationCompleted)',
                  name: 'Auth');
            }
          } catch (e) {
            dev.log(
                'Ошибка автоматической верификации (verificationCompleted): ${e.toString()}',
                name: 'Auth',
                error: e);
            // Не устанавливаем глобальную ошибку, чтобы не мешать ручному вводу
          }
        },

        verificationFailed: (FirebaseAuthException error) {
          dev.log(
              'Callback verificationFailed сработал. Code: ${error.code}, Message: ${error.message}',
              name: 'Auth',
              error: error);
          callbackTriggered = true;
          timeoutTimer?.cancel();
          if (state.status == StateStatus.loading) {
            // Проверяем, что мы все еще в ожидании
            state = state.copyWith(
                status: StateStatus.error,
                // Используем более детальную обработку ошибок, которая уже есть в verifyOTP
                errorMessage: _handleFirebaseAuthError(error,
                    defaultMessage: 'Ошибка при отправке кода подтверждения.'));
          } else {
            dev.log(
                'Callback verificationFailed сработал, но state уже не loading (${state.status}). Игнорируем.',
                name: 'Auth');
          }
        },

        codeAutoRetrievalTimeout: (String verificationId) {
          dev.log(
              'Callback codeAutoRetrievalTimeout сработал. verificationId: $verificationId',
              name: 'Auth');
          // Этот колбэк не означает ошибку, просто автоматическое чтение SMS не удалось/завершилось по таймауту.
          // Не меняем состояние здесь, если код уже отправлен (`codeSent` сработал).
          // Если codeSent не сработал, наш timeoutTimer обработает ошибку.
          // Можно сохранить verificationId, если он еще не установлен:
          if (state.verificationId == null &&
              state.status == StateStatus.loading) {
            dev.log(
                'Сохраняем verificationId из codeAutoRetrievalTimeout, т.к. codeSent еще не сработал.',
                name: 'Auth');
            // Важно: Не меняем статус на sentOTP здесь! Только сохраняем ID.
            // state = state.copyWith(verificationId: verificationId); // <-- Раскомментируй, если нужно сохранять ID даже без codeSent
          }
        },
      );
      dev.log('Вызов _auth.verifyPhoneNumber завершен (ожидание колбэков)',
          name: 'Auth');
    } catch (e) {
      // Ловим ошибки самого вызова verifyPhoneNumber, если они есть
      dev.log(
          'Неожиданная ошибка при вызове verifyPhoneNumber: ${e.toString()}',
          name: 'Auth',
          error: e);
      callbackTriggered = true; // Считаем, что обработка завершена (ошибкой)
      timeoutTimer?.cancel();
      // Устанавливаем ошибку, только если мы все еще в состоянии загрузки
      if (state.status == StateStatus.loading) {
        state = state.copyWith(
            status: StateStatus.error,
            errorMessage: 'Произошла неожиданная ошибка при запросе кода.');
      }
    }
  }

  // Вспомогательный метод для обработки ошибок FirebaseAuth
  String _handleFirebaseAuthError(FirebaseAuthException e,
      {String defaultMessage = 'Произошла ошибка Firebase.'}) {
    return switch (e.code) {
      'invalid-phone-number' => 'Неверный формат номера телефона.',
      'too-many-requests' =>
        'Слишком много запросов. Пожалуйста, попробуйте позже.',
      'network-request-failed' =>
        'Ошибка сети. Проверьте подключение к интернету.',
      'app-not-authorized' =>
        'Приложение не авторизовано для использования Phone Auth.',
      'quota-exceeded' =>
        'Превышена квота на отправку SMS.', // Редко для тестов
      'operation-not-allowed' =>
        'Вход по номеру телефона не включен в Firebase.',
      'invalid-verification-code' =>
        'Неверный код подтверждения. Пожалуйста, попробуйте еще раз.',
      'session-expired' =>
        'Срок действия кода истек. Отправьте код повторно.', // или 'expired-action-code'
      'invalid-verification-id' =>
        'Недействительный ID верификации. Попробуйте отправить код еще раз.',
      'user-disabled' => 'Аккаунт пользователя отключен.',
      'user-not-found' => 'Пользователь не найден.',
      'invalid-credential' => 'Неверные учетные данные.',
      'account-exists-with-different-credential' =>
        'Учетная запись уже существует с другим способом входа.',
      _ => e.message ?? defaultMessage
    };
  }

  Future<void> verifyOTP(String smsCode) async {
    try {
      dev.log('Попытка верификации OTP кода', name: 'Auth');
      state = state.copyWith(
          status: StateStatus.loading, errorMessage: null); // Сброс ошибки

      // Проверка наличия verificationId
      if (state.verificationId == null) {
        throw Exception(
            "Отсутствует ID верификации. Попробуйте отправить код повторно.");
      }

      // Проверка формата кода
      if (smsCode.length != 6) {
        throw Exception("Код должен содержать 6 цифр.");
      }

      PhoneAuthCredential credential = PhoneAuthProvider.credential(
          verificationId: state.verificationId!, smsCode: smsCode);
      await signInWithPhone(credential);

      // Проверяем, не установил ли signInWithPhone ошибку
      if (state.status != StateStatus.error) {
        state = state.copyWith(status: StateStatus.loggedIn);
        dev.log('OTP код успешно верифицирован', name: 'Auth');
      }
    } on FirebaseAuthException catch (e) {
      // Ловим ошибки верификации
      String errorMessage = _handleFirebaseAuthError(e,
          defaultMessage: 'Ошибка при проверке кода.');
      dev.log('Ошибка при верификации OTP: $errorMessage',
          name: 'Auth', error: e);
      state =
          state.copyWith(status: StateStatus.error, errorMessage: errorMessage);
    } catch (e) {
      // Ловим другие ошибки (например, из Exception в начале)
      dev.log('Ошибка при верификации OTP: ${e.toString()}',
          name: 'Auth', error: e);
      state =
          state.copyWith(status: StateStatus.error, errorMessage: e.toString());
    }
  }

  Future<void> signInWithPhone(PhoneAuthCredential credential) async {
    try {
      UserCredential userCredential =
          await _auth.signInWithCredential(credential);
      if (userCredential.user != null) {
        String id = userCredential.user!.uid;
        String? phoneNumber = userCredential.user!.phoneNumber;

        dev.log('Пользователь успешно авторизован: $id', name: 'Auth');

        dev.log('Проверка наличия номера телефона для пользователя: $id',
            name: 'Auth');
        if (phoneNumber == null) {
          dev.log(
              'Номер телефона отсутствует. Попытка использовать номер из state',
              name: 'Auth',
              error: 'Phone number is null');

          // Пробуем использовать номер из state
          if (state.phoneNumber.isNotEmpty) {
            phoneNumber = state.phoneNumber;
            dev.log('Используем номер телефона из state: $phoneNumber',
                name: 'Auth');
          } else {
            dev.log('Номер телефона недоступен ни из Firebase, ни из state',
                name: 'Auth', error: 'No phone number available');
            throw PhoneNumberNotAvailableException();
          }
        }

        final docsnapshot = await _clientRepo.getDocSnapshot(id);
        if (docsnapshot.exists == false) {
          dev.log('Создание нового профиля пользователя: $id', name: 'Auth');
          final client = Client(id: id, phoneNumber: phoneNumber);
          await _clientRepo.setClient(client);
          dev.log('Профиль пользователя создан успешно', name: 'Auth');
        }
      }
    } on FirebaseAuthException catch (e) {
      String errorMessage = switch (e.code) {
        'user-disabled' => 'Аккаунт пользователя отключен.',
        'user-not-found' => 'Пользователь не найден.',
        'invalid-credential' => 'Неверные учетные данные.',
        'account-exists-with-different-credential' =>
          'Учетная запись уже существует с другим способом входа.',
        'operation-not-allowed' => 'Вход с помощью телефона отключен.',
        _ => e.message ?? 'Ошибка при входе в систему.'
      };

      dev.log('Ошибка Firebase Auth: $errorMessage', name: 'Auth', error: e);
      state =
          state.copyWith(status: StateStatus.error, errorMessage: errorMessage);
    } catch (e) {
      dev.log('Неожиданная ошибка при входе: ${e.toString()}',
          name: 'Auth', error: e);
      state = state.copyWith(
          status: StateStatus.error,
          errorMessage: 'Произошла неожиданная ошибка при входе.');
    }
  }
}
