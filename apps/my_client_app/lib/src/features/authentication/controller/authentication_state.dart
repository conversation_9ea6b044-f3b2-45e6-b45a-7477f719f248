import 'package:freezed_annotation/freezed_annotation.dart';

part 'authentication_state.freezed.dart';

/// Определяет возможные состояния процесса аутентификации
enum StateStatus {
  initial,
  loading,
  sentOTP,
  loggedIn,
  error,
}

/// Состояние процесса аутентификации по номеру телефона
@freezed
class AuthenticationState with _$AuthenticationState {
  const factory AuthenticationState({
    @Default(StateStatus.initial) StateStatus status,
    @Default('') String phoneNumber,
    String? verificationId,
    String? errorMessage,
    @Default(null) int? resendTimer,
    @Default(false) bool canResend,
  }) = _AuthenticationState;
}
