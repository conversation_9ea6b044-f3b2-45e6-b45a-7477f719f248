import 'package:core_ui/core_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:my_client_app/src/features/authentication/controller/authentication_controller.dart';
import 'package:my_client_app/src/features/authentication/controller/authentication_state.dart';
import 'package:my_client_app/src/routing/typed_routes.dart';

class PhoneSignInScreen extends ConsumerStatefulWidget {
  const PhoneSignInScreen({super.key});

  @override
  ConsumerState<PhoneSignInScreen> createState() => _PhoneSigninScreenState();
}

class _PhoneSigninScreenState extends ConsumerState<PhoneSignInScreen> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _phoneTextController = TextEditingController();
  PhoneNumber number = PhoneNumber(isoCode: 'KZ');

  @override
  void dispose() {
    _phoneTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // ***listen
    ref.listen<AuthenticationState>(authenticationControllerProvider,
        (prev, newState) {
      // Проверяем переход в статус sentOTP
      if (prev?.status != StateStatus.sentOTP &&
          newState.status == StateStatus.sentOTP) {
        // Выполняем навигацию ТОЛЬКО если текущий экран - это PhoneSignInScreen
        final isCurrent = ModalRoute.of(context)?.isCurrent ?? false;
        if (isCurrent) {
          const OtpVerifyRouteData().push(context);
        }
      }
      // Обработка ошибок
      if (newState.status == StateStatus.error &&
          newState.errorMessage != null) {
        // Показываем диалог, только если мы на текущем экране,
        // чтобы избежать показа ошибки поверх OTP экрана, если ошибка пришла с задержкой
        final isCurrent = ModalRoute.of(context)?.isCurrent ?? false;
        // Дополнительно проверим, что предыдущий статус не был ошибкой с тем же сообщением,
        // чтобы не показывать диалог несколько раз подряд при быстрых изменениях state
        if (isCurrent && prev?.errorMessage != newState.errorMessage) {
          showErrorDialog(context, 'Ошибка', newState.errorMessage!);
        }
      }
    });

    // *** provider
    final authenticationState = ref.watch(authenticationControllerProvider);

    // *** widget
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Авторизация',
                  style: TextStyle(fontSize: 36, fontWeight: FontWeight.bold),
                ),
                Text('Пожалуйста введите ваш номер телефона'),
                gapH32,
                Form(
                  key: _formKey,
                  child: Container(
                    padding: EdgeInsets.only(left: 12, top: 8, bottom: 8),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: Colors.grey.shade100),
                    child: InternationalPhoneNumberInput(
                      inputDecoration:
                          InputDecoration(border: InputBorder.none),
                      textStyle: TextStyle(fontSize: 20),
                      selectorTextStyle: TextStyle(fontSize: 20),
                      spaceBetweenSelectorAndTextField: 0,
                      autoFocus: true,
                      maxLength: 10,
                      initialValue: number,
                      onInputChanged: (value) {
                        if (mounted) {
                          setState(() {
                            number = value;
                          });
                        }
                      },
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Введите номер телефона';
                        }
                        if (value.length != 10) {
                          return 'Номер должен содержать 10 цифр';
                        }
                        return null;
                      },
                      countries: ['KZ', 'JP'],
                      selectorConfig: SelectorConfig(
                        selectorType: PhoneInputSelectorType.DIALOG,
                        useBottomSheetSafeArea: true,
                      ),
                      textFieldController: _phoneTextController,
                      formatInput: false,
                    ),
                  ),
                ),
                SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text('${_phoneTextController.text.length.toString()}/10'),
                  ],
                ),

                //Button Continue
                gapH32,
                PrimaryButton(
                    text: 'Далее',
                    isLoading:
                        authenticationState.status == StateStatus.loading,
                    onPressed: () {
                      if (_formKey.currentState!.validate()) {
                        if (number.phoneNumber != null) {
                          ref
                              .read(authenticationControllerProvider.notifier)
                              .sendOTP(number.phoneNumber!);
                        }
                      }
                    }),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
