import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:my_client_app/src/features/home/<USER>/category_product_list_args.dart';
import 'package:my_client_app/src/routing/typed_routes.dart';

// Simple Notifier for actions, doesn't hold complex state itself for now.
class HomeController extends Notifier<void> {
  @override
  void build() {
    // No initial state needed for this simple controller
  }

  /// Navigates to the category product list screen with arguments.
  void selectCategory(BuildContext context, String id, String name,
      {required bool isTag}) {
    final args = CategoryProductListArgs(
      categoryId: id,
      categoryName: name,
      isCategory: !isTag,
    );
    // Use typed routes navigation
    CategoryProductListRouteData($extra: args).push(context);

    // Note: Logic for updating commonStateProvider is removed as per the plan.
    // The target screen (CategoryProductListScreen) should read args from GoRouterState.extra.
  }
}

/// Provider for the HomeController
final homeControllerProvider =
    NotifierProvider<HomeController, void>(HomeController.new);
