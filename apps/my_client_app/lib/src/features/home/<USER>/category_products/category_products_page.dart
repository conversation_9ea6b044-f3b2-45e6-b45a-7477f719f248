import 'package:core_data/core_data.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:my_client_app/src/features/home/<USER>/category_product_list_args.dart';
import 'package:my_client_app/src/features/productDetail/product_detail_args.dart';
import 'package:my_client_app/src/routing/typed_routes.dart';
import 'package:core_ui/core_ui.dart';

class CategoryProductListPage extends ConsumerWidget {
  const CategoryProductListPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Получаем аргументы из навигации
    final args = GoRouterState.of(context).extra as CategoryProductListArgs;

    // Выбираем нужный провайдер и получаем его значение
    final productsValue = args.categoryId == 'other'
        ? ref.watch(productListStreamProvider)
        : args.isCategory
            ? ref.watch(productListByCategoryFutureProvider(args.categoryId))
            : ref.watch(productsByTagStreamProvider(args.categoryId));

    return Scaffold(
      appBar: AppBar(
        title: Text(args.categoryName),
      ),
      body: AsyncValueWidget(
          value: productsValue, // Используем единое значение AsyncValue
          data: (productList) {
            // Отображаем список продуктов, если данные успешно загружены
            if (productList.isEmpty) {
              // Можно добавить сообщение, если список пуст
              return const Center(child: Text('Продукты не найдены'));
            }
            return ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              itemCount: productList.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 10),
                  child: ProductCard(
                    product: productList[index],
                    onCardTap: () => ProductDetailRouteData(
                      $extra: ProductDetailArgs(product: productList[index]),
                    ).push(context),
                  ),
                );
              },
            );
          }),
    );
  }
}
