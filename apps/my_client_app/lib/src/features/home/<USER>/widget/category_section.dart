import 'package:flutter/material.dart';
import 'package:my_client_app/src/features/home/<USER>/widget/category_name_field.dart';
import 'package:core_ui/core_ui.dart';

class CategorySection extends StatelessWidget {
  final String title;
  final String categoryId; // Временно, будет улучшено в разделе "Логика"
  final Widget productList; // ProductListHorizontal или ProductListVertical
  final void Function(String categoryId) onSeeAllPressed;

  const CategorySection({
    super.key,
    required this.title,
    required this.categoryId,
    required this.productList,
    required this.onSeeAllPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CategoryNameField(
          categoryName: title,
          onPressed: () => onSeeAllPressed(categoryId),
        ),
        gapH8, // Add spacing between title and product list
        productList,
        // Отступ после секции будет управляться в родительском ListView (например, gapH32)
      ],
    );
  }
}
