import 'package:core_domain/core_domain.dart';
import 'package:flutter/material.dart';
import 'package:my_client_app/src/features/productDetail/product_detail_args.dart';
import 'package:my_client_app/src/routing/typed_routes.dart';
import 'package:core_ui/core_ui.dart';

class ProductListVertical extends StatelessWidget {
  const ProductListVertical({
    super.key,
    required this.products,
  });
  final List<Product> products;

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      separatorBuilder: (context, index) => gapH20,
      padding: EdgeInsets.symmetric(
        horizontal: 20,
        vertical: 0,
      ),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(), // Add const
      itemCount: products.length,
      // Remove redundant SizedBox,
      itemBuilder: (context, index) => ProductCard(
        product: products[index],
        onCardTap: () => ProductDetailRouteData(
            $extra: ProductDetailArgs(product: products[index]),
        ).push(context),
      ),
    );
  }
}
