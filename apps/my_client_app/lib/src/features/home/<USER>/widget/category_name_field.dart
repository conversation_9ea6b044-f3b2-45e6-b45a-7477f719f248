import 'package:flutter/material.dart';

class CategoryNameField extends StatelessWidget {
  const CategoryNameField({
    super.key,
    required this.categoryName,
    this.onPressed,
  });
  final String categoryName;
  final void Function()? onPressed;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Padding(
      // Use symmetric padding for balance
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        // Consider removing MainAxisSize.min if Row should take full width
        // mainAxisSize: MainAxisSize.min, // Usually not needed with spaceBetween in a Padding
        children: [
          // Use Expanded to prevent overflow if categoryName is long
          Expanded(
              child: Text(categoryName,
                  style: textTheme.labelLarge,
                  overflow: TextOverflow.ellipsis)),
          // Use a more subtle style for the "See All" button
          TextButton(
            onPressed: onPressed,
            style: TextButton.styleFrom(
              // Use a standard text style, e.g., bodySmall
              textStyle: textTheme.bodySmall,
              // Reduce padding for a more minimal look if desired
              padding: const EdgeInsets.symmetric(
                  horizontal: 8, vertical: 4), // Adjust as needed
              tapTargetSize:
                  MaterialTapTargetSize.shrinkWrap, // Reduce tap area
              minimumSize: Size.zero, // Allow button to be smaller
            ),
            // Consider making the text localizable later
            child: const Text('Показать все'),
          ),
        ],
      ),
    );
  }
}
