import 'package:core_utils/core_utils.dart';
import 'package:my_client_app/src/routing/app_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:core_domain/core_domain.dart';
import 'package:core_data/core_data.dart';
part 'request_controller.g.dart';

enum RequestAction { cancel, delete }

enum RequestControllerID { basic, detail, pending, accepted, canceled }

@riverpod
class RequestController extends _$RequestController with NotifierMounted {
  @override
  AsyncValue<RequestAction?> build(
      [RequestControllerID controllerID = RequestControllerID.basic]) {
    ref.onDispose(setUnmounted);
    return AsyncValue.data(null);
  }

  Future<void> cancelRequest(Request request) async {
    state = const AsyncLoading();

    final updatedRequest = request.copyWith(
        status: RequestStatus.canceled,
        cancelInitiator: CancelInitiator.client);

    final value = await AsyncValue.guard(() async {
      await ref.read(requestRepositoryProvider).updateRequest(updatedRequest);
      return RequestAction.cancel;
    });

    if (mounted) {
      state = value;
    }
  }

  Future<void> deleteRequest(Request request, {bool goBack = false}) async {
    state = const AsyncLoading();
    await Future.delayed(const Duration(seconds: 1));

    final value = await AsyncValue.guard(() async {
      await ref.read(requestRepositoryProvider).deleteRequest(request);
      return RequestAction.delete;
    });

    if (mounted) {
      state = value;
      if (goBack) ref.read(goRouterProvider).pop();
    }
  }
}
