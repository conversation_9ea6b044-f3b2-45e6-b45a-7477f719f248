import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:my_client_app/src/features/requests/controller/request_controller.dart';
import 'package:my_client_app/src/features/requests/widgets/request_card_wrapper.dart';
import 'package:core_domain/core_domain.dart';
import 'package:core_data/core_data.dart';
import 'package:core_ui/core_ui.dart';

class RequestRejectedTab extends ConsumerWidget {
  const RequestRejectedTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // *** listener
    ref.listen(requestControllerProvider(RequestControllerID.canceled),
        (prev, next) {
      if (next.hasValue && next.value != null) {
        // message
        // final message = switch (next.value!) {
        //   RequestAction.cancel => 'Запрос отменен',
        //   RequestAction.delete => 'Запрос удален',
        // };
        if (next.value == RequestAction.delete) {
          // snackbar
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text('Заявка удалена'),
          ));
        }
      }
    });
    //final clientID = ref.watch(currentUserProvider);
    //final requestListValue = ref.watch(fetchRequestListProvider(clientID!.uid));
    //final requestListValue = ref.watch(fetchRejectedRequestListProvider);
    final requestListValue =
        ref.watch(requestListStatusStreamProvider(RequestStatus.declined));

    // *** widget
    return AsyncValueWidget(
        value: requestListValue,
        data: (requestList) {
          return ListView.builder(
            itemCount: requestList.length,
            itemBuilder: (context, index) => RequestCardWrapper(
              request: requestList[index],
              controllerID: RequestControllerID.canceled,
            ),
          );
        });
  }
}
