import 'package:core_utils/core_utils.dart';
import 'package:flutter/material.dart';
import 'package:core_domain/core_domain.dart';
import 'package:core_theme/core_theme.dart';
import 'package:my_client_app/src/core/design/design_system.dart';

/// Современная минималистичная карточка заявки
///
/// Особенности:
/// - Использование Shadcn UI компонентов
/// - Четкая типографическая иерархия
/// - Компактное изображение 80x80
/// - Современные иконки и цвета статусов
/// - Единообразие с дизайн-системой приложения
class RequestCardModern extends StatelessWidget {
  const RequestCardModern({
    required this.request,
    super.key,
    this.onTap,
    this.onActionPressed,
    this.isLoading = false,
  });

  final Request request;
  final VoidCallback? onTap;
  final VoidCallback? onActionPressed;
  final bool isLoading;

  String _getStatusText() {
    switch (request.status) {
      case RequestStatus.pending:
        return 'Ожидает подтверждения';
      case RequestStatus.accepted:
        return 'Подтверждено';
      case RequestStatus.declined:
        return 'Отклонено';
      case RequestStatus.canceled:
        if (request.cancelInitiator != null) {
          return request.cancelInitiator!.description;
        }
        return 'Отменено';
    }
  }

  Color _getStatusColor(FThemeData theme) {
    return switch (request.status) {
      RequestStatus.pending => Colors.orange,
      RequestStatus.accepted => Colors.green[800]!,
      RequestStatus.declined => theme.colors.destructive,
      RequestStatus.canceled => theme.colors.destructive,
    };
  }

  IconData _getStatusIcon() {
    return switch (request.status) {
      RequestStatus.pending => FIcons.clock,
      RequestStatus.accepted => FIcons.check,
      RequestStatus.declined => FIcons.x,
      RequestStatus.canceled => FIcons.x,
    };
  }

  bool _shouldShowActionButton() {
    return request.status == RequestStatus.pending ||
        request.status == RequestStatus.declined ||
        request.status == RequestStatus.canceled;
  }

  String _getActionButtonText() {
    return request.status == RequestStatus.pending ? 'Отменить' : 'Удалить';
  }

  String _getLoadingText() {
    return request.status == RequestStatus.pending
        ? 'Отменяется...'
        : 'Удаляется...';
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final statusText = _getStatusText();

    return Container(
      margin: const EdgeInsets.symmetric(
          horizontal: DesignSystem.spacingM, vertical: DesignSystem.spacingS),
      child: FCard(
        style: theme.cardStyle.copyWith(
          contentStyle: theme.cardStyle.contentStyle.copyWith(
            padding: EdgeInsets.zero,
          ),
        ),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(DesignSystem.radiusM),
          child: Padding(
            padding: const EdgeInsets.all(DesignSystem.spacingM),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header с изображением и основной информацией
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Изображение
                    ClipRRect(
                      borderRadius: BorderRadius.circular(DesignSystem.radiusS),
                      child: SizedBox(
                        width: 80,
                        height: 80,
                        child: request.imageURL != null
                            ? Image.network(
                                request.imageURL!,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return _buildPlaceholderImage(theme);
                                },
                              )
                            : _buildPlaceholderImage(theme),
                      ),
                    ),

                    const SizedBox(width: DesignSystem.spacingM),

                    // Основная информация
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Название события
                          Text(
                            request.eventName,
                            style: theme.typography.lg.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),

                          const SizedBox(height: DesignSystem.spacingXS),

                          // Партнер
                          Row(
                            children: [
                              Icon(
                                FIcons.user,
                                size: 16,
                                color: theme.colors.mutedForeground,
                              ),
                              const SizedBox(width: DesignSystem.spacingXS),
                              Expanded(
                                child: Text(
                                  request.partnerName,
                                  style: theme.typography.sm.copyWith(
                                    color: theme.colors.mutedForeground,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: DesignSystem.spacingXS),

                          // Дата
                          Row(
                            children: [
                              Icon(
                                FIcons.calendar,
                                size: 16,
                                color: theme.colors.mutedForeground,
                              ),
                              const SizedBox(width: DesignSystem.spacingXS),
                              Text(
                                request.dateStart.format('d MMMM'),
                                style: theme.typography.sm.copyWith(
                                  color: theme.colors.mutedForeground,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: DesignSystem.spacingM),

                // Статус
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: DesignSystem.spacingS,
                        vertical: DesignSystem.spacingXS,
                      ),
                      decoration: BoxDecoration(
                        color: _getStatusColor(theme).withValues(alpha: 0.1),
                        borderRadius:
                            BorderRadius.circular(DesignSystem.radiusS),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _getStatusIcon(),
                            size: 16,
                            color: _getStatusColor(theme),
                          ),
                          const SizedBox(width: DesignSystem.spacingXS),
                          Text(
                            statusText,
                            style: theme.typography.sm.copyWith(
                              color: _getStatusColor(theme),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                // Кнопка действий
                if (_shouldShowActionButton() && onActionPressed != null) ...[
                  const SizedBox(height: DesignSystem.spacingM),
                  FButton(
                    style: FButtonStyle.secondary,
                    onPress: isLoading ? () {} : onActionPressed,
                    child: isLoading
                        ? Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: theme.colors.foreground,
                                ),
                              ),
                              const SizedBox(width: DesignSystem.spacingS),
                              Text(_getLoadingText()),
                            ],
                          )
                        : Text(_getActionButtonText()),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPlaceholderImage(FThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: theme.colors.muted,
        borderRadius: BorderRadius.circular(DesignSystem.radiusS),
      ),
      child: Icon(
        FIcons.image,
        size: 32,
        color: theme.colors.mutedForeground,
      ),
    );
  }
}
