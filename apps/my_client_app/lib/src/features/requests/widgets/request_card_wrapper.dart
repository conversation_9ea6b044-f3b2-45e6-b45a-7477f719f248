import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:my_client_app/src/features/requests/controller/request_controller.dart';
import 'package:my_client_app/src/features/requests/controller/selected_request_state.dart';
import 'package:my_client_app/src/features/requests/widgets/request_card_modern.dart';
import 'package:my_client_app/src/routing/typed_routes.dart';
import 'package:core_domain/core_domain.dart';

/// Обертка над RequestCardModern с логикой управления состоянием
class RequestCardWrapper extends ConsumerWidget {
  const RequestCardWrapper({
    required this.request,
    required this.controllerID,
    super.key,
  });

  final Request request;
  final RequestControllerID controllerID;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controller = ref.watch(requestControllerProvider(controllerID));
    final selectedRequest = ref.watch(selectedRequestProvider);
    final isLoading = controller.isLoading && selectedRequest?.id == request.id;

    return RequestCardModern(
      request: request,
      isLoading: isLoading,
      onTap: () {
        RequestDetailRouteData($extra: request).push(context);
      },
      onActionPressed: () {
        // Добавляем заявку в selected state
        ref.read(selectedRequestProvider.notifier).addRequest(request);

        // Выполняем действие в зависимости от статуса
        if (request.status == RequestStatus.pending) {
          ref
              .read(requestControllerProvider(controllerID).notifier)
              .cancelRequest(request);
        } else {
          ref
              .read(requestControllerProvider(controllerID).notifier)
              .deleteRequest(request);
        }
      },
    );
  }
}
