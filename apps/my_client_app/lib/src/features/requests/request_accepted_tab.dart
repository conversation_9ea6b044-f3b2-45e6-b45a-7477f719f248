import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:my_client_app/src/features/requests/controller/request_controller.dart';
import 'package:my_client_app/src/features/requests/widgets/request_card_wrapper.dart';
import 'package:core_domain/core_domain.dart';
import 'package:core_data/core_data.dart';
import 'package:core_ui/core_ui.dart';

class RequestAceeptedTab extends ConsumerWidget {
  const RequestAceeptedTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    //final clientID = ref.watch(currentUserProvider);
    //final requestListValue = ref.watch(fetchRequestListProvider(clientID!.uid));
    //final requestListValue = ref.watch(fetchAcceptedRequestListProvider);
    final requestListValue =
        ref.watch(requestListStatusStreamProvider(RequestStatus.accepted));
    return AsyncValueWidget(
        value: requestListValue,
        data: (requestList) {
          return ListView.builder(
            itemCount: requestList.length,
            itemBuilder: (context, index) => RequestCardWrapper(
              request: requestList[index],
              controllerID: RequestControllerID.accepted,
            ),
          );
        });
  }
}
