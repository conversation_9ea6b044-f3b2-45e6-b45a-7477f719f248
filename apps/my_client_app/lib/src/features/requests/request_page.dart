import 'package:core_theme/core_theme.dart';
import 'package:flutter/material.dart';
import 'package:my_client_app/src/features/requests/request_accepted_tab.dart';
import 'package:my_client_app/src/features/requests/request_pending_tab.dart';
import 'package:my_client_app/src/features/requests/request_rejected_tab.dart';

class RequestPage extends StatelessWidget {
  const RequestPage({super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = context.theme.typography;
    return DefaultTabController(
      initialIndex: 0,
      length: 3,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: TabBar(
              tabAlignment: TabAlignment.start,
              isScrollable: true,
              indicatorSize: TabBarIndicatorSize.tab,
              dividerColor: Colors.transparent,
              labelStyle: textTheme.sm.copyWith(fontWeight: FontWeight.w600),
              unselectedLabelStyle:
                  textTheme.sm.copyWith(fontWeight: FontWeight.w400),
              tabs: const [
                Tab(text: 'Текущие'),
                Tab(text: 'В рассмотрении'),
                Tab(text: 'Отказанные'),
              ]),
        ),
        body: TabBarView(children: [
          RequestAceeptedTab(),
          RequestPendingTab(),
          RequestRejectedTab(),
        ]),
      ),
    );
  }
}
