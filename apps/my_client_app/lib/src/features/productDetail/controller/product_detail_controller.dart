import 'package:core_utils/core_utils.dart';
import 'package:my_client_app/src/features/productDetail/controller/product_detail_provider.dart';
import 'package:my_client_app/src/routing/app_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:core_domain/core_domain.dart';
import 'package:core_data/core_data.dart';
import 'package:uuid/uuid.dart';

part 'product_detail_controller.g.dart';

@riverpod
class ProductDetailController extends _$ProductDetailController
    with NotifierMounted {
  @override
  AsyncValue<void> build() {
    ref.onDispose(setUnmounted);
    return AsyncValue.data(null);
  }

  // *** add Reserv
  Future<void> addReserv(ProductDetailModel reserv) async {
    state = AsyncLoading<void>();
    final String cleintId =
        ref.read(firebaseAuthRepositoryProvider).currentUser!.uid;
    final partner = await ref
        .read(partnerRepositoryProvider)
        .fetchPartner(reserv.myService!.partnerRef);
    final request = Request(
      id: Uuid().v4(),
      slots: reserv.slots,
      status: RequestStatus.pending,
      eventName: reserv.myService!.name,
      partnerName: partner.name,
      dateStart: reserv.myService!.dateStart,
      eventID: reserv.myService!.id,
      clientID: cleintId,
      partnerID: reserv.myService!.partnerRef,
      imageURL: reserv.myService!.imageURL,
    );

    final value = await AsyncValue.guard(
        () async => ref.read(requestRepositoryProvider).addRequest(request));

    final success = value.hasError == false;
    if (mounted) {
      state = value;
      if (success) {
        ref.read(goRouterProvider).pop();
      }
    }
  }
} //end class

// Renamed from ProductDetailController2 for clarity (Task 2.1)
@riverpod
class ProductFavoriteController extends _$ProductFavoriteController
    with NotifierMounted {
  @override
  FutureOr<void> build() {
    ref.onDispose(setUnmounted);
    return AsyncValue.data(null);
  }

  // *** add to Favorite
  Future<void> addFavoriteProduct(String productId) async {
    state = await AsyncValue.guard(() async {
      final client = await ref.read(watchClientProvider.future);
      final List<String> updatedList = List.from(client.favoriteProductIDList);

      // print(
      //     '**********Before operation - List: $updatedList, ProductId: $productId');

      if (updatedList.contains(productId)) {
        // print('************ Removing product $productId from favorites');
        updatedList.remove(productId);
      } else {
        // print('************Adding product $productId to favorites');
        updatedList.add(productId);
      }

      // print('**********After operation - Updated list: $updatedList');

      final updatedClient = client.copyWith(favoriteProductIDList: updatedList);
      await ref.read(firebaseClientRepoProvider).updateClient(updatedClient);
      ref.invalidate(fetchServiceTypeByIdListProvider);
    });
  }
}
