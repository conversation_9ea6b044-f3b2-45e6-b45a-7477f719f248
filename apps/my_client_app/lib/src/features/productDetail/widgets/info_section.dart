import 'package:core_domain/core_domain.dart';
import 'package:core_theme/core_theme.dart';
import 'package:core_utils/core_utils.dart';
import 'package:flutter/material.dart';
import 'package:my_client_app/src/features/productDetail/product_detail_args.dart';
import 'package:core_ui/core_ui.dart';

class InfoSection extends StatelessWidget {
  // Use args instead of individual fields + previousRoute (Task 2.3)
  const InfoSection({
    super.key,
    required this.args,
    required this.textTheme,
  });

  final ProductDetailArgs args;
  final FTypography textTheme;
  // final String previousRoute; // Removed

  @override
  Widget build(BuildContext context) {
    final product = args.product; // Get product from args
    return Padding(
      // Standardize horizontal padding
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          gapH16,
          Text(
            product.name,
            style: textTheme.lg,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          gapH8,

          Row(
            children: [
              Icon(FIcons.banknote, size: 16),
              gapW8,
              Text(
                '${product.price.formatWithSpaces()} тнг',
              ),
            ],
          ),
          gapH4,
          Row(
            children: [
              // Use calendar icon for days
              Icon(FIcons.hourglass, size: 16),
              gapW8,
              Text(
                '${product.duration} ${product.durationType.formatDuration(product.duration)}',
              ),
            ],
          ),
          gapH4,

          Row(
            children: [
              Icon(
                FIcons.users,
                size: 16,
              ),
              gapW8,
              Text(
                'до ${product.slots} гостей',
                //style: textTheme.bodyMedium, // Use recommended style
              ),
            ],
          ),
          gapH16, // Increase gap before divider
          // Style the divider
          Divider(thickness: 1),
          gapH16,
        ],
      ),
    );
  }
}
