import 'package:core_theme/core_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:core_domain/core_domain.dart';
import 'package:core_data/core_data.dart';
import 'package:core_ui/core_ui.dart';

class PartnerSection extends ConsumerWidget {
  const PartnerSection({
    super.key,
    required this.textTheme,
    required this.product,
    this.onTap,
  });

  final FTypography textTheme;
  final Product product;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final partnerValue = ref.watch(fetchPartnerProvider(product.partnerRef));

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Партнер',
            style: textTheme.lg.copyWith(height: 1),
          ),
          AsyncValueWidget(
            value: partnerValue,
            data: (partner) {
              return ListTile(
                contentPadding: EdgeInsets.all(0),
                title: Text(partner.name),
                subtitle: Text(partner.phoneNumber),
                leading: CircleAvatar(
                  radius: 25,
                  backgroundImage: partner.imageURL != null
                      ? NetworkImage(partner.imageURL!)
                      : null,
                ),
                trailing: Icon(
                  //Icons.arrow_forward_ios,
                  FIcons.chevronRight,
                  size: 16,
                ),
                onTap: onTap,
              );
            },
          ),
          SDivider(),
        ],
      ),
    );
  }
}
