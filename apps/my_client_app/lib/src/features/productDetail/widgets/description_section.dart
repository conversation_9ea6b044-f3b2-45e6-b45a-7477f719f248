import 'package:core_domain/core_domain.dart';
import 'package:core_theme/core_theme.dart';
import 'package:flutter/material.dart';
import 'package:core_ui/core_ui.dart';

class DescriptionSection extends StatelessWidget {
  const DescriptionSection({
    super.key,
    required this.product,
    required this.theme,
  });

  final Product product;
  final FThemeData theme;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Style the divider

          Text(
            'Информация',
            style: theme.typography.lg.copyWith(height: 1),
          ),
          gapH12,
          Text(
            product.description,
          ),
          // Removed SizedBox(height: 50) - moved outside this widget
        ],
      ),
    );
  }
}
