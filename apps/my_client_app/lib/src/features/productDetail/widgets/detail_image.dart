import 'package:core_theme/core_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:my_client_app/src/features/productDetail/controller/product_detail_controller.dart';
import 'package:core_data/core_data.dart';
import 'package:core_ui/core_ui.dart';
import 'package:forui/forui.dart';

class DetailImage extends ConsumerWidget {
  const DetailImage({
    super.key,
    required this.imageUrl,
    required this.productID,
  });

  final String imageUrl;
  final String productID;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isFavoriteValue = ref.watch(
      watchClientProvider.select(
        (asyncClient) => asyncClient.whenData(
          (client) => client.favoriteProductIDList.contains(productID),
        ),
      ),
    );

    return AsyncValueWidget(
      value: isFavoriteValue,
      data: (isFavorite) {
        return SizedBox(
          height: MediaQuery.of(context).size.height * 0.4,
          width: double.infinity,
          child: Stack(
            children: [
              MyNetworkImage(
                imageUrl: imageUrl,
                height: MediaQuery.of(context).size.height * 0.4,
                width: double.infinity,
                fit: BoxFit.cover,
                cacheKey: 'product_detail_$productID',
                memCacheHeight: 600,
                memCacheWidth: 800,
              ),
              // Gradient overlay for better button visibility
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                height: 120,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.black.withValues(alpha: 0.4),
                        Colors.black.withValues(alpha: 0.2),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              ),
              _ImageOverlayButtons(
                isFavorite: isFavorite,
                onBackButtonPressed: () => Navigator.of(context).pop(),
                onFavoriteButtonPressed: () {
                  ref
                      .read(productFavoriteControllerProvider.notifier)
                      .addFavoriteProduct(productID);
                },
              ),
            ],
          ),
        );
      },
    );
  }
}

// --- Extracted Private Widget for Overlay Buttons ---

class _ImageOverlayButtons extends StatelessWidget {
  const _ImageOverlayButtons({
    required this.isFavorite,
    this.onBackButtonPressed,
    this.onFavoriteButtonPressed,
  });

  final bool isFavorite;
  final VoidCallback? onBackButtonPressed;
  final VoidCallback? onFavoriteButtonPressed;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Back Button with subtle background
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.black.withValues(alpha: 0.2),
              ),
              child: IconButton(
                icon:
                    const Icon(FIcons.arrowLeft, color: Colors.white, size: 24),
                onPressed: onBackButtonPressed,
              ),
            ),
            // Favorite Button with subtle background
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.black.withValues(alpha: 0.2),
              ),
              child: IconButton(
                icon: isFavorite
                    ? const Icon(
                        Icons.favorite,
                        color: Colors.red,
                        size: 24,
                      )
                    : const Icon(
                        Icons.favorite_border,
                        color: Colors.white,
                        size: 24,
                      ),
                onPressed: onFavoriteButtonPressed,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
