import 'package:core_theme/core_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:my_client_app/src/features/productDetail/controller/product_detail_provider.dart';
import 'package:my_client_app/src/features/productDetail/widgets/booking_bottom_sheet.dart';
import 'package:core_ui/core_ui.dart';

class BookingButtonSection extends ConsumerWidget {
  const BookingButtonSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colorScheme = context.theme.colors;
    final selectedService = ref
        .watch(productDetailStateProvider.select((state) => state.myService));
    return Container(
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: colorScheme.border, width: 1),
        ),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: PrimaryButton(
        text: 'Забронировать',
        onPressed: selectedService == null
            ? null
            : () async {
                //await DetailBottomSheet.show(context);
                showFSheet(
                  context: context,
                  side: FLayout.btt, // Bottom-to-top
                  builder: (context) => const DetailBottomSheet(),
                );
              },
      ),
    );
  }
}


  // /// Shows the booking bottom sheet using Forui FSheet
  // static Future<void> show(BuildContext context) {
  //   return showFSheet(
  //     context: context,
  //     side: FLayout.btt, // Bottom-to-top
  //     builder: (context) => const DetailBottomSheet(),
  //   );
  // }