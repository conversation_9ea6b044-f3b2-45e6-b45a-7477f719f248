import 'package:core_utils/core_utils.dart';
import 'package:flutter/material.dart';
import 'package:core_domain/core_domain.dart';
import 'package:core_ui/core_ui.dart';
import 'package:core_theme/core_theme.dart';

class EventSheduleCard extends StatelessWidget {
  const EventSheduleCard({
    super.key,
    required this.myService,
    required this.onTap,
    this.isSelected = false,
  });
  final Event myService;
  final void Function()? onTap;
  final bool isSelected;

  String _getDateLabel() {
    final now = DateTime.now();
    final eventDate = myService.dateStart;

    // Проверяем, сегодня ли событие
    if (eventDate.year == now.year &&
        eventDate.month == now.month &&
        eventDate.day == now.day) {
      return 'Сегодня, ${eventDate.format('d MMMM')}';
    }

    // Проверяем, завтра ли событие
    final tomorrow = now.add(Duration(days: 1));
    if (eventDate.year == tomorrow.year &&
        eventDate.month == tomorrow.month &&
        eventDate.day == tomorrow.day) {
      return 'Завтра, ${eventDate.format('d MMMM')}';
    }

    // Обычная дата
    return eventDate.format('d MMMM');
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final textTheme = theme.typography;
    final colorScheme = theme.colors;

    final remainingSlots = myService.slots - myService.slotsReserved;
    final isFullyBooked = remainingSlots <= 0;

    return InkWell(
      onTap: isFullyBooked ? null : onTap,
      borderRadius: theme.style.borderRadius,
      child: FCard(
        style: theme.cardStyle.copyWith(
          decoration: BoxDecoration(
            borderRadius: theme.style.borderRadius,
            border: Border.all(
              color: isSelected ? theme.colors.primary : theme.colors.border,
              width: isSelected ? 2 : 1,
            ),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Основная дата
            Text(
              _getDateLabel(),
              style: textTheme.base.copyWith(
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
            gapH12,

            // Информация о местах с визуальным акцентом
            Text(
              isFullyBooked ? 'Мест нет' : 'Осталось $remainingSlots мест',
              style: textTheme.sm.copyWith(
                color: isFullyBooked
                    ? colorScheme.destructive
                    : isSelected
                        ? colorScheme.primary
                        : colorScheme.mutedForeground,
                fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
