import 'package:core_theme/core_theme.dart';
import 'package:core_utils/core_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:my_client_app/src/features/productDetail/controller/product_detail_controller.dart';
import 'package:my_client_app/src/features/productDetail/controller/product_detail_provider.dart';
import 'package:core_ui/core_ui.dart';

class DetailBottomSheet extends ConsumerWidget {
  const DetailBottomSheet({
    super.key,
  });

  // /// Shows the booking bottom sheet using Forui FSheet
  // static Future<void> show(BuildContext context) {
  //   return showFSheet(
  //     context: context,
  //     side: FLayout.btt, // Bottom-to-top
  //     builder: (context) => const DetailBottomSheet(),
  //   );
  // }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen(productDetailControllerProvider, (prev, next) {
      if (next.hasError && next.error != null) {
        showErrorDialog(context, 'Ошибка', next.error.toString());
      }
    });

    final myService = ref
        .watch(productDetailStateProvider.select((state) => state.myService!));
    final slots =
        ref.watch(productDetailStateProvider.select((state) => state.slots));
    final controller = ref.watch(productDetailControllerProvider);
    final theme = context.theme;
    bool isLimit = slots >= myService.slots - myService.slotsReserved;
    final totalCost = myService.price * slots; // Use selected slots

    return Material(
      color: theme.colors.background,
      borderRadius: const BorderRadius.vertical(
        top: Radius.circular(20),
      ),
      child: SafeArea(
        top: false,
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Title section
              // Text(
              //   myService.name,
              //   style: theme.typography.xl,
              //   textAlign: TextAlign.center,
              // ),
              Text(
                myService.dateStart.format('d MMMM, EEE'),
                style: theme.typography.lg.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              gapH24,

              // Content section

              gapH8,
              Text(
                'Количество мест',
                style:
                    theme.typography.lg.copyWith(fontWeight: FontWeight.w600),
              ),
              gapH16,

              // Counter section
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  DecoratedBox(
                    decoration: BoxDecoration(
                      color: theme.colors.border,
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      onPressed: slots > 0
                          ? () {
                              ref
                                  .read(productDetailStateProvider.notifier)
                                  .decrement();
                            }
                          : null,
                      icon: Icon(
                        FIcons.minus,
                        size: 45,
                      ),
                    ),
                  ),
                  gapW48,
                  Text(
                    slots.toString(),
                    style:
                        theme.typography.lg.copyWith(fontSize: 46, height: 1),
                  ),
                  gapW48,
                  DecoratedBox(
                    decoration: BoxDecoration(
                      color: theme.colors.border,
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      onPressed: isLimit
                          ? null
                          : () {
                              ref
                                  .read(productDetailStateProvider.notifier)
                                  .increment();
                            },
                      icon: Icon(
                        FIcons.plus,
                        size: 45,
                      ),
                    ),
                  ),
                ],
              ),
              gapH16,
              Text(
                'Свободно ${myService.slots - myService.slotsReserved}',
                style: TextStyle(
                  color: isLimit
                      ? theme.colors.destructive
                      : theme.colors.mutedForeground,
                  fontWeight: isLimit ? FontWeight.bold : null,
                ),
              ),
              gapH32,

              // Total cost,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Цена за гостя',
                    style: theme.typography.base,
                  ),
                  Text(
                    ' ${myService.price.toPrice()}',
                    style: theme.typography.base,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 12),
                child: Divider(
                  color: theme.colors.border,
                  thickness: 1,
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Итого:',
                    style: theme.typography.lg
                        .copyWith(fontWeight: FontWeight.w600, height: 1),
                  ),
                  Text(
                    ' ${totalCost.toPrice()}',
                    style: theme.typography.lg
                        .copyWith(fontWeight: FontWeight.w600, height: 1),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              gapH24,

              // Action button
              SizedBox(
                width: double.infinity,
                child: PrimaryButton(
                  text: 'Подтвердить',
                  isLoading: controller.isLoading,
                  onPressed: (controller.isLoading || slots <= 0)
                      ? null
                      : () {
                          final currentState =
                              ref.read(productDetailStateProvider);
                          ref
                              .read(productDetailControllerProvider.notifier)
                              .addReserv(currentState);
                        },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
