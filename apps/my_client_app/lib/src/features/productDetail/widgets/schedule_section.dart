import 'package:core_theme/core_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:my_client_app/src/features/productDetail/controller/product_detail_provider.dart';
import 'package:my_client_app/src/features/productDetail/widgets/event_shedule_card.dart';
import 'package:core_domain/core_domain.dart';
import 'package:core_data/core_data.dart';
import 'package:core_ui/core_ui.dart';

class ScheduleSection extends ConsumerWidget {
  const ScheduleSection({
    super.key,
    required this.product,
  });

  final Product product;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = context.theme;
    final textTheme = theme.typography;
    final colorTheme = theme.colors;
    // final myServiceListValue =
    //     ref.watch(myServiceFromRefListProvider(myServiceIdList));
    // final eventListValue = ref.watch(fetchEventsByProductProvider(product.id));

    // Ограничиваем количество событий для предотвращения ошибок Firestore
    final limitedEventRefList = product.eventRefList.length > 100
        ? product.eventRefList.take(100).toList()
        : product.eventRefList;

    final eventListValue =
        ref.watch(eventListByIDListFutureProvider(limitedEventRefList));

    // Watch only the selected service ID
    final selectedServiceId = ref.watch(
        productDetailStateProvider.select((state) => state.myService?.id));
    // final state = ref.watch(productDetailStateProvider); // Removed full state watch

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            'Ближайшие свободные даты',
            style: textTheme.lg.copyWith(height: 1),
          ),
        ),
        gapH20,
        // Remove SizedBox with f,ixed height
        AsyncValueWidget<List<Event>>(
          value: eventListValue,
          data: (eventList) {
            if (eventList.isNotEmpty) {
              // Возвращаем горизонтальный Row с прокруткой
              return SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  children: eventList.asMap().entries.map((entry) {
                    final index = entry.key;
                    final event = entry.value;
                    return Row(
                      children: [
                        EventSheduleCard(
                          myService: event,
                          isSelected: selectedServiceId == event.id,
                          onTap: event.slotsReserved == event.slots
                              ? null
                              : () => ref
                                  .read(productDetailStateProvider.notifier)
                                  .selectEvent(event),
                        ),
                        if (index < eventList.length - 1) gapW12,
                      ],
                    );
                  }).toList(),
                ),
              );
            } else {
              // Replace Container with simple Text for empty state
              // *** schedule is empty
              return Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
                child: FCard(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        Icon(
                          FIcons.info,
                          size: 36,
                          color: colorTheme.mutedForeground,
                        ),
                        gapH8,
                        Text(
                          'К сожалению расписание отсутствует',
                          style: theme.typography.xl.copyWith(
                            color: theme.colors.mutedForeground,
                          ),
                          textAlign: TextAlign.center,
                          // style: textTheme.bodyMedium?.copyWith(
                          //     color: Colors.grey.shade600), // Use neutral color
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }
          },
        ),
        SDivider(horizontalPadding: 16),
      ],
    );
  }
}
