import 'package:core_theme/core_theme.dart';
import 'package:core_ui/core_ui.dart';
import 'package:core_domain/core_domain.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:my_client_app/src/core/constants/my_const.dart';
import 'package:my_client_app/src/features/productDetail/widgets/booking_button_section.dart';
import 'package:my_client_app/src/features/productDetail/widgets/description_section.dart';
import 'package:my_client_app/src/features/productDetail/controller/product_detail_controller.dart';
import 'package:my_client_app/src/features/productDetail/widgets/partner_section.dart';
import 'package:my_client_app/src/features/productDetail/widgets/schedule_section.dart';
import 'package:my_client_app/src/features/productDetail/widgets/detail_image.dart';
import 'package:my_client_app/src/features/productDetail/widgets/product_info_card.dart';
import 'package:my_client_app/src/routing/typed_routes.dart';

class ProductDetailsPage extends ConsumerWidget {
  const ProductDetailsPage({
    super.key,
    required this.product,
    this.hidePartnerField = false,
  });

  final Product product;
  final bool hidePartnerField;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen(productDetailControllerProvider, (prev, next) {
      if (next.hasValue) {
        showSuccessSnackBar(context, 'Заявка отправлена успешно');
      }
    });
    //final state = ref.watch(productDetailStateProvider);
    final theme = context.theme;
    final textTheme = theme.typography;
    final imageURL = product.imageUrl ?? myImageURL;


    return Scaffold(
      // Add SafeArea
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // *** Image
                  DetailImage(
                    imageUrl: imageURL,
                    productID: product.id,
                  ),
                  gapH16,

                  // *** Info Card (Improved)
                  ProductInfoCard(product: product),

                  // *** PartnerField
                  // Conditionally display PartnerField based on the flag (Task 2.3)
                  if (!hidePartnerField)
                    PartnerSection(
                      textTheme: textTheme,
                      product: product,
                      onTap: () => PartnerDetailRouteData(
                          partnerID: product.partnerRef,
                      ).push(context),
                    ),

                  // *** Schedule
                  ScheduleSection(product: product),
                  //gapH24,

                  // *** Description
                  DescriptionSection(
                    product: product,
                    theme: theme,
                  ),
                  const SizedBox(height: 100),
                ],
              ),
            ),
          ),
          Column(
            mainAxisSize: MainAxisSize.min,
            children: const [
              //BookingSelectionPreview(),
              BookingButtonSection(),
            ],
          ),
        ],
      ), // Close SafeArea
    );
  }
}
