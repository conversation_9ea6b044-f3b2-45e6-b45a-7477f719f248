import 'dart:developer';
import 'package:core_utils/core_utils.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:core_domain/core_domain.dart';
import 'package:core_data/core_data.dart';
part 'profile_controller.g.dart';

@riverpod
class ProfileController extends _$ProfileController with NotifierMounted {
  @override
  AsyncValue<void> build() {
    ref.onDispose(() {
      setUnmounted();

      log('ClientDetailController closed');
    });
    ref.onAddListener(() {
      log('ClientDetailController opened');
    });
    return AsyncValue.data(null);
  }

  // *** updateClient
  Future<void> updateClient(String? localImage, Client client) async {
    String? imageURL;
    state = AsyncValue.loading();
    if (localImage != null) {
      imageURL = await ref
          .read(storageRepositoryProvider)
          .upLoadClientImage(localImage, client.id);
    }

    // *** Add imageURL
    if (imageURL != null) {
      client = client.copyWith(imageURL: imageURL);
    }

    final clientRepo = ref.read(firebaseClientRepoProvider);
    final value = await AsyncValue.guard(() => clientRepo.updateClient(client));

    if (mounted) {
      state = value;
    }
  }
}
