import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:core_domain/core_domain.dart';
import 'package:core_data/core_data.dart';
part 'profile_notifier.freezed.dart';
part 'profile_notifier.g.dart';

@riverpod
class ProfileNotifier extends _$ProfileNotifier {
  @override
  ProfileState build() {
    final clientValue = ref.watch(watchClientProvider);
    String name = '';
    String phone = '';

    if (clientValue is AsyncData<Client>) {
      name = clientValue.value.name ?? '';
      phone = clientValue.value.phoneNumber;
    }

    return ProfileState(
      clientValue: clientValue,
      name: name,
      phone: phone,
    );
  }

  void clearLocalImage() {
    state = state.copyWith(localImage: null);
  }

  void setLocalImage(String? localImage) {
    state = state.copyWith(localImage: localImage);
  }

  // Обновляем имя в состоянии
  void setName(String name) {
    state = state.copyWith(name: name);
  }

  // Обновляем телефон в состоянии
  void setPhone(String phone) {
    state = state.copyWith(phone: phone);
  }

  // Метод для уведомления об изменениях
  void notifyChanges() {
    // Пересоздаем объект состояния, чтобы вызвать обновление UI
    state = state.copyWith();
  }
}

@freezed
class ProfileState with _$ProfileState {
  factory ProfileState({
    required AsyncValue<Client> clientValue,
    required String name,
    required String phone,
    String? localImage,
  }) = _ProfileState;
}
