import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:core_theme/core_theme.dart';

class ProfileTextField extends StatelessWidget {
  const ProfileTextField({
    super.key,
    required this.textController,
    required this.labelName,
    this.hintText,
    this.enabled = true,
    this.onFieldSubmit,
  });

  final TextEditingController textController;
  final String labelName;
  final String? hintText;
  final bool enabled;
  final VoidCallback? onFieldSubmit;

  @override
  Widget build(BuildContext context) {
    return FTextField(
      controller: textController,
      label: Text(labelName),
      hint: hintText,
      enabled: enabled,
      onChange: (_) => onFieldSubmit?.call(),
    );
  }
}
