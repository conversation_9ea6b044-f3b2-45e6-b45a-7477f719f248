import 'package:flutter/material.dart';
import 'package:core_theme/core_theme.dart';
import 'package:core_ui/core_ui.dart';

class ProfileSkeleton extends StatelessWidget {
  const ProfileSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    
    return Padding(
      padding: const EdgeInsets.only(left: 20, right: 20, bottom: 10),
      child: Column(
        children: [
          // Avatar skeleton
          Container(
            width: 200,
            height: 200,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: theme.colors.muted,
            ),
          ),
          gapH32,
          
          // Name field skeleton
          _buildFieldSkeleton(theme),
          gapH24,
          
          // Phone field skeleton  
          _buildFieldSkeleton(theme),
          gapH32,
          
          // Save button skeleton
          Container(
            width: double.infinity,
            height: Sizes.p48,
            decoration: BoxDecoration(
              color: theme.colors.muted,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildFieldSkeleton(FThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label skeleton
        Container(
          width: 80,
          height: 16,
          decoration: BoxDecoration(
            color: theme.colors.muted,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        gapH8,
        // Field skeleton
        Container(
          width: double.infinity,
          height: 48,
          decoration: BoxDecoration(
            color: theme.colors.muted,
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ],
    );
  }
}