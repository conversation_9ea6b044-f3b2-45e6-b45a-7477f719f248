import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:core_theme/core_theme.dart';

enum ToastType { success, error, info }

class ToastHelper {
  static void showToast({
    required BuildContext context,
    required String message,
    required ToastType type,
  }) {
    final theme = context.theme;
    
    Color backgroundColor;
    Color textColor;
    IconData icon;
    
    switch (type) {
      case ToastType.success:
        backgroundColor = theme.colors.primary;
        textColor = theme.colors.primaryForeground;
        icon = FIcons.check;
        break;
      case ToastType.error:
        backgroundColor = theme.colors.destructive;
        textColor = theme.colors.destructiveForeground;
        icon = FIcons.triangleAlert;
        break;
      case ToastType.info:
        backgroundColor = theme.colors.primary;
        textColor = theme.colors.primaryForeground;
        icon = FIcons.info;
        break;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              icon,
              color: textColor,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: theme.typography.sm.copyWith(
                  color: textColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: backgroundColor,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        duration: const Duration(seconds: 3),
      ),
    );
  }
  
  static void showSuccess(BuildContext context, String message) {
    showToast(context: context, message: message, type: ToastType.success);
  }
  
  static void showError(BuildContext context, String message) {
    showToast(context: context, message: message, type: ToastType.error);
  }
  
  static void showInfo(BuildContext context, String message) {
    showToast(context: context, message: message, type: ToastType.info);
  }
}