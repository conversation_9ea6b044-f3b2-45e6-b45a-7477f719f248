import 'dart:io';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:core_ui/core_ui.dart';
import 'package:core_theme/core_theme.dart';

class ProfileAvatar extends StatelessWidget {
  final String? localImagePath;
  final String? networkImageUrl;
  final double radius;
  final Function(String?) onImageSelected;
  final VoidCallback onImageCancelled;
  final Future<String?> Function() pickImage;

  const ProfileAvatar({
    required this.onImageSelected,
    required this.onImageCancelled,
    required this.pickImage,
    this.localImagePath,
    this.networkImageUrl,
    this.radius = 100,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    // Если есть локальное изображение, используем его
    if (localImagePath != null) {
      return LocalImageAvatar(
        localImagePath: localImagePath!,
        radius: radius,
        onImageSelected: onImageSelected,
        onImageCancelled: onImageCancelled,
        pickImage: pickImage,
      );
    }

    // Если есть сетевое изображение, используем MyNetworkImage
    if (networkImageUrl != null) {
      return NetworkImageAvatar(
        networkImageUrl: networkImageUrl!,
        radius: radius,
        onImageSelected: onImageSelected,
        pickImage: pickImage,
      );
    }

    // Если нет изображений, показываем placeholder
    return PlaceholderAvatar(
      radius: radius,
      onImageSelected: onImageSelected,
      pickImage: pickImage,
    );
  }
}

class LocalImageAvatar extends StatefulWidget {
  final String localImagePath;
  final double radius;
  final Function(String?) onImageSelected;
  final VoidCallback onImageCancelled;
  final Future<String?> Function() pickImage;

  const LocalImageAvatar({
    required this.localImagePath,
    required this.radius,
    required this.onImageSelected,
    required this.onImageCancelled,
    required this.pickImage,
    super.key,
  });

  @override
  State<LocalImageAvatar> createState() => _LocalImageAvatarState();
}

class _LocalImageAvatarState extends State<LocalImageAvatar> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    
    return Stack(
      alignment: Alignment.center,
      children: [
        GestureDetector(
          onTap: _isLoading ? null : () async {
            setState(() => _isLoading = true);
            final localImage = await widget.pickImage();
            setState(() => _isLoading = false);
            if (localImage != null && mounted) {
              widget.onImageSelected(localImage);
            }
          },
          child: Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: theme.colors.border,
                width: 2,
              ),
            ),
            child: CircleAvatar(
              radius: widget.radius,
              backgroundImage: FileImage(File(widget.localImagePath)),
            ),
          ),
        ),
        if (_isLoading)
          Container(
            width: widget.radius * 2,
            height: widget.radius * 2,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: theme.colors.background.withValues(alpha: 0.8),
            ),
            child: Center(
              child: SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation(theme.colors.primary),
                ),
              ),
            ),
          ),
        if (!_isLoading) ...[  
          SubtleCameraHint(radius: widget.radius),
          CancelButton(
            radius: widget.radius,
            onPressed: widget.onImageCancelled,
          ),
        ],
      ],
    );
  }
}

class NetworkImageAvatar extends StatefulWidget {
  final String networkImageUrl;
  final double radius;
  final Function(String?) onImageSelected;
  final Future<String?> Function() pickImage;

  const NetworkImageAvatar({
    required this.networkImageUrl,
    required this.radius,
    required this.onImageSelected,
    required this.pickImage,
    super.key,
  });

  @override
  State<NetworkImageAvatar> createState() => _NetworkImageAvatarState();
}

class _NetworkImageAvatarState extends State<NetworkImageAvatar> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    
    return Stack(
      alignment: Alignment.center,
      children: [
        GestureDetector(
          onTap: _isLoading ? null : () async {
            setState(() => _isLoading = true);
            final localImage = await widget.pickImage();
            setState(() => _isLoading = false);
            if (localImage != null && mounted) {
              widget.onImageSelected(localImage);
            }
          },
          child: Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: theme.colors.border,
                width: 2,
              ),
            ),
            child: ClipOval(
              child: SizedBox(
                width: widget.radius * 2,
                height: widget.radius * 2,
                child: MyNetworkImage(
                  imageUrl: widget.networkImageUrl,
                  width: widget.radius * 2,
                  height: widget.radius * 2,
                  fit: BoxFit.cover,
                  borderRadius: BorderRadius.circular(widget.radius),
                ),
              ),
            ),
          ),
        ),
        if (_isLoading)
          Container(
            width: widget.radius * 2,
            height: widget.radius * 2,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: theme.colors.background.withValues(alpha: 0.8),
            ),
            child: Center(
              child: SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation(theme.colors.primary),
                ),
              ),
            ),
          ),
        if (!_isLoading)
          SubtleCameraHint(radius: widget.radius),
      ],
    );
  }
}

class PlaceholderAvatar extends StatefulWidget {
  final double radius;
  final Function(String?) onImageSelected;
  final Future<String?> Function() pickImage;

  const PlaceholderAvatar({
    required this.radius,
    required this.onImageSelected,
    required this.pickImage,
    super.key,
  });

  @override
  State<PlaceholderAvatar> createState() => _PlaceholderAvatarState();
}

class _PlaceholderAvatarState extends State<PlaceholderAvatar> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return GestureDetector(
      onTap: _isLoading ? null : () async {
        setState(() => _isLoading = true);
        final localImage = await widget.pickImage();
        setState(() => _isLoading = false);
        if (localImage != null && mounted) {
          widget.onImageSelected(localImage);
        }
      },
      child: Container(
        width: widget.radius * 2,
        height: widget.radius * 2,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: theme.colors.muted,
          border: Border.all(
            color: theme.colors.border,
            width: 2,
          ),
        ),
        child: _isLoading
          ? Center(
              child: SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation(theme.colors.primary),
                ),
              ),
            )
          : Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  FIcons.user,
                  size: widget.radius * 0.6,
                  color: theme.colors.mutedForeground,
                ),
                SizedBox(height: widget.radius * 0.1),
                Icon(
                  FIcons.camera,
                  size: widget.radius * 0.2,
                  color: theme.colors.primary,
                ),
                SizedBox(height: widget.radius * 0.05),
                Text(
                  'Добавить фото',
                  style: theme.typography.sm.copyWith(
                    color: theme.colors.foreground,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
      ),
    );
  }
}

class SubtleCameraHint extends StatelessWidget {
  final double radius;

  const SubtleCameraHint({
    required this.radius,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    
    return Positioned(
      bottom: radius * 0.1,
      right: radius * 0.1,
      child: Container(
        padding: const EdgeInsets.all(6),
        decoration: BoxDecoration(
          color: theme.colors.background.withValues(alpha: 0.95),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: theme.colors.foreground.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(
          FIcons.camera,
          size: radius * 0.15,
          color: theme.colors.primary,
        ),
      ),
    );
  }
}

class CancelButton extends StatelessWidget {
  final double radius;
  final VoidCallback onPressed;

  const CancelButton({
    required this.radius,
    required this.onPressed,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    
    return Positioned(
      top: 0,
      right: 0,
      child: GestureDetector(
        onTap: onPressed,
        child: Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: theme.colors.destructive,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: theme.colors.foreground.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Icon(
            FIcons.x,
            color: theme.colors.background,
            size: radius * 0.15,
          ),
        ),
      ),
    );
  }
}
