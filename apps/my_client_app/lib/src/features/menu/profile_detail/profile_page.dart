import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:my_client_app/src/features/menu/profile_detail/controller/profile_controller.dart';
import 'package:core_utils/core_utils.dart';
import 'package:my_client_app/src/features/menu/profile_detail/controller/profile_notifier.dart';
import 'package:my_client_app/src/features/menu/profile_detail/profile_text_field.dart';
import 'package:my_client_app/src/features/menu/profile_detail/widgets/profile_avatar.dart';
import 'package:my_client_app/src/features/menu/profile_detail/widgets/toast_helper.dart';
import 'package:my_client_app/src/features/menu/profile_detail/widgets/profile_skeleton.dart';
import 'package:core_domain/core_domain.dart';
import 'package:core_ui/core_ui.dart';
import 'package:core_theme/core_theme.dart';

class ProfilePage extends HookConsumerWidget {
  const ProfilePage({super.key});

  bool _hasChanges(Client client, ProfileState state) {
    // Проверяем изменение имени и наличие локального изображения
    // Removed debug prints
    return (client.name ?? '') != state.name || state.localImage != null;
  }

  String? _validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Имя не может быть пустым';
    }
    if (value.trim().length < 2) {
      return 'Имя должно содержать минимум 2 символа';
    }
    if (value.trim().length > 50) {
      return 'Имя не должно превышать 50 символов';
    }
    return null;
  }

  Future<bool?> _showConfirmationDialog(BuildContext context) {
    final theme = context.theme;
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Подтверждение'),
        content: Text('Вы уверены, что хотите отменить все изменения?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('Нет'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: theme.colors.destructive,
            ),
            child: Text('Да'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    _listenToControllerChanges(context, ref);

    final controllerValue = ref.watch(profileControllerProvider);
    final state = ref.watch(profileNotifierProvider);

    // Initialize controllers directly with state values
    final nameController = useTextEditingController(text: state.name);
    final phoneController = useTextEditingController(text: state.phone);

    // Removed useEffect for synchronization

    return Scaffold(
      appBar: AppBar(
        title: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Профиль'),
            if (state.clientValue.hasValue &&
                _hasChanges(state.clientValue.value!, state))
              Container(
                margin: const EdgeInsets.only(left: 8),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: context.theme.colors.primary,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'Изменено',
                  style: context.theme.typography.xs.copyWith(
                    color: context.theme.colors.primaryForeground,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
          ],
        ),
        centerTitle: true,
      ),
      resizeToAvoidBottomInset: true,
      body: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: state.clientValue.when(
            loading: () => const ProfileSkeleton(),
            error: (error, stack) => Center(
                  child: Text('Ошибка загрузки: $error'),
                ),
            data: (client) {
              return Padding(
                padding: const EdgeInsets.only(left: 20, right: 20, bottom: 10),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // Используем новый виджет ProfileAvatar
                      ProfileAvatar(
                        localImagePath: state.localImage,
                        networkImageUrl: client.imageURL,
                        onImageSelected: (imagePath) {
                          ref
                              .read(profileNotifierProvider.notifier)
                              .setLocalImage(imagePath);
                        },
                        onImageCancelled: () {
                          ref
                              .read(profileNotifierProvider.notifier)
                              .clearLocalImage();
                        },
                        pickImage: pickAndCropImage,
                      ),
                      gapH32,

                      ProfileTextField(
                        textController: nameController,
                        labelName: 'Имя',
                        hintText: 'Введите имя',
                        onFieldSubmit: () {
                          ref
                              .read(profileNotifierProvider.notifier)
                              .setName(nameController.text);
                        },
                      ),
                      gapH24,

                      ProfileTextField(
                        textController: phoneController,
                        labelName: 'Номер телефона',
                        enabled: false,
                      ),
                      gapH32,
                      Row(
                        children: [
                          if (_hasChanges(client, state)) ...[
                            Expanded(
                              child: SizedBox(
                                height: Sizes.p48,
                                child: FButton(
                                  style: FButtonStyle.outline,
                                  onPress: () async {
                                    // Убираем фокус перед показом диалога
                                    FocusScope.of(context).unfocus();

                                    final confirm =
                                        await _showConfirmationDialog(context);
                                    if (confirm == true && context.mounted) {
                                      // Небольшая задержка и убираем фокус после закрытия диалога
                                      await Future.delayed(
                                          const Duration(milliseconds: 100));
                                      if (context.mounted) {
                                        FocusScope.of(context).unfocus();
                                      }

                                      // Сброс имени
                                      nameController.text = client.name ?? '';
                                      ref
                                          .read(
                                              profileNotifierProvider.notifier)
                                          .setName(client.name ?? '');
                                      // Очистка изображения
                                      ref
                                          .read(
                                              profileNotifierProvider.notifier)
                                          .clearLocalImage();
                                      // Показ toast уведомления
                                      if (context.mounted) {
                                        ToastHelper.showInfo(
                                            context, 'Изменения отменены');
                                      }
                                    } else if (context.mounted) {
                                      // Убираем фокус даже если пользователь нажал "Нет"
                                      FocusScope.of(context).unfocus();
                                    }
                                  },
                                  child: const Text('Отменить'),
                                ),
                              ),
                            ),
                            gapW12,
                          ],
                          Expanded(
                            child: PrimaryButton(
                              text: 'Cохранить',
                              isLoading: controllerValue.isLoading,
                              onPressed: _hasChanges(client, state)
                                  ? () {
                                      // Убираем фокус с текстовых полей
                                      FocusScope.of(context).unfocus();

                                      // Валидация имени перед сохранением
                                      final nameValidation =
                                          _validateName(nameController.text);
                                      if (nameValidation != null) {
                                        ToastHelper.showError(
                                            context, nameValidation);
                                        return;
                                      }

                                      final newClient = client.copyWith(
                                          name: nameController.text.trim());
                                      ref
                                          .read(profileControllerProvider
                                              .notifier)
                                          .updateClient(
                                              state.localImage, newClient);
                                    }
                                  : null,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            }),
      ),
    );
  }

  void _listenToControllerChanges(BuildContext context, WidgetRef ref) {
    ref.listen(profileControllerProvider, (prev, next) {
      if (next.isLoading) return;

      if (next.hasValue) {
        ref.read(profileNotifierProvider.notifier).clearLocalImage();
        // Показ toast уведомления
        ToastHelper.showSuccess(context, 'Данные успешно сохранены');
      } else if (next.hasError) {
        // Показ toast уведомления об ошибке
        ToastHelper.showError(context, 'Ошибка: ${next.error}');
      }
    });
  }
}
