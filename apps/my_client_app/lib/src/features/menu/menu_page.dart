import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:my_client_app/src/features/menu/widgets/menu_list_tile.dart';
import 'package:my_client_app/src/routing/typed_routes.dart';
import 'package:core_data/core_data.dart';
import 'package:core_ui/core_ui.dart';

class MenuPage extends ConsumerWidget {
  const MenuPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final clientValue = ref.watch(watchClientProvider);
    final theme = context.theme;

    return Scaffold(
      appBar: AppBar(
        title: Text('Меню'),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(Sizes.p16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AsyncValueWidget(
                  value: clientValue,
                  data: (client) {
                    return FCard(
                      child: InkWell(
                        onTap: () => const ProfileDetailRouteData().push(context),
                        borderRadius: BorderRadius.circular(8),
                        child: Padding(
                          padding: const EdgeInsets.all(Sizes.p16),
                          child: Row(
                            children: [
                              Container(
                                width: 60,
                                height: 60,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: theme.colors.primary
                                        .withValues(alpha: 0.2),
                                    width: 2,
                                  ),
                                ),
                                child: CircleAvatar(
                                  backgroundColor: theme.colors.primary
                                      .withValues(alpha: 0.1),
                                  backgroundImage: client.imageURL == null
                                      ? null
                                      : NetworkImage(client.imageURL!),
                                  child: client.imageURL == null
                                      ? Icon(
                                          FIcons.user,
                                          size: 24,
                                          color: theme.colors.primary,
                                        )
                                      : null,
                                ),
                              ),
                              gapW16,
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      client.name ?? client.phoneNumber,
                                      style: theme.typography.lg,
                                    ),
                                    gapH4,
                                    Text(
                                      'Показать профиль',
                                      style: theme.typography.sm.copyWith(
                                        color: theme.colors.mutedForeground,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Icon(
                                FIcons.chevronRight,
                                size: 20,
                                color: theme.colors.mutedForeground,
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
                gapH24,
                _buildMenuSection(
                  context,
                  title: 'Основные',
                  items: [
                    MenuListTile(
                      title: 'Настройки',
                      leading: Icon(FIcons.settings),
                      onTap: () => const SettingsRouteData().push(context),
                    ),
                    const Divider(height: 1),
                  ],
                ),
                gapH20,
                _buildMenuSection(
                  context,
                  title: 'Поддержка',
                  items: [
                    MenuListTile(
                      title: 'Помощь',
                      leading: Icon(FIcons.circleHelp),
                      onTap: () => const HelpRouteData().push(context),
                    ),
                    const Divider(height: 1),
                    MenuListTile(
                      title: 'О приложении',
                      leading: Icon(FIcons.info),
                      onTap: () => const AboutRouteData().push(context),
                    ),
                    const Divider(height: 1),
                  ],
                ),
                gapH32,
                // Незаметная кнопка выхода внизу слева
                Align(
                  alignment: Alignment.centerLeft,
                  child: TextButton(
                    onPressed: () => _showLogoutDialog(context, ref),
                    style: TextButton.styleFrom(
                      foregroundColor: theme.colors.mutedForeground,
                      padding: const EdgeInsets.symmetric(
                        horizontal: Sizes.p16,
                        vertical: Sizes.p8,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          FIcons.logOut,
                          size: 16,
                          color: theme.colors.mutedForeground,
                        ),
                        gapW8,
                        Text(
                          'Выйти',
                          style: theme.typography.sm.copyWith(
                            color: theme.colors.mutedForeground,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                gapH16,
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMenuSection(
    BuildContext context, {
    required String title,
    required List<Widget> items,
  }) {
    final theme = context.theme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(
              horizontal: Sizes.p16, vertical: Sizes.p8),
          child: Text(
            title,
            style: theme.typography.sm.copyWith(
              color: theme.colors.mutedForeground,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        ...items,
      ],
    );
  }

  void _showLogoutDialog(BuildContext context, WidgetRef ref) {
    final theme = context.theme;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Выйти из аккаунта?'),
        content: Text('Вы уверены, что хотите выйти?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Отмена'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ref.read(firebaseAuthRepositoryProvider).signOut();
            },
            child: Text(
              'Выйти',
              style: TextStyle(color: theme.colors.destructive),
            ),
          ),
        ],
      ),
    );
  }
}
