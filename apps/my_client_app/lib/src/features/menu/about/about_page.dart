import 'package:flutter/material.dart';
import 'package:my_client_app/src/core/design/design_system.dart';
import 'package:my_client_app/src/features/menu/about/widgets/description_section.dart';
import 'package:my_client_app/src/features/menu/about/widgets/logo_section.dart';
import 'package:my_client_app/src/features/menu/about/widgets/version_section.dart';

class AboutPage extends StatelessWidget {
  const AboutPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: Text(
          'О приложении',
          style: DesignSystem.getTextTheme(context).titleLarge,
        ),
        elevation: 0,
      ),
      body: SafeArea(
        child: Center(
          child: ConstrainedBox(
            constraints: const BoxConstraints(
              maxWidth: DesignSystem.maxContentWidth,
            ),
            child: ListView(
              padding: EdgeInsets.symmetric(
                horizontal: DesignSystem.spacingM,
                vertical: DesignSystem.spacingXL,
              ),
              children: const [
                LogoSection(),
                Sized<PERSON>ox(height: DesignSystem.spacingXL),
                DescriptionSection(),
                SizedBox(height: DesignSystem.spacingL),
                VersionSection(),
                SizedBox(height: DesignSystem.spacingXL),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
