import 'package:flutter/material.dart';
import 'package:my_client_app/src/core/design/design_system.dart';
import 'package:package_info_plus/package_info_plus.dart';

class VersionSection extends StatefulWidget {
  const VersionSection({super.key});

  @override
  State<VersionSection> createState() => _VersionSectionState();
}

class _VersionSectionState extends State<VersionSection>
    with SingleTickerProviderStateMixin {
  String? version;
  String? buildNumber;
  late final AnimationController _controller;
  late final Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: DesignSystem.animationDuration,
      vsync: this,
    );

    _fadeAnimation = CurvedAnimation(
      parent: _controller,
      curve: DesignSystem.animationCurve,
    );

    _loadPackageInfo();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _loadPackageInfo() async {
    final packageInfo = await PackageInfo.fromPlatform();
    if (mounted) {
      setState(() {
        version = packageInfo.version;
        buildNumber = packageInfo.buildNumber;
      });
      _controller.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = DesignSystem.getTextTheme(context);

    return Container(
      padding: EdgeInsets.all(DesignSystem.spacingL),
      margin: EdgeInsets.symmetric(vertical: DesignSystem.spacingM),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(DesignSystem.radiusL),
        boxShadow: DesignSystem.shadowS,
      ),
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.info_outline,
              size: 20,
              color: theme.colorScheme.primary,
            ),
            SizedBox(width: DesignSystem.spacingS),
            AnimatedSwitcher(
              duration: DesignSystem.animationDuration,
              child: Text(
                version != null
                    ? 'Версия $version${buildNumber != null ? ' (Build $buildNumber)' : ''}'
                    : 'Загрузка...',
                key: ValueKey(version),
                style: textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.87),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
