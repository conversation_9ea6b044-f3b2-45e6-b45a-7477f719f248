import 'package:flutter/material.dart';
import 'package:my_client_app/src/core/design/design_system.dart';

class Feature {
  final IconData icon;
  final String title;
  final String description;

  const Feature({
    required this.icon,
    required this.title,
    required this.description,
  });
}

class FeatureItem extends StatelessWidget {
  final Feature feature;

  const FeatureItem({
    required this.feature,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = DesignSystem.getTextTheme(context);
    final theme = Theme.of(context);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.all(DesignSystem.spacingS),
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(DesignSystem.radiusS),
          ),
          child: Icon(
            feature.icon,
            size: 24,
            color: theme.colorScheme.primary,
          ),
        ),
        SizedBox(width: DesignSystem.spacingM),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                feature.title,
                style: textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.87),
                ),
              ),
              SizedBox(height: DesignSystem.spacingXS),
              Text(
                feature.description,
                style: textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
