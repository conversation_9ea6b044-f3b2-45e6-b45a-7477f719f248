import 'package:flutter/material.dart';
import 'package:my_client_app/src/core/design/design_system.dart';
import 'feature_item.dart';

class FeaturesListWidget extends StatelessWidget {
  const FeaturesListWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final features = [
      Feature(
        icon: Icons.search_outlined,
        title: 'Поиск туров поблизости',
        description: 'Найдите интересные места рядом с вами',
      ),
      Feature(
        icon: Icons.map_outlined,
        title: 'Детальные описания маршрутов',
        description: 'Подробная информация о каждом туре',
      ),
      Feature(
        icon: Icons.calendar_today_outlined,
        title: 'Прямое бронирование',
        description: 'Быстрое и удобное бронирование туров',
      ),
      Feature(
        icon: Icons.chat_outlined,
        title: 'Связь с местными гидами',
        description: 'Прямое общение с опытными гидами',
      ),
    ];

    return Column(
      children: features.map((feature) {
        return Padding(
          padding: EdgeInsets.symmetric(vertical: DesignSystem.spacingS),
          child: FeatureItem(feature: feature),
        );
      }).toList(),
    );
  }
}
