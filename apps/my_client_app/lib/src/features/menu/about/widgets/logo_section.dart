import 'package:flutter/material.dart';
import 'package:my_client_app/src/core/design/design_system.dart';

class LogoSection extends StatelessWidget {
  const LogoSection({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      children: [
        Container(
          margin: EdgeInsets.symmetric(vertical: DesignSystem.spacingXL),
          width: 160,
          height: 160,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: theme.colorScheme.primary.withValues(alpha: 0.1),
            boxShadow: DesignSystem.shadowS,
          ),
          child: MouseRegion(
            cursor: SystemMouseCursors.click,
            child: Icon(
              Icons.travel_explore,
              size: 80,
              color: theme.colorScheme.primary,
            ),
          ),
        ),
        Text(
          'Local Tours',
          style: DesignSystem.getTextTheme(context).displaySmall?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
        ),
      ],
    );
  }
}
