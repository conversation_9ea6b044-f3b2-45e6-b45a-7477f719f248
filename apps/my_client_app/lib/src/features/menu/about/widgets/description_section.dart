import 'package:flutter/material.dart';
import 'package:my_client_app/src/core/design/design_system.dart';
import 'features_list.dart';

class DescriptionSection extends StatelessWidget {
  const DescriptionSection({super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = DesignSystem.getTextTheme(context);
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.all(DesignSystem.spacingL),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(DesignSystem.radiusL),
        boxShadow: DesignSystem.shadowM,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Откройте для себя уникальные местные туры с нашим приложением. '
            'Исследуйте интересные места и маршруты вместе с опытными местными гидами.',
            style: textTheme.bodyLarge?.copyWith(
              height: 1.5,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.87),
            ),
          ),
          SizedBox(height: DesignSystem.spacingL),
          Text(
            'Основные возможности:',
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
          SizedBox(height: DesignSystem.spacingM),
          const FeaturesListWidget(),
        ],
      ),
    );
  }
}
