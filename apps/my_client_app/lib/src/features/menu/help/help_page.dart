import 'package:flutter/material.dart';
import 'package:my_client_app/src/core/design/design_system.dart';
import 'package:my_client_app/src/features/menu/help/widgets/support_section.dart';

class HelpPage extends StatelessWidget {
  const HelpPage({super.key});

  @override
  Widget build(BuildContext context) {
    //final textTheme = DesignSystem.getTextTheme(context);

    return Scaffold(
      appBar: AppBar(
        title: Text('Помощь'),
      ),
      body: SafeArea(
        child: Center(
          child: ConstrainedBox(
            constraints: const BoxConstraints(
              maxWidth: DesignSystem.maxContentWidth,
            ),
            child: ListView(
              padding: EdgeInsets.symmetric(
                horizontal: DesignSystem.spacingM,
                vertical: DesignSystem.spacingXL,
              ),
              children: [
                // TODO: Добавить FAQ и инструкции
                SupportSection(),
                SizedBox(height: DesignSystem.spacingXL),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
