import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:my_client_app/src/core/design/design_system.dart';
import 'package:url_launcher/url_launcher.dart';

import 'contact_card.dart';

class SupportSection extends StatelessWidget {
  const SupportSection({super.key});

  // В реальном приложении эти данные лучше хранить в конфигурации
  static const String supportEmail = '<EMAIL>';
  static const String supportPhone = '****** 777 7777';

  Future<void> _launchURL(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  Future<void> _copyToClipboard(BuildContext context, String text) async {
    await Clipboard.setData(ClipboardData(text: text));
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.check_circle_outline, color: Colors.white, size: 20),
              SizedBox(width: DesignSystem.spacingS),
              Text('Скопировано: $text'),
            ],
          ),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(DesignSystem.radiusS),
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = DesignSystem.getTextTheme(context);

    return Container(
      padding: EdgeInsets.all(DesignSystem.spacingL),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(DesignSystem.radiusL),
        boxShadow: DesignSystem.shadowM,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Служба поддержки',
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
          SizedBox(height: DesignSystem.spacingL),
          ContactCard(
            icon: Icons.email_outlined,
            title: 'Email',
            value: supportEmail,
            onTap: () => _launchURL('mailto:$supportEmail'),
            onLongPress: () => _copyToClipboard(context, supportEmail),
          ),
          SizedBox(height: DesignSystem.spacingM),
          ContactCard(
            icon: Icons.phone_outlined,
            title: 'Телефон',
            value: supportPhone,
            onTap: () => _launchURL('tel:$supportPhone'),
            onLongPress: () => _copyToClipboard(context, supportPhone),
          ),
        ],
      ),
    );
  }
}
