import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:core_ui/core_ui.dart';

class MenuListTile extends StatelessWidget {
  final Widget? leading;
  final String title;
  final String? subtitle;
  final VoidCallback? onTap;
  final bool showTrailing;
  final Widget? trailing;
  final bool isDestructive;

  const MenuListTile({
    super.key,
    this.leading,
    required this.title,
    this.subtitle,
    this.onTap,
    this.showTrailing = true,
    this.trailing,
    this.isDestructive = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            if (leading != null) ...[
              IconTheme(
                data: IconThemeData(
                  color: isDestructive
                      ? theme.colors.destructive
                      : theme.colors.primary,
                  size: 24,
                ),
                child: leading!,
              ),
              gapW16,
            ],
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.typography.base.copyWith(
                      color: isDestructive ? theme.colors.destructive : null,
                    ),
                  ),
                  if (subtitle != null) ...[
                    gapH4,
                    Text(
                      subtitle!,
                      style: theme.typography.sm.copyWith(
                        color: theme.colors.mutedForeground,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            if (trailing != null)
              trailing!
            else if (showTrailing)
              Icon(
                FIcons.chevronRight,
                size: 20,
                color: theme.colors.mutedForeground,
              ),
          ],
        ),
      ),
    );
  }
}
