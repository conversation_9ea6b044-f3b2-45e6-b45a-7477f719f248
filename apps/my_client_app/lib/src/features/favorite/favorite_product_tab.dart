import 'package:core_data/core_data.dart';
import 'package:core_ui/core_ui.dart';
import 'package:core_theme/core_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class FavoriteServiceTab extends ConsumerWidget {
  const FavoriteServiceTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final productListValue = ref.watch(fetchServiceTypeByIdListProvider);

    return AsyncValueWidget(
        value: productListValue,
        data: (productList) {
          if (productList.isEmpty) {
            final theme = context.theme;
            return Center(
              child: Padding(
                padding: EdgeInsets.all(20),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      FIcons.heart,
                      size: 64,
                      color: theme.colors.mutedForeground,
                    ),
                    gapH16,
                    Text(
                      'Нет избранных услуг',
                      style: theme.typography.lg.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    gapH8,
                    Text(
                      'Добавьте услуги в избранное, чтобы они отображались здесь',
                      style: theme.typography.sm.copyWith(
                        color: theme.colors.mutedForeground,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            );
          }

          return ListView.builder(
            padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            itemCount: productList.length,
            itemBuilder: (context, index) => Padding(
              padding: const EdgeInsets.only(bottom: 10),
              child: ProductCard(
                product: productList[index],
                isfavorite: true,
              ),
            ),
          );
        });
  }
}
