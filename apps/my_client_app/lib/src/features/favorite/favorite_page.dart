import 'package:flutter/material.dart';
import 'package:my_client_app/src/features/favorite/favorite_partner_tab.dart';
import 'package:my_client_app/src/features/favorite/favorite_product_tab.dart';
import 'package:core_theme/core_theme.dart';

class FavoritePage extends StatelessWidget {
  const FavoritePage({super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = context.theme.typography;
    final colorScheme = context.theme.colors;
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: TabBar(
            //tabAlignment: TabAlignment.start,
            //isScrollable: true,
            indicatorSize: TabBarIndicatorSize.label,
            dividerColor: colorScheme.border,
            labelStyle: textTheme.sm.copyWith(fontWeight: FontWeight.w600),
            unselectedLabelStyle:
                textTheme.sm.copyWith(fontWeight: FontWeight.w400),
            tabs: [
              Tab(text: 'Услуги'),
              Tab(text: 'Партнеры'),
            ],
          ),
        ),
        body: Tab<PERSON><PERSON><PERSON><PERSON><PERSON>(
          children: [
            FavoriteServiceTab(),
            FavoriteAgentsTab(),
          ],
        ),
      ),
    );
  }
}
