import 'package:core_data/core_data.dart';
import 'package:core_ui/core_ui.dart';
import 'package:core_theme/core_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:my_client_app/src/routing/typed_routes.dart';

class FavoriteAgentsTab extends ConsumerWidget {
  const FavoriteAgentsTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final partnerListValue = ref.watch(partnerListFavoriteProvider);
    return AsyncValueWidget(
        value: partnerListValue,
        data: (partnerList) {
          if (partnerList.isEmpty) {
            final theme = context.theme;
            return Center(
              child: Padding(
                padding: EdgeInsets.all(20),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      FIcons.heart,
                      size: 64,
                      color: theme.colors.mutedForeground,
                    ),
                    gapH16,
                    Text(
                      'Нет избранных партнеров',
                      style: theme.typography.lg.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    gapH8,
                    Text(
                      'Добавьте партнеров в избранное, чтобы они отображались здесь',
                      style: theme.typography.sm.copyWith(
                        color: theme.colors.mutedForeground,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            );
          }

          return ListView.builder(
            padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            itemCount: partnerList.length,
            itemBuilder: (context, index) => PartnerCard(
              partner: partnerList[index],
              onTap: () => PartnerDetailRouteData(
                partnerID: partnerList[index].id,
              ).push(context),
            ),
          );
        });
  }
}
