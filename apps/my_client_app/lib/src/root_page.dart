import 'package:core_theme/core_theme.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class RootPage extends StatelessWidget {
  const RootPage({super.key, required this.navigationShell});
  final StatefulNavigationShell navigationShell;

  static const double _iconSize = 24.0;
  static const double _selectedIconSize = 28.0;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return Scaffold(
      body: navigationShell,
      bottomNavigationBar: NavigationBar(
        onDestinationSelected: (value) {
          navigationShell.goBranch(value,
              initialLocation: value == navigationShell.currentIndex);
        },
        selectedIndex: navigationShell.currentIndex,
        // backgroundColor: theme.colorScheme.surface,
        // surfaceTintColor: theme.colorScheme.surfaceTint,
        // indicatorColor: theme.colorScheme.secondaryContainer,
        // shadowColor: theme.colorScheme.shadow,
        shadowColor: theme.colors.primary,
        labelTextStyle: WidgetStateProperty.resolveWith<TextStyle?>((states) {
          if (states.contains(WidgetState.selected)) {
            return theme.typography.sm.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colors.primary,
            );
          }
          return theme.typography.sm.copyWith(
            fontWeight: FontWeight.w400,
            color: theme.colors.mutedForeground,
          );
          // return TextStyle(
          //   color: theme.colorScheme.mutedForeground,
          //   fontWeight: FontWeight.w500,
          // );
        }),
        height: 64,
        //elevation: 0.3,
        labelBehavior: NavigationDestinationLabelBehavior.alwaysShow,
        animationDuration: const Duration(milliseconds: 300),
        destinations: [
          NavigationDestination(
            icon: Icon(
              FIcons.house,
              size: _iconSize,
              color: theme.colors.mutedForeground,
            ),
            selectedIcon: Icon(
              FIcons.house,
              size: _selectedIconSize,
              color: theme.colors.primary,
            ),
            label: 'Главная',
          ),
          NavigationDestination(
            icon: Icon(
              FIcons.listCheck,
              size: _iconSize,
              color: theme.colors.mutedForeground,
            ),
            selectedIcon: Icon(
              FIcons.listCheck,
              size: _selectedIconSize,
              color: theme.colors.primary,
            ),
            label: 'Заявки',
          ),
          NavigationDestination(
            icon: Icon(
              FIcons.heart,
              size: _iconSize,
              color: theme.colors.mutedForeground,
            ),
            selectedIcon: Icon(
              FIcons.heart,
              size: _selectedIconSize,
              color: theme.colors.primary,
            ),
            label: 'Избранное',
          ),
          NavigationDestination(
            icon: Icon(
              FIcons.user,
              size: _iconSize,
              color: theme.colors.mutedForeground,
            ),
            selectedIcon: Icon(
              FIcons.user,
              size: _selectedIconSize,
              color: theme.colors.primary,
            ),
            label: 'Меню',
          ),
        ],
      ),
    );
  }
}
