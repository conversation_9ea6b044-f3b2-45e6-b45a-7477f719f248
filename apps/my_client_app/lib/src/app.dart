import 'package:core_theme/core_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:my_client_app/src/routing/app_router.dart';
import 'package:my_client_app/src/routing/app_startup.dart';

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appStartup = ref.watch(appStartupProvider);
    final themeMode = ref.watch(currentThemeModeProvider);
    final foruiTheme = ref.watch(foruiThemeDataProvider);
    final goRouter = ref.watch(goRouterProvider);

    return appStartup.when(
      data: (_) => MaterialApp.router(
        routerConfig: goRouter,
        title: 'My Client App',
        debugShowCheckedModeBanner: false,
        theme: foruiTheme.toApproximateMaterialTheme(),
        themeMode: themeMode,
        builder: (context, child) => FTheme(
          data: foruiTheme,
          child: child!,
        ),
      ),

      // *** error
      error: (error, stack) => MaterialApp(
        theme: foruiTheme.toApproximateMaterialTheme(),
        themeMode: themeMode,
        home: Scaffold(
          body: Center(
            child: Text(error.toString()),
          ),
        ),
        builder: (context, child) => FTheme(
          data: foruiTheme,
          child: child!,
        ),
      ),

      // *** loading
      loading: () => MaterialApp(
        theme: foruiTheme.toApproximateMaterialTheme(),
        themeMode: themeMode,
        home: Scaffold(
          body: Center(
            child: CircularProgressIndicator(),
          ),
        ),
        builder: (context, child) => FTheme(
          data: foruiTheme,
          child: child!,
        ),
      ),
    );

    // final userID = ref.watch(firebaseAuthRepositoryProvider).currentUser!.uid;
    // FcmTokenRepo().saveTokenToDatavase(userID);
    // final goRouter = ref.watch(goRouterProvider);
  }
}
