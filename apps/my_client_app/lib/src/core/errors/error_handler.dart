import 'package:firebase_auth/firebase_auth.dart';

class AppErrorHandler {
  // Методы для обработки ошибок Firebase Auth
  static String getFirebaseAuthErrorMessage(FirebaseAuthException error) {
    return switch (error.code) {
      'user-not-found' => 'Пользователь не найден',
      'wrong-password' => 'Неверный пароль',
      'invalid-phone-number' => 'Неверный формат номера телефона',
      'too-many-requests' => 'Слишком много запросов. Попробуйте позже',
      _ => error.message ?? 'Произошла неизвестная ошибка',
    };
  }

  // Методы для обработки ошибок Firestore
  static String getFirestoreErrorMessage(FirebaseException error) {
    return switch (error.code) {
      'permission-denied' => 'Нет доступа к этим данным',
      'not-found' => 'Запрашиваемые данные не найдены',
      _ => error.message ?? 'Произошла ошибка при работе с базой данных',
    };
  }

  // Общий метод для получения сообщения об ошибке
  static String getUserFriendlyMessage(Object error) {
    if (error is FirebaseAuthException) {
      return getFirebaseAuthErrorMessage(error);
    } else if (error is FirebaseException) {
      return getFirestoreErrorMessage(error);
    } else {
      return 'Произошла ошибка: ${error.toString()}';
    }
  }
}
