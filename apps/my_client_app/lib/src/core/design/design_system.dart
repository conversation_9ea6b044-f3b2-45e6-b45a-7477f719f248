import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// Константы дизайн-системы в стиле Airbnb
abstract class DesignSystem {
  // Размеры
  static const double maxContentWidth = 600;

  // Отступы
  static const double spacingXS = 4;
  static const double spacingS = 8;
  static const double spacingM = 16;
  static const double spacingL = 24;
  static const double spacingXL = 32;
  static const double spacingXXL = 48;

  // Скругления
  static const double radiusS = 8;
  static const double radiusM = 12;
  static const double radiusL = 16;
  static const double radiusXL = 24;

  // Анимация
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Curve animationCurve = Cubic(0.4, 0, 0.2, 1);

  // Тени
  static List<BoxShadow> get shadowS => [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.05),
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ];

  static List<BoxShadow> get shadowM => [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.05),
          blurRadius: 8,
          offset: const Offset(0, 4),
        ),
      ];

  static List<BoxShadow> get shadowL => [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.08),
          blurRadius: 16,
          offset: const Offset(0, 8),
        ),
      ];

  // Типографика
  static TextTheme getTextTheme(BuildContext context) {
    final baseTextTheme = Theme.of(context).textTheme;

    return TextTheme(
      displayLarge: GoogleFonts.montserrat(
        textStyle: baseTextTheme.displayLarge?.copyWith(
          fontWeight: FontWeight.bold,
          letterSpacing: -1.5,
        ),
      ),
      displayMedium: GoogleFonts.montserrat(
        textStyle: baseTextTheme.displayMedium?.copyWith(
          fontWeight: FontWeight.bold,
          letterSpacing: -0.5,
        ),
      ),
      displaySmall: GoogleFonts.montserrat(
        textStyle: baseTextTheme.displaySmall?.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
      headlineLarge: GoogleFonts.montserrat(
        textStyle: baseTextTheme.headlineLarge?.copyWith(
          fontWeight: FontWeight.w600,
          letterSpacing: 0.25,
        ),
      ),
      headlineMedium: GoogleFonts.montserrat(
        textStyle: baseTextTheme.headlineMedium?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      headlineSmall: GoogleFonts.montserrat(
        textStyle: baseTextTheme.headlineSmall?.copyWith(
          fontWeight: FontWeight.w600,
          letterSpacing: 0.15,
        ),
      ),
      titleLarge: GoogleFonts.montserrat(
        textStyle: baseTextTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.w600,
          letterSpacing: 0.15,
        ),
      ),
      titleMedium: GoogleFonts.montserrat(
        textStyle: baseTextTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
          letterSpacing: 0.1,
        ),
      ),
      titleSmall: GoogleFonts.montserrat(
        textStyle: baseTextTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.w600,
          letterSpacing: 0.1,
        ),
      ),
      bodyLarge: GoogleFonts.inter(
        textStyle: baseTextTheme.bodyLarge?.copyWith(
          letterSpacing: 0.5,
        ),
      ),
      bodyMedium: GoogleFonts.inter(
        textStyle: baseTextTheme.bodyMedium?.copyWith(
          letterSpacing: 0.25,
        ),
      ),
      bodySmall: GoogleFonts.inter(
        textStyle: baseTextTheme.bodySmall?.copyWith(
          letterSpacing: 0.4,
        ),
      ),
      labelLarge: GoogleFonts.inter(
        textStyle: baseTextTheme.labelLarge?.copyWith(
          fontWeight: FontWeight.w500,
          letterSpacing: 1.25,
        ),
      ),
      labelMedium: GoogleFonts.inter(
        textStyle: baseTextTheme.labelMedium?.copyWith(
          fontWeight: FontWeight.w500,
          letterSpacing: 0.4,
        ),
      ),
      labelSmall: GoogleFonts.inter(
        textStyle: baseTextTheme.labelSmall?.copyWith(
          letterSpacing: 1.5,
        ),
      ),
    );
  }
}
