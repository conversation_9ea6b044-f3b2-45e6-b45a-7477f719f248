import 'package:flutter/foundation.dart';

@immutable
class FCMException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;

  const FCMException(
    this.message, {
    this.code,
    this.originalError,
  });

  @override
  String toString() =>
      'FCMException: $message${code != null ? ' (code: $code)' : ''}';

  factory FCMException.tokenError([String? details]) => FCMException(
        'Не удалось получить FCM токен${details != null ? ': $details' : ''}',
        code: 'fcm_token_error',
      );

  factory FCMException.maxRetries() => const FCMException(
        'Превышено количество попыток получения токена',
        code: 'fcm_max_retries',
      );

  factory FCMException.permissionDenied() => const FCMException(
        'Отказано в разрешении на уведомления',
        code: 'fcm_permission_denied',
      );

  factory FCMException.notificationError([String? details]) => FCMException(
        'Ошибка отправки уведомления${details != null ? ': $details' : ''}',
        code: 'fcm_notification_error',
      );
}
