class FCMRetryPolicy {
  final int maxAttempts;
  final Duration initialDelay;
  final Duration maxDelay;

  const FCMRetryPolicy({
    this.maxAttempts = 3,
    this.initialDelay = const Duration(seconds: 1),
    this.maxDelay = const Duration(seconds: 10),
  });

  Duration getDelayForAttempt(int attempt) {
    if (attempt < 0) {
      throw ArgumentError('Attempt number cannot be negative');
    }

    // Экспоненциальное увеличение задержки
    final delay = initialDelay * (attempt + 1);

    // Не превышаем максимальную задержку
    return delay > maxDelay ? maxDelay : delay;
  }

  bool shouldRetry(int attempt) {
    return attempt < maxAttempts;
  }
}
