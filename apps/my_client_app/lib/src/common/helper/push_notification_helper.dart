import 'dart:io';

import 'package:core_logging/core_logging.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:my_client_app/src/common/exceptions/fcm_exception.dart';
import 'package:my_client_app/src/common/utils/fcm_retry_policy.dart';

@pragma('vm:entry-point')
Future<void> backgroudHandler(RemoteMessage message) async {
  try {
    TalkerService.talker
        .debug('Получено фоновое сообщение: ${message.messageId}');
    TalkerService.talker.debug('Данные: ${message.data}');
    if (message.notification != null) {
      TalkerService.talker.debug(
        'Уведомление: ${message.notification!.title} - ${message.notification!.body}',
      );
    }
  } catch (e) {
    TalkerService.talker.error('Ошибка в backgroudHandler: $e');
  }
}

class PushNotificationHelper {
  static final _retryPolicy = FCMRetryPolicy();
  static String fcmToken = '';

  static Future<void> initialized() async {
    try {
      // Инициализация уведомлений в зависимости от платформы
      if (Platform.isAndroid) {
        NotificationHelper.initialized();
        await _requestPermissionWithRetry();
      } else if (Platform.isIOS) {
        await _requestPermissionWithRetry();
      }

      // Настройка обработчиков сообщений
      FirebaseMessaging.onBackgroundMessage(backgroudHandler);
      await getDeviceTokenToSendNotification();

      await FirebaseMessaging.instance
          .setForegroundNotificationPresentationOptions(
        alert: true,
        badge: true,
        sound: true,
      );

      // Обработка сообщений в разных состояниях приложения
      _setupTerminatedStateHandler();
      _setupForegroundHandler();
      _setupBackgroundHandler();
    } catch (e) {
      TalkerService.talker.error('Ошибка инициализации FCM: $e');
      throw FCMException('Ошибка инициализации push-уведомлений',
          originalError: e);
    }
  }

  static Future<void> getDeviceTokenToSendNotification() async {
    for (int attempt = 0; attempt < _retryPolicy.maxAttempts; attempt++) {
      try {
        final token = await FirebaseMessaging.instance.getToken();
        if (token == null) {
          throw FCMException.tokenError('Токен не получен');
        }
        fcmToken = token;
        TalkerService.talker.debug('FCM токен получен: $token');
        return;
      } catch (e) {
        TalkerService.talker
            .error('Попытка ${attempt + 1}: Ошибка получения FCM токена: $e');
        if (_retryPolicy.shouldRetry(attempt)) {
          await Future.delayed(_retryPolicy.getDelayForAttempt(attempt));
        } else {
          throw FCMException.maxRetries();
        }
      }
    }
  }

  static Future<void> _requestPermissionWithRetry() async {
    for (int attempt = 0; attempt < _retryPolicy.maxAttempts; attempt++) {
      try {
        final settings = await FirebaseMessaging.instance.requestPermission();
        if (settings.authorizationStatus == AuthorizationStatus.authorized) {
          TalkerService.talker.debug('Разрешения на уведомления получены');
          return;
        } else {
          throw FCMException.permissionDenied();
        }
      } catch (e) {
        TalkerService.talker
            .error('Попытка ${attempt + 1}: Ошибка получения разрешений: $e');
        if (_retryPolicy.shouldRetry(attempt)) {
          await Future.delayed(_retryPolicy.getDelayForAttempt(attempt));
        } else {
          throw e;
        }
      }
    }
  }

  static void _setupTerminatedStateHandler() {
    FirebaseMessaging.instance.getInitialMessage().then((message) {
      if (message != null) {
        TalkerService.talker.debug(
          'Получено сообщение из terminated состояния: ${message.messageId}',
        );
      }
    });
  }

  static void _setupForegroundHandler() {
    FirebaseMessaging.onMessage.listen((message) {
      TalkerService.talker.debug(
        'Получено foreground сообщение: ${message.messageId}',
      );

      if (message.notification != null && Platform.isAndroid) {
        NotificationHelper.displayNotification(message);
      }
    });
  }

  static void _setupBackgroundHandler() {
    FirebaseMessaging.onMessageOpenedApp.listen((message) {
      TalkerService.talker.debug(
        'Получено background сообщение: ${message.messageId}',
      );
    });
  }
} // end PushNotificationHelper

class NotificationHelper {
  static final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  static void initialized() {
    try {
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      flutterLocalNotificationsPlugin.initialize(
        InitializationSettings(
          android: initializationSettingsAndroid,
        ),
        onDidReceiveBackgroundNotificationResponse: localBackgroundHandler,
        onDidReceiveNotificationResponse: localBackgroundHandler,
      );

      TalkerService.talker.debug('Локальные уведомления инициализированы');
    } catch (e) {
      TalkerService.talker
          .error('Ошибка инициализации локальных уведомлений: $e');
      throw FCMException(
        'Ошибка инициализации локальных уведомлений',
        originalError: e,
      );
    }
  }

  static Future<void> displayNotification(RemoteMessage message) async {
    try {
      if (message.notification == null) {
        throw FCMException.notificationError(
            'Отсутствует содержимое уведомления');
      }

      final id = DateTime.now().minute ~/ 1000;
      const notificationDetail = NotificationDetails(
        android: AndroidNotificationDetails(
          'push_notification',
          'push_notification_channelName',
          importance: Importance.max,
          priority: Priority.high,
        ),
      );

      await flutterLocalNotificationsPlugin.show(
        id,
        message.notification!.title,
        message.notification!.body,
        notificationDetail,
        payload: message.data['route'],
      );

      TalkerService.talker.debug(
        'Отображено локальное уведомление: ${message.notification!.title}',
      );
    } catch (e) {
      TalkerService.talker.error('Ошибка отображения уведомления: $e');
      throw FCMException.notificationError(e.toString());
    }
  }
}

Future<void> localBackgroundHandler(NotificationResponse data) async {
  try {
    TalkerService.talker.debug(
      'Получен отклик на локальное уведомление: ${data.payload}',
    );
  } catch (e) {
    TalkerService.talker.error('Ошибка в localBackgroundHandler: $e');
  }
}
