import 'package:flutter/material.dart';

class NavigationItem {
  final IconData icon;
  final IconData activeIcon;
  final String label;
  final String? badge;

  const NavigationItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
    this.badge,
  });
}

// Предустановленные элементы навигации для приложения
class AppNavigationItems {
  static const List<NavigationItem> defaultItems = [
    NavigationItem(
      icon: Icons.home_outlined,
      activeIcon: Icons.home,
      label: 'Главная',
    ),
    NavigationItem(
      icon: Icons.description_outlined,
      activeIcon: Icons.description,
      label: 'Заявки',
    ),
    NavigationItem(
      icon: Icons.favorite_border,
      activeIcon: Icons.favorite,
      label: 'Избранное',
    ),
    NavigationItem(
      icon: Icons.person_outline,
      activeIcon: Icons.person,
      label: 'Меню',
    ),
  ];
}
