import 'dart:async';
import 'package:flutter/foundation.dart';

class GoRouterRefreshStream<T> extends ChangeNotifier {
  GoRouterRefreshStream(Stream<T> stream) {
    notifyListeners();
    _subscription = stream.asBroadcastStream().listen(
      (T _) => notifyListeners(),
      onError: (error, stackTrace) {
        debugPrint('Navigation stream error: $error');
        debugPrint('Stack trace: $stackTrace');
      },
    );
  }

  late final StreamSubscription<T> _subscription;

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }
}
