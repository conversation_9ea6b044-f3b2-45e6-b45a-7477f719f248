import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

// Import all screen widgets
import 'package:my_client_app/src/features/home/<USER>';
import 'package:my_client_app/src/features/requests/request_page.dart';
import 'package:my_client_app/src/features/favorite/favorite_page.dart';
import 'package:my_client_app/src/features/menu/menu_page.dart';
import 'package:my_client_app/src/features/menu/settings/settings_page.dart';
import 'package:my_client_app/src/features/menu/about/about_page.dart';
import 'package:my_client_app/src/features/menu/help/help_page.dart';
import 'package:my_client_app/src/features/authentication/phone_signin_screen.dart';
import 'package:my_client_app/src/features/authentication/otp_verify_screen.dart';
import 'package:my_client_app/src/features/home/<USER>/category_products/category_products_page.dart';
import 'package:my_client_app/src/features/productDetail/product_detail_page.dart';
import 'package:my_client_app/src/features/partner/partner_detail_page.dart';
import 'package:my_client_app/src/features/request_detail/request_detail_screen.dart';
import 'package:my_client_app/src/features/menu/profile_detail/profile_page.dart';
import 'package:my_client_app/src/root_page.dart';

// Import data types
import 'package:core_domain/core_domain.dart';
import 'package:my_client_app/src/features/productDetail/product_detail_args.dart';
import 'package:my_client_app/src/features/home/<USER>/category_product_list_args.dart';

part 'typed_routes.g.dart';

// =============================================================================
// MAIN SHELL ROUTE
// =============================================================================

@TypedStatefulShellRoute<AppShellRouteData>(
  branches: <TypedStatefulShellBranch<StatefulShellBranchData>>[
    TypedStatefulShellBranch<HomeBranchData>(
      routes: <TypedRoute<RouteData>>[
        TypedGoRoute<HomeRouteData>(path: '/home'),
      ],
    ),
    TypedStatefulShellBranch<RequestBranchData>(
      routes: <TypedRoute<RouteData>>[
        TypedGoRoute<RequestRouteData>(path: '/request'),
      ],
    ),
    TypedStatefulShellBranch<FavoriteBranchData>(
      routes: <TypedRoute<RouteData>>[
        TypedGoRoute<FavoriteRouteData>(path: '/favorite'),
      ],
    ),
    TypedStatefulShellBranch<MenuBranchData>(
      routes: <TypedRoute<RouteData>>[
        TypedGoRoute<MenuRouteData>(path: '/account'),
      ],
    ),
  ],
)
class AppShellRouteData extends StatefulShellRouteData {
  const AppShellRouteData();

  @override
  Widget builder(
    BuildContext context,
    GoRouterState state,
    StatefulNavigationShell navigationShell,
  ) {
    return RootPage(navigationShell: navigationShell);
  }
}

// =============================================================================
// BRANCH DATA CLASSES
// =============================================================================

class HomeBranchData extends StatefulShellBranchData {
  const HomeBranchData();
}

class RequestBranchData extends StatefulShellBranchData {
  const RequestBranchData();
}

class FavoriteBranchData extends StatefulShellBranchData {
  const FavoriteBranchData();
}

class MenuBranchData extends StatefulShellBranchData {
  const MenuBranchData();
}

// =============================================================================
// MAIN BRANCH ROUTES
// =============================================================================

class HomeRouteData extends GoRouteData {
  const HomeRouteData();
  @override
  Widget build(BuildContext context, GoRouterState state) => HomePage();
}

class RequestRouteData extends GoRouteData {
  const RequestRouteData();
  @override
  Widget build(BuildContext context, GoRouterState state) => RequestPage();
}

class FavoriteRouteData extends GoRouteData {
  const FavoriteRouteData();
  @override
  Widget build(BuildContext context, GoRouterState state) => FavoritePage();
}

class MenuRouteData extends GoRouteData {
  const MenuRouteData();
  @override
  Widget build(BuildContext context, GoRouterState state) => MenuPage();
}

// =============================================================================
// SIMPLE ROUTES
// =============================================================================

@TypedGoRoute<SettingsRouteData>(path: '/settings')
class SettingsRouteData extends GoRouteData {
  const SettingsRouteData();
  @override
  Widget build(BuildContext context, GoRouterState state) =>
      const SettingsPage();
}

@TypedGoRoute<AboutRouteData>(path: '/about')
class AboutRouteData extends GoRouteData {
  const AboutRouteData();
  @override
  Widget build(BuildContext context, GoRouterState state) => const AboutPage();
}

@TypedGoRoute<HelpRouteData>(path: '/help')
class HelpRouteData extends GoRouteData {
  const HelpRouteData();
  @override
  Widget build(BuildContext context, GoRouterState state) => const HelpPage();
}

@TypedGoRoute<PhoneSignInRouteData>(path: '/phoneSignIn')
class PhoneSignInRouteData extends GoRouteData {
  const PhoneSignInRouteData();
  @override
  Widget build(BuildContext context, GoRouterState state) =>
      PhoneSignInScreen();
}

@TypedGoRoute<OtpVerifyRouteData>(path: '/otpVerify')
class OtpVerifyRouteData extends GoRouteData {
  const OtpVerifyRouteData();
  @override
  Widget build(BuildContext context, GoRouterState state) => OTPVerifyScreen();
}

@TypedGoRoute<CategoryProductListRouteData>(path: '/categoryProductList')
class CategoryProductListRouteData extends GoRouteData {
  const CategoryProductListRouteData({required this.$extra});

  final CategoryProductListArgs $extra;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return CategoryProductListPage();
  }
}

@TypedGoRoute<ProfileDetailRouteData>(path: '/profileDetail')
class ProfileDetailRouteData extends GoRouteData {
  const ProfileDetailRouteData();
  @override
  Widget build(BuildContext context, GoRouterState state) => ProfilePage();
}

// =============================================================================
// ROUTES WITH PARAMETERS
// =============================================================================

@TypedGoRoute<PartnerDetailRouteData>(path: '/partnerDetail/:partnerID')
class PartnerDetailRouteData extends GoRouteData {
  const PartnerDetailRouteData({required this.partnerID});

  final String partnerID;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return PartnerDetailPage(partnerID: partnerID);
  }
}

@TypedGoRoute<ProductDetailRouteData>(path: '/productDetail')
class ProductDetailRouteData extends GoRouteData {
  const ProductDetailRouteData({required this.$extra});

  final ProductDetailArgs $extra;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return ProductDetailsPage(
      product: $extra.product,
      hidePartnerField: $extra.hidePartnerField,
    );
  }
}

@TypedGoRoute<RequestDetailRouteData>(path: '/requestDetail')
class RequestDetailRouteData extends GoRouteData {
  const RequestDetailRouteData({required this.$extra});

  final Request $extra;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return RequestDetailScreen(request1: $extra);
  }
}

