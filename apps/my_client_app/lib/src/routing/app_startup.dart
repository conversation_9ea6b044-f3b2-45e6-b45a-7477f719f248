import 'dart:developer';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:my_client_app/src/common/helper/push_notification_helper.dart';
import 'package:my_client_app/src/data/fcm_token_repo.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:core_data/core_data.dart';
part 'app_startup.g.dart';

@riverpod
FutureOr<void> appStartup(Ref ref) async {
  try {
    // Инициализируем уведомления
    await PushNotificationHelper.initialized();

    // Если пользователь авторизован - сохраняем токен
    final currentUser = ref.watch(firebaseAuthRepositoryProvider).currentUser;
    if (currentUser != null) {
      await FcmTokenRepo().saveTokenToDatavase(currentUser.uid);
    }
  } catch (e) {
    log('Ошибка при инициализации приложения: $e');
    rethrow;
  }
}
