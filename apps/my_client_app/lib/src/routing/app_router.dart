import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:my_client_app/main.dart';
import 'package:my_client_app/src/routing/go_router_refresh_stream.dart';
import 'package:my_client_app/src/routing/typed_routes.dart';
import 'package:core_data/core_data.dart';


final goRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
      navigatorKey: navigatorKey,
      initialLocation: '/home',
      debugLogDiagnostics: true,
      redirect: (context, state) {
        final isLoggedIn =
            ref.watch(firebaseAuthRepositoryProvider).currentUser;
        if (isLoggedIn != null) {
          if (state.uri.path == '/otpVerify') {
            return '/home';
          }
          if (state.uri.path == '/phoneSignIn') {
            return '/home';
          }
        } else {
          if (state.uri.path.startsWith('/home') ||
              state.uri.path == '/account') {
            return '/phoneSignIn';
          }
        }
        return null;
      },
      refreshListenable: GoRouterRefreshStream<User?>(
          ref.watch(firebaseAuthRepositoryProvider).authStateChanges()),
      routes: $appRoutes);
});
