import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:intl/intl.dart';
import 'package:my_client_app/firebase_options.dart';
import 'package:my_client_app/src/app.dart';
import 'package:my_client_app/src/common/helper/push_notification_helper.dart';
import 'package:core_logging/core_logging.dart'; // Updated import
import 'package:talker_riverpod_logger/talker_riverpod_logger.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  //   Internationalization
  Intl.defaultLocale = 'ru_RU';
  initializeDateFormatting('ru_RU', null);

  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  TalkerService.talker.debug('Talker started');
  await PushNotificationHelper.initialized();
  runApp(ProviderScope(
    observers: [TalkerRiverpodObserver(talker: TalkerService.talker)],
    child: const MyApp(),
  ));
}
