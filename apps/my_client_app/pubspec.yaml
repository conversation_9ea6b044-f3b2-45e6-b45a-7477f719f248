name: my_client_app
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+3

environment:
  sdk: '>=3.3.4 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  core_utils:
    path: ../../packages/core_utils
  core_domain:
    path: ../../packages/core_domain
  core_data:
    path: ../../packages/core_data
  core_logging:
    path: ../../packages/core_logging
  core_ui:
    path: ../../packages/core_ui
  core_theme:
    path: ../../packages/core_theme  
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  cloud_firestore: ^5.5.0
  go_router: ^15.1.2
  flutter_riverpod: ^2.5.1
  riverpod_annotation: ^2.3.5
  flutter_hooks: ^0.21.2
  hooks_riverpod: ^2.5.1
  google_fonts: ^6.2.1
  # talker_flutter is now included via core_logging
  talker_riverpod_logger: ^4.5.2
  firebase_auth: ^5.3.3
  firebase_core: ^3.8.0
  intl_phone_number_input: ^0.7.4
  pinput: ^5.0.0
  uuid: ^4.4.0
  image_picker: ^1.1.2
  image_cropper: ^8.0.2
  firebase_storage: ^12.3.6
  freezed_annotation: ^2.4.4
  json_annotation: ^4.9.0
  font_awesome_flutter: ^10.8.0
  flex_color_scheme: ^8.0.1
  firebase_messaging: ^15.1.5
  flutter_local_notifications: ^18.0.1
  url_launcher: ^6.2.5
  package_info_plus: ^8.3.0
  cached_network_image: ^3.3.1
  # Removed skeletonizer due to Canvas API compatibility issues
  intl: 0.20.2
  forui: ^0.12.0
  go_router_builder: ^2.9.0


dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  riverpod_generator: ^2.6.3
  build_runner: ^2.4.13
  custom_lint: ^0.7.0
  riverpod_lint: ^2.6.3
  freezed: ^2.5.7
  json_serializable: ^6.8.0


# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  assets:
    - shorebird.yaml
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
