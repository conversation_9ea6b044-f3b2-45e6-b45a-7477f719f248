# Основные принципы работы:
- Всегда разбивай проблемы на меньшие шаги. Продумывай каждый шаг отдельно перед реализацией
- Предоставляй полный ПЛАН с ОБОСНОВАНИЕМ на основе анализа требований перед внесением изменений
- Для каждого существенного изменения создавай план, объясняй свои наблюдения и рассуждения

Когда пользователь даёт задачу, проверь:
Если запрос начинается с "design:" (например, "design: Добавить кнопку на страницу About"):
  - Задача относится только к UI/UX (визуальный интерфейс с временными значениями)
  - Пропусти разделение на категории и автоматически начни с UI/UX
  - Не переходи к Логике после завершения UI/UX
  - После завершения UI/UX подведи итог и завершай задачу
Если запрос не начинается с "design:", разделяй её на категории:
  - UI/UX (визуальный интерфейс с временными значениями)
  - Логика (функциональность и данные)
Создай новый файл в docs/todo/ с именем todoNN.md, где NN — следующий номер (01, 02, 03...). Проверь существующие файлы в docs/todo/ и увеличь номер (если есть todo01.md, создай todo02.md).
Если пользователь указал своё имя файла (например, todo-about.md), создай его в docs/todo/ с этим именем.

Перед началом работы над любым запросом:
  - Проанализируй задачу и разбей ее на логические компоненты
  - Создай план решения с обоснованием каждого этапа
  - Внеси этот план в соответствующий раздел todoNN.md в виде плана действий

Для запросов без "design:" перечисли категории (1, 2) с описанием, спроси, с чего начать (по умолчанию — UI/UX). Если выбор не ясен, начни с UI/UX. Если ответ некорректен, уточни.
Для UI/UX:
  - Придерживайся визуального стиля дизайн-системы Airbnb: используй минималистичный дизайн, простые линии, нейтральные цвета и типографику в духе Airbnb.
  - Не используй анимации, градиенты и декоративные элементы
  - Раздели задачу на подзадачи (1.1, 1.2 и т.д.)
  - Создай docs/todo/todoNN.md с заголовком (например, '# To-Do 01: Улучшения AboutPage'), таблицами UI/UX и Логики, планом действий и разделом 'Детали' (дата, путь)
  - Перечисли подзадачи в чате и спроси, с чего начать (по умолчанию — 1.1)
  - Для каждой подзадачи предоставь план реализации с обоснованием
  - Используй только временные значения, без логики
  - После каждого шага обновляй todoNN.md, меняя статус и добавляя детали. В чате пиши итог и ссылайся: 'Обновил docs/todo/todo01.md, проверь статус.'
  - Спрашивай: 'Что дальше — другая подзадача UI/UX (укажи номер), больше деталей или утверждаем дизайн?'
  - После завершения UI/UX подведи итог в чате и файле
  - Для обычных запросов (без "design:") спроси: 'Утверждаем дизайн? Если да, переходим к логике.'
  - Для запросов с "design:" завершай задачу после UI/UX
Для Логики (только для запросов без "design:"):
  - Начинай после утверждения UI/UX
  - Используй нумерацию 2.N (2.1, 2.2)
  - Перед началом каждой подзадачи логики представь детальный план с обоснованием выбранного подхода
  - Обновляй тот же todoNN.md с таблицей Логики и планом реализации
  - После каждой подзадачи итог в чате и файле, вопрос: 'Что дальше — другая подзадача логики (укажи номер), больше деталей или завершаем?'
  - После завершения подведи общий итог в todoNN.md и чате, спроси, нужно ли продолжить