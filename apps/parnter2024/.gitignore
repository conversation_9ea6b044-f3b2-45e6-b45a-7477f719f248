# See https://www.dartlang.org/guides/libraries/private-files

# Files and directories created by pub
.dart_tool/
.packages
build/
pubspec.lock

# Generated files
**.freezed.dart
**.g.dart
**.config.dart
custom_lint.log

# Flutter/Dart specific
.flutter-plugins
.flutter-plugins-dependencies
*.dart.js
*.info.json      # Produced by the --dump-info flag.
*.js             # When generated by dart2js. Don't specify *.js if your
                 # project includes source files written in JavaScript.
*.js_
*.js.deps
*.js.map

# Environment files
.env*

# Generated platform-specific files
**/GeneratedPluginRegistrant.*
**/Flutter/ephemeral/
**/Flutter/Generated.xcconfig
**/Runner/GeneratedPluginRegistrant.*

# Documentation
doc/api/

android/app/.cxx/

# Keystore files (SECURITY - DO NOT COMMIT)
*.jks
*.jks.*
*.keystore
keystore.properties
