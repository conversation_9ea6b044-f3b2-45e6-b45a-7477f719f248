# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Context

This is the partner/seller app (`parnter2024`) within a Flutter monorepo. It shares code with two other apps:
- `my_client_app` - Customer mobile app
- `eventapp_client_web` - Customer web catalog

The app is currently being integrated into the monorepo architecture, with ongoing migration to shared packages.

## Commands

### Development
```bash
# Run the partner app
flutter run

# Run with verbose logging
flutter run --dart-define=TALKER_ENABLED=true

# Code generation after model/provider/route changes
dart run build_runner build --delete-conflicting-outputs

# Watch mode for automatic generation
dart run build_runner watch --delete-conflicting-outputs
```

### Code Quality
```bash
# Analyze code
flutter analyze

# Custom Riverpod lints
dart run custom_lint

# Format code
dart format .
```

### Build & Release
```bash
# Android APK
flutter build apk

# Android App Bundle
flutter build appbundle

# Deploy OTA updates
shorebird release android
```

## Architecture

### Repository Integration Status

#### ✅ Migrated to core_data:
- CategoryRepository
- ClientRepository (extended with `fetchClientListById`)
- EventRepository (extended with partner-specific methods)
- PartnerRepository (extended with `partnerFuture`, `partnerStream`, `updatePartner`)
- ProductRepository (extended with CRUD operations, image upload, partner filtering)
- RequestRepository (extended with partner-filtered streams, status-based filtering)

#### ⚠️ Local Implementations:
- StorageRepository - Partner/product image uploads (already marked as completed in plan)

### Key Patterns

1. **Partner Auto-Inject Providers**: Use wrapper providers in `app/common/providers/partner_auto_inject_providers.dart` that automatically inject the current partner ID:
```dart
// Don't use core_data providers directly
// Use these wrappers instead:
ref.watch(partnerEventListStreamProvider)  // Auto-filters by current partner
ref.watch(partnerFetchEventListProvider)   // Auto-filters by current partner
ref.watch(partnerProductListStreamProvider)  // Auto-filters by current partner
ref.watch(partnerSelfStreamProvider)  // Gets current partner data
```

2. **State Management**: Always use `NotifierMounted` mixin:
```dart
class SomeController extends _$SomeController with NotifierMounted {
  @override
  FutureOr<void> build() {
    ref.onDispose(setUnmounted);
  }
  
  Future<void> someMethod() async {
    state = const AsyncLoading();
    final result = await AsyncValue.guard(() => someOperation());
    if (mounted) {  // Critical check
      state = result;
    }
  }
}
```

3. **Theme Access**: Use ForUI pattern:
```dart
context.theme.colorScheme.primary
context.theme.typography.base
// Never: Theme.of(context)
```

## Critical Security Issues

### 🚨 Immediate Actions Required:
1. **Exposed Passwords**: `android/app/build.gradle` contains hardcoded keystore passwords
2. **Missing Security Rules**: No `firestore.rules` or `storage.rules` files
3. **App Check**: Not initialized despite being a dependency

## Known Issues

### High Priority:
- 0% test coverage
- Repository singletons (FirebaseEventRepo, FirebaseProductRepo)
- Mixed UI libraries (Material vs ForUI)
- No offline support

### Code Quality:
- 30+ linter warnings
- Empty directories to remove
- Duplicate widgets (BottomButtonField, InfoTile variants)
- File naming inconsistencies

## Integration Notes

When working with shared packages:
- Models: All use `core_domain` ✅
- Repositories: Mix of `core_data` and local implementations
- UI: Use `core_ui` components where available
- Navigation: Type-safe routes with GoRouter
- Theme: ForUI with `context.theme`

## Firebase Configuration
- Project ID: `event-app-b25e1`
- Android only (iOS not configured)
- Shorebird App ID: `51965aea-f13a-4492-a28a-5ea085593399`