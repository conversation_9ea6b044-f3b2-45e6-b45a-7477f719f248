import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'base_tile_style.dart';

class PrimaryTileStyle implements BaseTileStyleProvider {
  @override
  FTileStyle provideTileStyle({
    required FThemeData theme,
    required FTileContentStyle newContentStyle,
    FWidgetStateMap<Border>? border,
  }) {
    return FTileStyle(
      pressable: theme.tileGroupStyle.tileStyle.pressable.copyWith(
          contentStyle: newContentStyle,
          border: FWidgetStateMap({
            WidgetState.focused: Border.all(
              width: theme.style.borderWidth,
              color: Colors.transparent,
            ),
            WidgetState.any: Border.all(
              width: theme.style.borderWidth,
              color: Colors.transparent,
            ),
          })),
      unpressable: theme.tileGroupStyle.tileStyle.unpressable.copyWith(
          contentStyle: newContentStyle,
          border: FWidgetStateMap({
            WidgetState.focused: Border.all(
              width: theme.style.borderWidth,
              color: Colors.transparent,
            ),
            WidgetState.any: Border.all(
              width: theme.style.borderWidth,
              color: Colors.transparent,
            ),
          })),
    );
  }
}

class CustomContentStyle {
  FTileContentStyle get({required FThemeData theme}) {
    return theme.tileGroupStyle.tileStyle.pressable.contentStyle.copyWith(
      padding: EdgeInsets.fromLTRB(5, 5, 0, 10),
      titleTextStyle: FWidgetStateMap({
        WidgetState.disabled: theme.typography.sm.copyWith(
          color: theme.colors.disable(theme.colors.primary),
        ),
        WidgetState.any: theme.typography.sm,
      }),
    );
  }
}
