import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:parnter2024/app/common/services/push_notification_helper.dart';
import 'package:parnter2024/app/common/services/talker_service.dart';
import 'package:parnter2024/app/routes/app_router.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:parnter2024/firebase_options.dart';
import 'package:talker_riverpod_logger/talker_riverpod_logger.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await initializeDateFormatting('ru_RU', null);
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Инициализация уведомлений при старте приложения
  await PushNotificationHelper.initialized();

  runApp(ProviderScope(observers: [
    TalkerRiverpodObserver(
      talker: TalkerService.talker,
      settings: TalkerRiverpodLoggerSettings(
        printStateFullData: true,
      ),
    ),
  ], child: MyApp()));
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});
  //final theme = zincLight;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MaterialApp.router(
      localizationsDelegates: FLocalizations.localizationsDelegates,
      supportedLocales: FLocalizations.supportedLocales,
      builder: (_, child) => FTheme(data: FThemes.zinc.light, child: child!),
      theme: FThemes.zinc.light.toApproximateMaterialTheme(),
      title: 'Flutter Demo',
      debugShowCheckedModeBanner: false,
      // locale: const Locale('ru', 'RU'),
      // supportedLocales: const [
      //   Locale('ru', 'RU'),
      // ],
      // localizationsDelegates: const [
      //   GlobalMaterialLocalizations.delegate,
      //   GlobalCupertinoLocalizations.delegate,
      //   GlobalWidgetsLocalizations.delegate
      // ],
      routerConfig: ref.watch(goRouterProvider),
    );
  }
}
