import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:core_domain/core_domain.dart';
import 'package:parnter2024/app/routes/typed_routes.dart';

// Путь к локальному изображению-заполнителю
const String placeholderImagePath = 'assets/images/placeholder.jpg';

class RequestClientCard extends StatelessWidget {
  const RequestClientCard(
      {super.key, required this.imageURL, required this.client});
  final String imageURL;
  final Client client;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return FCard(
      title: Row(
        children: [
          Icon(
            FIcons.user,
            size: 18,
            color: theme.colors.primary,
          ),
          SizedBox(width: 8),
          Text('Информация о пользователе'),
        ],
      ),
      style: theme.cardStyle.copyWith(
        decoration: theme.cardStyle.decoration.copyWith(
          border: Border.all(color: theme.colors.border),
        ),
      ),
      child: InkWell(
        onTap: () {
          ClientDetailRoute(id: client.id).push(context);
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            children: [
              CircleAvatar(
                radius: 24,
                backgroundImage: imageURL.isNotEmpty
                    ? NetworkImage(imageURL) as ImageProvider
                    : AssetImage(placeholderImagePath),
              ),
              SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      client.name ?? 'Пользователь',
                      style: theme.typography.base.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      client.phoneNumber,
                      style: theme.typography.sm.copyWith(
                        color: theme.colors.mutedForeground,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                FIcons.chevronRight,
                size: 20,
                color: theme.colors.mutedForeground,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
