import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:core_ui/core_ui.dart';
import 'package:parnter2024/app/features/event_details/view/widgets/my_list_tile.dart';
import 'package:core_data/core_data.dart';

class ClientDetailPage extends ConsumerWidget {
  const ClientDetailPage({super.key, required this.clientId});
  final String clientId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final clientValue = ref.watch(fetchClientByIdProvider(clientId));
    return Scaffold(
        appBar: AppBar(title: Text('Детали клиента')),
        body: AsyncValueWidget(
            value: clientValue,
            data: (client) {
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  children: [
                    CircleAvatar(
                      radius: 75,
                      backgroundImage: client.imageURL != null
                          ? NetworkImage(client.imageURL!)
                          : null,
                    ),
                    gapH12,
                    Text(
                      client.name ?? client.phoneNumber,
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    gapH48,
                    MyListTile(
                      iconData: Icons.phone_callback_outlined,
                      title: client.phoneNumber,
                    ),
                    MyListTile(
                      iconData: Icons.email_outlined,
                      title: client.email ?? 'Не указан',
                    ),
                  ],
                ),
              );
            }));
  }
}
