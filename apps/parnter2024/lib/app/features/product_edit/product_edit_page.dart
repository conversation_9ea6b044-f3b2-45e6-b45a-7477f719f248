import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:core_utils/core_utils.dart';
import 'package:parnter2024/app/common/my_form_text_field.dart';
import 'package:parnter2024/app/common/validators.dart';
import 'package:core_ui/core_ui.dart';
import 'package:parnter2024/app/features/product_edit/category_selection_editfield.dart';
import 'package:parnter2024/app/features/product_edit/controller/edit_product_controller.dart';
import 'package:parnter2024/app/features/product_edit/controller/edit_product_provider.dart';
import 'package:parnter2024/app/features/product_edit/widget/product_form_controller.dart';
import 'package:parnter2024/app/features/product_new/view/image_field.dart';
import 'package:core_domain/core_domain.dart' show Product, DurationType;
import 'package:parnter2024/app/utils/error_dialog.dart';

class EditProductPage extends HookConsumerWidget {
  const EditProductPage({super.key, required this.product});
  final Product product;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen(editProductControllerProvider, (_, next) {
      if (next.hasError) {
        errorDialog(context, next.error.toString());
      }
    });
    final formState = useProductForm(product);

    // 4. Side effects
    useEffect(() {
      Future.microtask(() {
        ref
            .read(productEditNotifierProvider.notifier)
            .init(product.caterogyIDList);
      });
      return null;
    }, const []);

    // 5. Event handlers
    void updateDurationType(DurationType? value) {
      if (value == null) return;
      formState.durationType.value = value;
    }

    Future<void> pickAndSetImage() async {
      final imagePath = await pickAndCropImage();
      if (imagePath != null) {
        formState.imageLocalPath.value = imagePath;
      }
    }

    void clearImage() {
      formState.imageLocalPath.value = null;
    }

    // Проверка наличия изображения
    bool hasImage() {
      return formState.imageLocalPath.value != null ||
          (formState.imageURL.value != null &&
              formState.imageURL.value!.isNotEmpty);
    }

    final controller = ref.watch(editProductControllerProvider);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Редактирование'),
        actions: [
          IconButton(
            onPressed: () async {
              final confirmed = await showDialog(
                  context: context,
                  builder: (context) {
                    return AlertDialog(
                      title: const Text('Подтверждение'),
                      content: const Text(
                          'Вы уверены, что хотите удалить этот продукт?'),
                      actions: [
                        TextButton(
                            onPressed: () {
                              context.pop(true);
                            },
                            child: Text('Удалить')),
                        TextButton(
                            onPressed: () {
                              context.pop(false);
                            },
                            child: Text('Отмена')),
                      ],
                    );
                  });
              if (confirmed == true) {
                ref
                    .read(editProductControllerProvider.notifier)
                    .deleteProduct(product);
              }
            },
            icon: Icon(Icons.delete_outline),
          )
        ],
      ),
      body: Column(
        children: [
          // Основное содержимое формы с прокруткой
          Expanded(
            child: Form(
              key: formState.formKey,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    ImageField(
                      imagePath: formState.imageLocalPath.value,
                      imageURL: formState.imageURL.value,
                      onCancelImage: formState.imageLocalPath.value != null
                          ? clearImage
                          : null,
                      onChangeImage: pickAndSetImage,
                    ),

                    MyTextFromField(
                      validator: validateString,
                      textController: formState.nameController,
                      labelName: 'Название *',
                    ),
                    MyTextFromField(
                      textController: formState.descController,
                      labelName: 'Описание',
                    ),

                    // Duration ***
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Expanded(
                          child: MyTextFromField(
                            validator: validateNumber,
                            keyboardType: TextInputType.number,
                            textController: formState.durationController,
                            labelName: 'Длительность *',
                          ),
                        ),

                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.only(top: 16),
                            child: Row(
                              children: [
                                // День
                                InkWell(
                                  onTap: () =>
                                      updateDurationType(DurationType.day),
                                  child: Row(
                                    children: [
                                      Radio<DurationType>(
                                        value: DurationType.day,
                                        groupValue:
                                            formState.durationType.value,
                                        onChanged: updateDurationType,
                                      ),
                                      const Text('день'),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 16),
                                // Час
                                InkWell(
                                  onTap: () =>
                                      updateDurationType(DurationType.hour),
                                  child: Row(
                                    children: [
                                      Radio<DurationType>(
                                        value: DurationType.hour,
                                        groupValue:
                                            formState.durationType.value,
                                        onChanged: updateDurationType,
                                      ),
                                      const Text('час'),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        //RadioChoice(),
                      ],
                    ),
                    MyTextFromField(
                      validator: validateNumber,
                      keyboardType: TextInputType.number,
                      textController: formState.slotController,
                      labelName: 'Количество мест *',
                    ),
                    MyTextFromField(
                      validator: validateNumber,
                      keyboardType: TextInputType.number,
                      textController: formState.priceController,
                      labelName: 'Цена *',
                    ),

                    CategorySelectionEditField(
                      product: product,
                    ),

                    // Добавляем отступ внизу для скролла под фиксированной кнопкой
                    const SizedBox(height: 60),
                  ],
                ),
              ),
            ),
          ),

          // Фиксированная кнопка внизу экрана
          FormSaveButton.fixed(
            isLoading: controller.isLoading,
            onPressed: () {
              final valid = formState.formKey.currentState?.validate() ?? false;
              // Проверяем наличие выбранных категорий
              final selectedCategories =
                  ref.read(productEditNotifierProvider).selectedCategories;
              final hasCategories = selectedCategories.isNotEmpty;

              if (!hasCategories) {
                // Показываем сообщение о необходимости выбрать категории
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Необходимо выбрать хотя бы одну категорию'),
                    duration: Duration(seconds: 2),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              }

              if (valid && hasCategories) {
                ref.read(editProductControllerProvider.notifier).updateProduct(
                      product: product,
                      formState: formState,
                    );
              }
            },
          ),
        ],
      ),
    );
  }
}
