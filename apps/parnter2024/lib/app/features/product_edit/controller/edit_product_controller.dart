import 'package:parnter2024/app/features/product_edit/controller/edit_product_provider.dart';
import 'package:parnter2024/app/features/product_edit/widget/product_form_controller.dart';
import 'package:core_domain/core_domain.dart' show Product;
import 'package:core_data/core_data.dart';
import 'package:parnter2024/app/routes/app_router.dart';
import 'package:core_utils/core_utils.dart' show NotifierMounted;
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'edit_product_controller.g.dart';

@riverpod
class EditProductController extends _$EditProductController
    with NotifierMounted {
  @override
  FutureOr<void> build() {
    ref.onDispose(setUnmounted);
  }

  Future<void> updateProduct({
    required Product product,
    required ProductFormState formState,
  }) async {
    // *** imageUrl
    var newImageUrl = product.imageUrl;

    if (formState.imageLocalPath.value != null) {
      try {
        state = AsyncLoading();
        newImageUrl = await ref.read(productRepoProvider).upLoadImage(
              formState.imageLocalPath.value!,
              product.id,
            );
      } catch (e, st) {
        if (mounted) {
          state = AsyncError(e, st);
          return;
        }
      }
    }

    state = const AsyncLoading();

    // *** newProduct
    final categoryList =
        ref.read(productEditNotifierProvider).selectedCategories;
    var newProduct = product.copyWith(
      name: formState.nameController.text,
      description: formState.descController.text,
      slots: int.parse(formState.slotController.text),
      duration: int.parse(formState.durationController.text),
      price: int.parse(formState.priceController.text),
      imageUrl: newImageUrl,
      durationType: formState.durationType.value,
      caterogyIDList: categoryList,
    );

    final value = await AsyncValue.guard(() async {
      await ref.read(productRepoProvider).updateProduct(newProduct);
    });

    final success = value.hasError == false;
    if (mounted) {
      state = value;
      if (success) {
        ref.read(goRouterProvider).pop();
      }
    }
  }

  // *** delete product
  Future<void> deleteProduct(Product product) async {
    state = AsyncLoading();

    if (product.eventRefList.isNotEmpty) {
      state = AsyncError(
          'У сервиса есть актинвые события, удалите их', StackTrace.current);
      return;
    }

    final value = await AsyncValue.guard(() async {
      await ref.read(productRepoProvider).deleteProduct(product);
    });

    if (mounted) {
      state = value;
      if (!value.hasError) {
        ref.read(goRouterProvider).pop();
      }
    }
  }
}
