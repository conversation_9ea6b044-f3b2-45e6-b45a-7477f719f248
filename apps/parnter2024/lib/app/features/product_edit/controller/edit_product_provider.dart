import 'package:parnter2024/app/features/product_edit/controller/product_edit_state.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'edit_product_provider.g.dart';

@riverpod
class ProductEditNotifier extends _$ProductEditNotifier {
  @override
  ProductEditState build() {
    return const ProductEditState();
  }

  void init(List<String> categoryIDList) {
    state = state.copyWith(selectedCategories: categoryIDList);
  }

  void updateCategoryList(String categoryID) {
    final selectedCategoires = state.selectedCategories;

    if (selectedCategoires.contains(categoryID)) {
      state = state.copyWith(selectedCategories: [
        ...selectedCategoires.where((id) => id != categoryID)
      ]);
    } else {
      state = state
          .copyWith(selectedCategories: [...selectedCategoires, categoryID]);
    }
  }
}

// @freezed
// class EditProductModel with _$EditProductModel {
//   const factory EditProductModel({
//     //@Default(DurationType.day) DurationType? durationType,
//     //String? imagePath,
//     @Default([]) List<String> selectedCategories,
//   }) = _EditProductState;
// }
