import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:core_domain/core_domain.dart' show Product, DurationType;

ProductFormState useProductForm(Product product) {
  final nameController = useTextEditingController(text: product.name);
  final descController = useTextEditingController(text: product.description);
  final slotController =
      useTextEditingController(text: product.slots.toString());
  final durationController =
      useTextEditingController(text: product.duration.toString());
  final priceController =
      useTextEditingController(text: product.price.toString());
  final durationType = useState<DurationType>(product.durationType);
  final imageURL = useState<String?>(product.imageUrl);
  final imageLocalPath = useState<String?>(null);
  final formKey = useMemoized(() => GlobalKey<FormState>(), []);

  return ProductFormState(
    nameController: nameController,
    descController: descController,
    slotController: slotController,
    durationController: durationController,
    priceController: priceController,
    durationType: durationType,
    imageURL: imageURL,
    imageLocalPath: imageLocalPath,
    formKey: formKey,
  );
}

class ProductFormState {
  final TextEditingController nameController;
  final TextEditingController descController;
  final TextEditingController slotController;
  final TextEditingController durationController;
  final TextEditingController priceController;
  final ValueNotifier<DurationType> durationType;
  final ValueNotifier<String?> imageURL;
  final ValueNotifier<String?> imageLocalPath;
  final GlobalKey<FormState> formKey;

  ProductFormState({
    required this.nameController,
    required this.descController,
    required this.slotController,
    required this.durationController,
    required this.priceController,
    required this.durationType,
    required this.imageURL,
    required this.imageLocalPath,
    required this.formKey,
  });

  bool validate() => formKey.currentState?.validate() ?? false;
  bool isValid() => validate();
}
