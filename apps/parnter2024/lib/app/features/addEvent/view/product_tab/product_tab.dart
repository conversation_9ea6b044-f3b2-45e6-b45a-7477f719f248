import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:core_ui/widgets/common/async_value_widget.dart';
import 'package:parnter2024/app/common/widgets/bottom_action_button.dart';
import 'package:parnter2024/app/features/addEvent/controller/add_event_provider.dart';
import 'package:parnter2024/app/features/addEvent/view/product_tab/product_empty_filed.dart';
import 'package:parnter2024/app/features/addEvent/view/product_tab/product_list.dart';
import 'package:parnter2024/app/common/providers/partner_auto_inject_providers.dart';
import 'package:parnter2024/app/routes/typed_routes.dart';

class ProductTab extends ConsumerWidget {
  const ProductTab({super.key, required this.tabController});

  final TabController tabController;

  void _onPressed(BuildContext context) {
    const NewProductRoute().push(context);
  }

  void _nextPage() {
    if (tabController.index < tabController.length - 1) {
      tabController.animateTo(tabController.index + 1);
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(addEventValueProvider);
    //final productListValue = ref.watch(fetchProductListProvider);
    final productListValue = ref.watch(partnerProductListStreamProvider);

    return AsyncValueWidget(
        value: productListValue,
        data: (productList) => Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                //Text(productList.length.toString()),
                //SizedBox(height: 20),
                Expanded(
                  child: productList.isNotEmpty
                      ? ProductList(
                          state: state,
                          productList: productList,
                          onPressed: () => _onPressed(context),
                        )
                      : ProductEmptyField(
                          onPressed: () => _onPressed(context),
                        ),
                ),

                // *** Button  Далее***
                BottomActionButton.simple(
                  //onPressed: () => tabController.index = 2,
                  onPressed: _nextPage,
                  text: 'Далее',
                ),
              ],
            ));
  }
}
