import 'package:flutter/material.dart';
import 'package:parnter2024/app/features/addEvent/view/widgets/item_empty_card.dart';

class ProductEmptyField extends StatelessWidget {
  const ProductEmptyField({
    super.key,
    required this.onPressed,
  });
  final void Function()? onPressed;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          ItemEmptyCard(
            //title: 'У вас пока нет услуг. \nСоздайте первую, чтобы продолжить.',
            title: 'У вас пока нет услуг',
            subtitle: 'Создайте первую, чтобы продолжить',
            icon: Icons.shopping_bag, // Добавляем иконку услуг
            onTap: onPressed,
          ),
          OutlinedButton.icon(
            icon: Icon(Icons.add),
            onPressed: onPressed,
            label: Text('Создать  услугу'),
          )
        ],
      ),
    );
  }
}
