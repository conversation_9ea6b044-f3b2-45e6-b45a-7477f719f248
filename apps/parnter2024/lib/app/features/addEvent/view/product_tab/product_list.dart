import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:parnter2024/app/features/addEvent/controller/add_event_provider.dart';
import 'package:parnter2024/app/features/addEvent/view/widgets/product_card.dart';
import 'package:core_domain/core_domain.dart' show Product;
import 'package:parnter2024/app/routes/typed_routes.dart';

class ProductList extends ConsumerWidget {
  const ProductList({
    super.key,
    required this.state,
    required this.productList,
    required this.onPressed,
  });

  final AddEventState state;
  final List<Product> productList;

  final void Function()? onPressed;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 0),
        child: ListView.builder(
          padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
          itemCount: productList.length + 1,
          itemBuilder: (context, index) {
            int length = productList.length;
            if (index < length) {
              return InkWell(
                onTap: () {
                  ref
                      .read(addEventValueProvider.notifier)
                      .selectProduct(productList[index]);
                },
                child: ProductCard(
                  product: productList[index],
                  isSelected:
                      state.selectedProduct?.id == productList[index].id,
                  onEditPressed: () {
                    EditProductRoute($extra: productList[index]).push(context);
                  },

                  //isSelected: true,
                ),
              );
            } else {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 20),
                child: FButton(
                  prefix: Icon(FIcons.plus),
                  style: FButtonStyle.outline,
                  onPress: onPressed,
                  child: Text('Добавить новую услугу'),
                ),
              );
            }
          },
        ));
  }
}
