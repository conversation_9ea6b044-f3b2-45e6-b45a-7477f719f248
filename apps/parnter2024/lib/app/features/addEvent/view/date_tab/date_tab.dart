import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:parnter2024/app/common/widgets/bottom_action_button.dart';
import 'package:parnter2024/app/features/addEvent/controller/add_event_provider.dart';
import 'package:parnter2024/app/features/addEvent/view/date_tab/calendar_event.dart';
import 'package:parnter2024/app/features/addEvent/view/date_tab/selected_date_list.dart';
import 'package:parnter2024/app/features/addEvent/view/widgets/item_empty_card.dart';

class DateTab extends ConsumerWidget {
  const DateTab({super.key, required this.tabController});

  final TabController tabController;

  void _nextPage() {
    if (tabController.index < tabController.length - 1) {
      tabController.animateTo(tabController.index + 1);
    }
  }

  void _showClearAllConfirmationBottomSheet(
      BuildContext context, WidgetRef ref) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext ctx) {
        return Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Text(
                'Подтверждение',
                style: Theme.of(context).textTheme.titleLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 15),
              Text(
                'Вы уверены, что хотите удалить все выбранные даты?\nЭто действие нельзя отменить.',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 25),
              FilledButton(
                child: const Text('Удалить все'),
                onPressed: () {
                  ref.read(addEventValueProvider.notifier).clearAllDates();
                  Navigator.pop(ctx);
                },
              ),
              const SizedBox(height: 10),
              OutlinedButton(
                child: const Text('Отмена'),
                onPressed: () {
                  Navigator.pop(ctx);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(addEventValueProvider);
    final controller = ref.watch(addEventValueProvider.notifier);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Expanded(
          child: SingleChildScrollView(
            child: Column(
              //crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                CalendarEvent(state: state, controller: controller),
                //const SizedBox(height: 16),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 30, horizontal: 20),
                  child: state.selectedDates.isEmpty
                      ? Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              child: ItemEmptyCard(
                                title: 'Нет выбранных дат',
                                subtitle:
                                    'Выберите одну или несколько дат в календаре выше.',
                                icon: Icons.calendar_today,
                              ),
                            ),
                          ],
                        )
                      : SelectedDateList(),
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
        BottomActionButton.simple(
          text: 'Далее',
          onPressed: state.selectedDates.isEmpty ? null : _nextPage,
        ),
      ],
    );
  }
}
