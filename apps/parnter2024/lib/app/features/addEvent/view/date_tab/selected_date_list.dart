import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:core_utils/core_utils.dart';
import 'package:parnter2024/app/features/addEvent/controller/add_event_provider.dart';
import 'package:parnter2024/theme/tile_styles/primary_tile_style.dart';

class SelectedDateList extends ConsumerWidget {
  const SelectedDateList({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = context.theme;
    final selectedDates = ref.watch(addEventValueProvider).selectedDates;

    // *** If no selected dates ***
    if (selectedDates.isEmpty) {
      return SizedBox.shrink();
    }

    // *** Tile group ***
    return FTileGroup.builder(
      style: theme.tileGroupStyle.copyWith(
        borderColor: Colors.transparent,
        //borderWidth: 0.1,
        childPadding: EdgeInsets.all(0),
        labelPadding: EdgeInsets.only(bottom: 5),
      ),
      divider: FTileDivider.none,
      label: Row(
        children: [
          Text('Выбранные даты (${selectedDates.length})'),
          Spacer(),
          InkWell(
            child: Text(
              'Удалить все',
              style: context.theme.typography.xs
                  .copyWith(color: theme.colors.mutedForeground),
            ),
            onTap: () =>
                ref.read(addEventValueProvider.notifier).clearAllDates(),
          ),
        ],
      ),
      count: selectedDates.length,
      tileBuilder: (context, index) {
        final date = selectedDates[index]; // Используем отсортированный список
        final dayOfWeek = date.shortDayOfWeek;
        final formattedDate = "${date.format('d MMMM', 'ru')} ($dayOfWeek)";

        return FTile(
          title: Text(formattedDate),
          style: PrimaryTileStyle().provideTileStyle(
            theme: theme,
            newContentStyle: CustomContentStyle().get(theme: theme),
          ),
          suffixIcon: InkWell(
            onTap: () =>
                ref.read(addEventValueProvider.notifier).removeDate(date),
            child: Icon(FIcons.trash2),
          ),
        );
      },
    );
  }
}
