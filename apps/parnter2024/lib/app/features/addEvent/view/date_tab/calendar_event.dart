import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:parnter2024/app/common/theme/calendar_styles.dart';
import 'package:parnter2024/app/features/addEvent/controller/add_event_provider.dart';
import 'package:table_calendar/table_calendar.dart';

class CalendarEvent extends StatelessWidget {
  const CalendarEvent({
    super.key,
    required this.state,
    required this.controller,
  });

  final AddEventState state;
  final AddEventValue controller;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    // final borderRadius = theme.style.borderRadius;
    // final colors = theme.colors;
    final calendarStyles = AppCalendarStyles(theme);
    // final defaultDecoration = BoxDecoration(
    //   shape: BoxShape.rectangle,
    //   borderRadius: borderRadius,
    // );
    // final todayDecoration = BoxDecoration(
    //   shape: BoxShape.rectangle,
    //   borderRadius: borderRadius,
    //   color: colors.border,
    // );
    // final selectedDecoration = BoxDecoration(
    //   shape: BoxShape.rectangle,
    //   borderRadius: borderRadius,
    //   color: colors.primary,
    // );
    return TableCalendar(
      locale: 'ru_RU',
      focusedDay: state.focusedDay,
      firstDay: DateTime.utc(2024, 1, 1),
      lastDay: DateTime.utc(2025, 12, 31),
      selectedDayPredicate: (day) {
        return state.selectedDates
            .any((selectedDate) => isSameDay(selectedDate, day));
      },
      onDaySelected: (selectedDay, focusedDay) {
        controller.onDaySelected(selectedDay, focusedDay);
      },
      calendarFormat: state.calendarFormat,
      onFormatChanged: (calendarFormat) {
        controller.chanageCalendarFormat(calendarFormat);
      },
      onPageChanged: controller.onPageChanged,
      availableCalendarFormats: const {
        CalendarFormat.month: 'Месяц',
        CalendarFormat.twoWeeks: '2 недели',
        CalendarFormat.week: 'Неделя',
      },
      enabledDayPredicate: (day) {
        return !day.isBefore(DateTime.now().subtract(Duration(days: 1)));
      },
      calendarStyle: calendarStyles.getCalendarStyle(isRectangular: true),
      headerStyle: HeaderStyle(
        formatButtonVisible: true,
        titleCentered: true,
        formatButtonShowsNext: false,
      ),
      calendarBuilders: CalendarBuilders(),
    );
  }
}
