import 'package:flutter/material.dart';
import 'package:forui/forui.dart';

class PopOverMenu extends StatefulWidget {
  const PopOverMenu({super.key, this.onPressed});
  final VoidCallback? onPressed;

  @override
  State<PopOverMenu> createState() => _PopOverMenuState();
}

class _PopOverMenuState extends State<PopOverMenu>
    with SingleTickerProviderStateMixin {
  late FPopoverController controller;
  @override
  void initState() {
    super.initState();
    controller = FPopoverController(vsync: this);
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FPopoverMenu.automatic(
      popoverController: controller,
      menuAnchor: Alignment.topLeft,
      childAnchor: Alignment.bottomCenter,
      menu: [
        FTileGroup(
          children: [
            FTile(
                prefixIcon: Icon(FIcons.pencil),
                title: Text('Aibek'),
                onPress: () {
                  widget.onPressed?.call();
                  controller.toggle();
                }),
            //FTile(title: Text('Samat'), onPress: () {}),
          ],
        ),
      ],
      child: Icon(FIcons.ellipsisVertical),
    );
  }
}
