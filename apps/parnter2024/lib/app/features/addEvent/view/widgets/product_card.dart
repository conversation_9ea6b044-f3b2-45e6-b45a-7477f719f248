import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:core_ui/core_ui.dart';
import 'package:parnter2024/app/features/addEvent/view/widgets/pop_over_menu.dart';
import 'package:parnter2024/app/features/home/<USER>/widgets/event_tile.dart';
import 'package:core_domain/core_domain.dart' show Product;

class ProductCard extends StatelessWidget {
  const ProductCard({
    super.key,
    required this.product,
    this.isSelected = false,
    this.onEditPressed,
  });

  final Product product;
  final bool isSelected;
  final VoidCallback? onEditPressed;

  @override
  Widget build(BuildContext context) {
    return FFocusedOutline(
      focused: isSelected,
      child: FCard.raw(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // *** Image
              SizedBox(
                height: 75,
                width: 100,
                child: MyNetworkImage.buildNetworkImage(
                  imageUrl: product.imageUrl,
                  width: 100,
                  height: 75,
                  cacheWidth: 200,
                  cacheHeight: 150,
                  borderRadius: context.theme.style.borderRadius,
                ),
              ),
              SizedBox(width: 12),

              // *** Info
              Expanded(
                child: Column(
                  //mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // *** Title,
                    FLabel(
                      axis: Axis.vertical,
                      label: Text(
                        product.name,
                        style: isSelected
                            ? TextStyle(
                                fontWeight: FontWeight.bold,
                                color: context.theme.colors.primary,
                              )
                            : null,
                      ),
                      child: Column(
                        children: [
                          EventTile(
                            title: product.price.toString(),
                            icondata: FIcons.calendar,
                          ),
                          EventTile(
                            title: '${product.slots}',
                            icondata: FIcons.usersRound,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // *** edit icon,
              PopOverMenu(onPressed: onEditPressed),
            ],
          ),
        ),
      ),
    );
  }
}
