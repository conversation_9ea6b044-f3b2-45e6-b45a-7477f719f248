import 'package:flutter/material.dart';
import 'package:forui/forui.dart';

class ItemEmptyCard extends StatelessWidget {
  const ItemEmptyCard({
    super.key,
    this.onTap,
    required this.title,
    this.subtitle,
    this.icon,
  });
  final void Function()? onTap;
  final String title;
  final String? subtitle;
  final IconData? icon;

  @override
  Widget build(BuildContext context) {
    // Используем компактные размеры по умолчанию
    final iconSize = 36.0;
    final verticalPadding = 16.0;
    final horizontalPadding = 16.0;
    final spacingHeight = 6.0;

    // Стиль текста для заголовка
    final titleStyle = Theme.of(context).textTheme.titleMedium!.copyWith(
          fontSize: 16.0,
        );

    // Стиль текста для подзаголовка
    final subtitleStyle = Theme.of(context).textTheme.bodyMedium!.copyWith(
          color: Colors.black54,
          fontSize: 14.0,
        );

    return Card.outlined(
        child: InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.symmetric(
            horizontal: horizontalPadding, vertical: verticalPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min, // Делаем колонку компактной
          children: [
            Icon(
              icon ?? Icons.add,
              size: iconSize,
              color: context.theme.colors.mutedForeground, // Добавляем цвет
            ),
            SizedBox(height: spacingHeight),
            Text(
              title,
              textAlign: TextAlign.center,
              style: titleStyle,
            ),
            if (subtitle != null) ...[
              // Используем spread оператор для чистоты кода
              SizedBox(height: spacingHeight),
              Text(
                subtitle!,
                textAlign: TextAlign.center,
                style: subtitleStyle,
              ),
            ],
          ],
        ),
      ),
    ));
  }
}
