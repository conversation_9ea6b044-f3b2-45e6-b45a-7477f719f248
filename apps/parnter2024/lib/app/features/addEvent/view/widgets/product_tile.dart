import 'package:flutter/material.dart';
import 'package:core_domain/core_domain.dart' show Product;

const String placeholderImagePath = 'assets/images/placeholder.jpg';

class ProductTile extends StatelessWidget {
  const ProductTile({
    super.key,
    this.isSelected = false,
    required this.product,
    this.onEditPressed,
    this.showEditMenu = true,
  });
  final bool isSelected;
  final Product product;
  final VoidCallback? onEditPressed;
  final bool showEditMenu;

  @override
  Widget build(BuildContext context) {
    return Card(
      shape: RoundedRectangleBorder(
          side: BorderSide(
              width: isSelected ? 1 : 0.5,
              color: isSelected ? Colors.black : Colors.grey),
          borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(10),
        child: Column(
          children: [
            // *** Image Name
            Row(
              children: [
                CircleAvatar(
                  backgroundImage: product.imageUrl != null
                      ? NetworkImage(product.imageUrl!)
                      : AssetImage(placeholderImagePath) as ImageProvider,
                ),
                SizedBox(width: 10),
                Expanded(
                  child: Text(
                    product.name,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ),
                Icon(Icons.check_circle_outline,
                    color: isSelected ? Colors.black : Colors.grey),
                if (showEditMenu)
                  PopupMenuButton<String>(
                    icon: Icon(Icons.more_vert),
                    tooltip: 'Изменить',
                    onSelected: (value) {
                      if (value == 'edit' && onEditPressed != null) {
                        onEditPressed!();
                      }
                    },
                    itemBuilder: (context) => [
                      PopupMenuItem<String>(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit,
                                size: 18,
                                color: Theme.of(context).colorScheme.primary),
                            SizedBox(width: 8),
                            Text('Изменить'),
                          ],
                        ),
                      ),
                    ],
                  ),
              ],
            ),

            // *** detail
            SizedBox(height: 10),
            Container(
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(12),
              ),
              padding: EdgeInsets.all(10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    children: [
                      Text(product.slots.toString()),
                      Text('мест'),
                    ],
                  ),
                  Column(
                    children: [
                      Text(product.duration.toString()),
                      Text('дня'),
                    ],
                  ),
                  Column(
                    children: [
                      Text(product.price.toString()),
                      Text('тг/чел'),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
