import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:parnter2024/app/features/addEvent/view/confirm_tab/confirm_tab.dart';
import 'package:parnter2024/app/features/addEvent/view/date_tab/date_tab.dart';
import 'package:parnter2024/app/features/addEvent/view/product_tab/product_tab.dart';
import 'package:parnter2024/app/routes/typed_routes.dart';

class AddEventPage extends ConsumerStatefulWidget {
  const AddEventPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _AddEventPageState();
}

class _AddEventPageState extends ConsumerState<AddEventPage>
    with TickerProviderStateMixin {
  late TabController tabController;

  @override
  void initState() {
    tabController = TabController(length: 3, vsync: this);
    tabController.addListener(_handleTabChange);
    super.initState();
  }

  @override
  void dispose() {
    tabController.removeListener(_handleTabChange);
    tabController.dispose();
    super.dispose();
  }

  // Функция для обновления UI при смене вкладки
  void _handleTabChange() {
    // Вызываем setState для обновления UI (чтобы показать/скрыть кнопку добавления)
    setState(() {});
  }

  // void nextPage() {
  //   if (tabController.index < tabController.length - 1) {
  //     tabController.animateTo(tabController.index + 1);
  //   }
  // }

  void _onAddProductPressed(BuildContext context) {
    const NewProductRoute().push(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Создание расписания'),
        actions: [
          // Добавляем кнопку только когда активна вкладка услуг (индекс 0)
          if (tabController.index == 0)
            IconButton(
              icon: Icon(Icons.add),
              tooltip: 'Добавить услугу',
              onPressed: () => _onAddProductPressed(context),
            ),
        ],
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(50),
          child: Container(
            decoration: BoxDecoration(
                color: context.theme.colors.muted,
                borderRadius: context.theme.style.borderRadius),
            child: TabBar(
              controller: tabController,
              //unselectedLabelColor: Colors.grey.shade700,
              labelPadding: EdgeInsets.symmetric(horizontal: 40, vertical: 0),
              dividerColor: Colors.transparent,
              tabAlignment: TabAlignment.center,
              indicatorWeight: 0,
              indicatorSize: TabBarIndicatorSize.tab,
              indicator: BoxDecoration(
                color: context.theme.colors.primary,
                borderRadius: context.theme.style.borderRadius,
              ),
              labelColor: context.theme.colors.primaryForeground,
              tabs: [
                Tab(text: 'Услуга'),
                Tab(text: 'Дата'),
                Tab(text: 'Итог'),
              ],
            ),
          ),
        ),
      ),
      body: TabBarView(
        controller: tabController,
        children: [
          ProductTab(
            tabController: tabController,
          ),
          DateTab(
            tabController: tabController,
          ),
          ConfirmTab(
            tabController: tabController,
          ),
        ],
      ),
    );
  }
}
