import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:parnter2024/app/common/widgets/bottom_action_button.dart';
import 'package:parnter2024/app/features/addEvent/controller/add_event_controller.dart';
import 'package:parnter2024/app/features/addEvent/controller/add_event_provider.dart';
import 'package:parnter2024/app/features/addEvent/view/date_tab/selected_date_list.dart';
import 'package:parnter2024/app/features/addEvent/view/widgets/item_empty_card.dart';
import 'package:parnter2024/app/features/addEvent/view/widgets/product_card.dart';

class ConfirmTab extends ConsumerWidget {
  const ConfirmTab({super.key, required this.tabController});
  final TabController tabController;

  void _navigateToTab(int index, TabController controller) {
    if (index >= 0 && index < controller.length - 1) {
      controller.animateTo(index);
    }
  }

  void _showErrorDialog(BuildContext context, Object error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Ошибка создания расписания'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Не удалось создать расписание.'),
            const SizedBox(height: 8),
            Text(
              'Детали ошибки: ${error.toString().contains('Error') ? 'Ошибка соединения с сервером' : error.toString()}',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Закрыть'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(addEventValueProvider);
    final controller = ref.watch(addEventControllerProvider);
    final theme = context.theme;

    // Слушаем изменение состояния контроллера
    ref.listen<AsyncValue>(addEventControllerProvider, (previous, next) {
      // Показываем диалог при переходе в состояние загрузки
      if (previous?.isLoading == false && next.isLoading) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const Center(
            child: CircularProgressIndicator(),
          ),
        );
      }
      // Закрываем диалог, когда загрузка завершена
      else if (previous?.isLoading == true && !next.isLoading) {
        // Закрываем диалог с индикатором загрузки
        Navigator.of(context).pop();

        // Проверяем успешность операции
        if (next.hasError) {
          // Показываем диалог с ошибкой вместо Snackbar
          _showErrorDialog(context, next.error!);
        } else {
          // Операция успешна (AsyncData с любым значением, включая null)
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Расписание успешно создано'),
            ),
          );
          Navigator.of(context).pop(); // Закрываем экран создания события
        }
      }
    });

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Expanded(
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // *** Selected service ***
                  Text(
                    ' Выбранная услуга',
                    style: theme.typography.base
                        .copyWith(fontWeight: FontWeight.w600),
                  ),
                  //SizedBox(height: 10),
                  if (state.selectedProduct == null)
                    ItemEmptyCard(
                      title: 'Услуга не выбрана',
                      subtitle: 'Нажмите, чтобы выбрать услугу',
                      icon: FIcons.briefcase, // Используем иконку услуг
                      onTap: () => _navigateToTab(0, tabController),
                    ),
                  SizedBox(height: 6),
                  if (state.selectedProduct != null)
                    ProductCard(
                      product: state.selectedProduct!,
                      isSelected: true,
                    ),

                  // *** Divider ***
                  FDivider(
                    style: FDividerStyle(
                        color: theme.colors.muted,
                        padding: EdgeInsets.symmetric(vertical: 40)),
                    axis: Axis.horizontal,
                  ),

                  // *** List of selected dates ***

                  state.selectedDates.isEmpty
                      ? Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              Text(
                                '  Даты',
                                style: theme.typography.base
                                    .copyWith(fontWeight: FontWeight.w600),
                              ),
                              ItemEmptyCard(
                                title: 'Нет выбранных дат',
                                subtitle:
                                    'Нажмите на дату в календаре, чтобы выбрать её',
                                onTap: () => _navigateToTab(1, tabController),
                                icon: FIcons
                                    .calendarDays, // Добавляем иконку календаря
                              ),
                            ],
                          ),
                        )
                      : SelectedDateList(),
                ],
              ),
            ),
          ),
        ),

        // *** Confirm button ***
        BottomActionButton.simple(
          text: controller.hasError ? 'Повторить' : 'Подтвердить',
          onPressed:
              (state.selectedDates.isEmpty || state.selectedProduct == null)
                  ? null
                  : () {
                      // Просто вызываем метод создания событий
                      ref
                          .read(addEventControllerProvider.notifier)
                          .createEventList();
                    },
        ),
      ],
    );
  }
}
