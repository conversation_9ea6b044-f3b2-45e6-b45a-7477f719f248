import 'dart:developer';

import 'package:parnter2024/app/features/addEvent/controller/add_event_provider.dart';
import 'package:core_domain/core_domain.dart' show Event;
import 'package:core_data/core_data.dart';
import 'package:parnter2024/app/common/providers/partner_auto_inject_providers.dart';
import 'package:core_utils/core_utils.dart' show NotifierMounted;
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uuid/uuid.dart';

part 'add_event_controller.g.dart';

@riverpod
class AddEventController extends _$AddEventController with NotifierMounted {
  @override
  FutureOr<void> build() {
    ref.onDispose(setUnmounted);
  }

  Future<bool> createEventList() async {
    //final productList = ref.watch(fetchProductListProvider).value ?? [];
    var value = ref.read(addEventValueProvider);
    if (value.selectedProduct != null && value.selectedDates.isNotEmpty) {
      AsyncValue<Null> status = AsyncData(null);
      List<String> eventIDList = [];

      try {
        state = AsyncLoading();
        status = await AsyncValue.guard(() async {
          final batch = ref.read(firebaseFirestoreProvider).batch();

          // create event list
          for (int i = 0; i < value.selectedDates.length; i++) {
            final eventID = Uuid().v4();
            eventIDList.add(eventID);

            final product = value.selectedProduct;
            final event = Event(
              id: eventID,
              partnerRef: product!.partnerRef,
              productRef: product.id,
              name: product.name,
              description: product.description,
              dateStart: value.selectedDates[i],
              duration: product.duration,
              durationType: product.durationType,
              price: product.price,
              slots: product.slots,
              slotsFree: product.slots,
              address: product.address,
              imageURL: product.imageUrl,
            );
            //ref.read(partnerEventRepoProvider).addEvent(event);
            ref.read(partnerEventRepoProvider).addEventBatch(event, batch);
          }

          // update product
          final product = value.selectedProduct;
          List<String> newList = [...product!.eventRefList, ...eventIDList];
          ref.read(productRepoProvider).updateProductBatch(
              product.copyWith(eventRefList: newList), batch);

          // Выполняем все операции атомарно
          await batch.commit();
        });
      } catch (e, st) {
        log('Error creating event list: $e\n$st');
        if (mounted) {
          state = AsyncError(e, st);
        }
      }

      final success = status.hasError == false;
      if (mounted) {
        state = status;
        if (success) {
          //ref.invalidate(fetchEventListProvider);
        }
        return success; // Return true on success
      }
    }
    return false; // Return false if validation fails or error occurs
  }
}
