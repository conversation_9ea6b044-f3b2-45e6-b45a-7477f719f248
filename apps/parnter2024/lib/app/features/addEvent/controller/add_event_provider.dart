import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:core_domain/core_domain.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:table_calendar/table_calendar.dart';

part 'add_event_provider.freezed.dart';
part 'add_event_provider.g.dart';

@riverpod
class AddEventValue extends _$AddEventValue {
  @override
  AddEventState build() {
    return AddEventState(focusedDay: DateTime.now());
  }

  void selectProduct(Product product) {
    //print('***************************$index*************');
    state = state.copyWith(selectedProduct: product);
  }

  void onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    if (state.selectedDates.contains(selectedDay)) {
      state = state.copyWith(selectedDates: [
        ...state.selectedDates.where((date) => date != selectedDay)
      ], focusedDay: focusedDay);
    } else {
      state = state.copyWith(
          focusedDay: focusedDay,
          selectedDates: [...state.selectedDates, selectedDay]
            ..sort((a, b) => a.compareTo(b)));
    }
  }

  void removeDate(DateTime delDate) {
    state = state.copyWith(selectedDates: [
      ...state.selectedDates.where((date) => date != delDate)
    ]);
  }

  void clearAllDates() {
    state = state.copyWith(selectedDates: []); // Просто очищаем список
  }

  void chanageCalendarFormat(CalendarFormat calendarFormat) {
    state = state.copyWith(calendarFormat: calendarFormat);
  }

  void onPageChanged(DateTime focusedDay) {
    state = state.copyWith(focusedDay: focusedDay);
  }
}

@freezed
class AddEventState with _$AddEventState {
  const factory AddEventState({
    //@Default(0) int productIndex,
    @Default([]) List<DateTime> selectedDates,
    @Default(CalendarFormat.month) CalendarFormat calendarFormat,
    //@Default([]) List<Product> productList,
    required DateTime focusedDay,
    Product? selectedProduct,
  }) = _AddEventState;
}
