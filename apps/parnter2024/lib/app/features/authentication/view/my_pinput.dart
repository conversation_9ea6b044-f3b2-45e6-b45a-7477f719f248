import 'package:flutter/material.dart';
import 'package:pinput/pinput.dart';

class MyPinPut extends StatelessWidget {
  MyPinPut({
    super.key,
    required this.pinController,
    required this.onChanged,
  });

  final TextEditingController pinController;
  final void Function(String)? onChanged;

  //static const focusedBorderColor = Color.fromRGBO(23, 171, 144, 1);
  //static const fillColor = Color.fromRGBO(243, 246, 249, 0);
  //static const borderColor = Color.fromRGBO(23, 171, 144, 0.4);

  final defaultPinTheme = PinTheme(
    width: 56,
    height: 56,
    textStyle: const TextStyle(
      fontSize: 22,
      fontWeight: FontWeight.bold,
      //color: Color.fromRGBO(30, 60, 87, 1),
      color: Colors.black87,
    ),
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(8),
      border: Border.all(color: Colors.grey),
    ),
  );

  @override
  Widget build(BuildContext context) {
    return Pinput(
      autofocus: true,
      controller: pinController,
      onChanged: onChanged,
      length: 6,
      defaultPinTheme: defaultPinTheme,
      focusedPinTheme: defaultPinTheme.copyWith(
        decoration: defaultPinTheme.decoration!.copyWith(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.black),
        ),
      ),
      submittedPinTheme: defaultPinTheme.copyWith(
        decoration: defaultPinTheme.decoration!.copyWith(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.black),
        ),
      ),
      errorPinTheme: defaultPinTheme.copyBorderWith(
        border: Border.all(color: Colors.redAccent),
      ),
    );
  }
}
