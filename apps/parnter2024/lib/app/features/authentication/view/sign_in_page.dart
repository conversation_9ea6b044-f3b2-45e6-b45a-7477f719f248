import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:core_ui/widgets/buttons/primary_button.dart';
import 'package:parnter2024/app/features/authentication/controller/auth_provider.dart';
import 'package:parnter2024/app/routes/typed_routes.dart';
import 'package:parnter2024/app/utils/error_dialog.dart';

class SignInPage extends ConsumerStatefulWidget {
  const SignInPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _SignInPageState();
}

class _SignInPageState extends ConsumerState<SignInPage> {
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController phoneTextController = TextEditingController();

  PhoneNumber number = PhoneNumber(isoCode: 'KZ');

  String? _phoneNumber;

  @override
  void initState() {
    _phoneNumber = number.phoneNumber ?? '+7';

    super.initState();
  }

  @override
  void dispose() {
    phoneTextController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
// listen ***
    ref.listen(authStateNotProvider, (_, next) {
      if (next.status == AuthStatus.sentOTP) {
        const OtpRoute().push(context);
      }
      if (next.status == AuthStatus.error) {
        errorDialog(context, next.errorMessage ?? 'error');
      }
    });

    final authController = ref.watch(authStateNotProvider);

    return Scaffold(
      body: SafeArea(
          child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Авторизация',
              style: TextStyle(
                fontSize: 36,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text('Пожалуйста введите ваш номер телефона'),
            SizedBox(height: 32),
            Form(
              key: formKey,
              child: Container(
                padding: EdgeInsets.only(left: 12, top: 8, bottom: 8),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: Colors.grey.shade100),
                child: InternationalPhoneNumberInput(
                  inputDecoration: InputDecoration(border: InputBorder.none),
                  textStyle: TextStyle(fontSize: 20),
                  selectorTextStyle: TextStyle(fontSize: 20),
                  spaceBetweenSelectorAndTextField: 0,
                  autoFocus: true,
                  maxLength: 10,
                  initialValue: number,
                  onInputChanged: (value) {
                    setState(() {});
                    _phoneNumber = value.phoneNumber;
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Введите номер телефона';
                    }
                    if (value.length != 10) {
                      return 'Номер должен содержать 10 цифр';
                    }
                    return null;
                  },
                  selectorConfig: SelectorConfig(
                      selectorType: PhoneInputSelectorType.DIALOG,
                      useBottomSheetSafeArea: true),
                  textFieldController: phoneTextController,
                  formatInput: false,
                ),
              ),
            ),
            SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                //Text('${_numberLength.toString()}/10'),
                Text('${phoneTextController.text.length.toString()}/10'),
              ],
            ),

            //Button Continue
            SizedBox(height: 32),
            PrimaryButton(
              text: 'Далее',
              isLoading: authController.status == AuthStatus.loading,
              onPressed: () {
                if (formKey.currentState!.validate()) {
                  log('Form is Valid $_phoneNumber *************************');
                  ref
                      .read(authStateNotProvider.notifier)
                      .verifyNumber(_phoneNumber!);
                }
              },
            ),

            SizedBox(height: 150),
          ],
        ),
      )),
    );
  }
}
