import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:core_ui/widgets/buttons/primary_button.dart';
import 'package:parnter2024/app/features/authentication/controller/auth_provider.dart';
import 'package:parnter2024/app/features/authentication/view/my_pinput.dart';
import 'package:parnter2024/app/routes/typed_routes.dart';
import 'package:parnter2024/app/utils/error_dialog.dart';

class OtpPage extends ConsumerStatefulWidget {
  const OtpPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _OtpPageState();
}

class _OtpPageState extends ConsumerState<OtpPage> {
  late TextEditingController otpTextController;

  @override
  void initState() {
    otpTextController = TextEditingController();
    super.initState();
  }

  @override
  void dispose() {
    otpTextController.dispose();
    super.dispose();
  }

  bool isOtpComplete() {
    return otpTextController.text.length == 6;
  }

  @override
  Widget build(BuildContext context) {
    // *** listen
    ref.listen(authStateNotProvider, (_, next) {
      if (next.status == AuthStatus.error) {
        errorDialog(context, next.errorMessage ?? 'Ошибка');
      }
    });

    final authController = ref.watch(authStateNotProvider);

    // *** Widget
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Введите код',
              style: TextStyle(fontSize: 36, fontWeight: FontWeight.bold),
            ),
            Text(
                'Мы отправили смс с кодом активации на номер ${authController.phoneNumber}'),

            SizedBox(height: 32),
            MyPinPut(
              pinController: otpTextController,
              onChanged: (value) {
                if (isOtpComplete()) {
                  setState(() {});
                }
                setState(() {});
              },
            ),
            SizedBox(height: 32),

            // *** Button
            PrimaryButton(
              text: 'Проверить код',
              isLoading: authController.status == AuthStatus.loading,
              onPressed: otpTextController.text.length != 6
                  ? null
                  : () {
                      ref
                          .read(authStateNotProvider.notifier)
                          .verifyOTP(otpTextController.text);
                    },
            ),

            // *** Text button to change number,
            SizedBox(height: 20),
            Row(
              //mainAxisAlignment: MainAxisAlignment.start,
              children: [
                TextButton.icon(
                  onPressed: () {
                    const SignInRoute().go(context);
                  },
                  label: Text(
                    'Изменить номер',
                    style: TextStyle(
                        decoration: TextDecoration.underline, fontSize: 14),
                  ),
                  icon: Icon(
                    Icons.edit,
                    size: 16,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
