import 'dart:developer';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:core_data/core_data.dart' show firebaseAuthProvider;
import 'package:parnter2024/app/features/authentication/repo/fcm_token/fcm_token_repo.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
part 'auth_repo.g.dart';

class AuthRepo {
  AuthRepo(this._firebaseAuth);
  final FirebaseAuth _firebaseAuth;

  Stream<User?> authStateChanges() => _firebaseAuth.authStateChanges();

  User? get currentUser => _firebaseAuth.currentUser;

  Stream<User?> get user {
    return _firebaseAuth.authStateChanges().map((firebaseUser) {
      final authUser = firebaseUser;
      return authUser;
    });
  }

  Future<void> logOut() async {
    if (currentUser != null) {
      // try remove fcm token
      try {
        final fcmService = FcmTokenRepo();
        await fcmService.removeToken(currentUser!.uid).catchError((error) {
          log('Ошибка при работе с FCM токеном $error');
        });
      } catch (e) {
        log('Ошибка при работе с FCM токеном $e');
      }
    }
    // signOut
    await _firebaseAuth.signOut();
  }
} // END CLASS

@Riverpod(keepAlive: false)
Stream<User?> authStateChanges(Ref ref) {
  final authRepo = ref.watch(authRepoProvider);
  return authRepo.authStateChanges();
}

@Riverpod(keepAlive: false)
AuthRepo authRepo(Ref ref) {
  return AuthRepo(ref.watch(firebaseAuthProvider));
}
