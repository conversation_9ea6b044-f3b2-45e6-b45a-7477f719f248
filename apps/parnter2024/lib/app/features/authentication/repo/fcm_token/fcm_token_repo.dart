import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

class FcmTokenRepo {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseMessaging _messaging = FirebaseMessaging.instance;

  // ***Получаем и сохраняем токен для текущего пользователя
  Future<void> saveTokenToDatavase(String userID) async {
    try {
      // Получаем текущий FCM токен
      String? token = await _messaging.getToken();

      if (token == null) return;

      // Обновляем документ пользователя, добавляя новый токен в массив
      await _firestore.collection('partners').doc(userID).update({
        'fcmTokens': FieldValue.arrayUnion([token])
      });

      // Слушаем обновление токена
      _messaging.onTokenRefresh.listen((newToken) async {
        await _updateToken(userID, token, newToken);
      });
    } catch (e) {
      rethrow;
    }
  }

  // *** Обновляем токен - удаляем старый и добавляем новый
  Future<void> _updateToken(
      String userID, String oldToken, String newToken) async {
    try {
      // Удаляем старый токен
      await _firestore.collection('partners').doc(userID).update({
        'fcmTokens': FieldValue.arrayRemove([oldToken]),
      });

      // Добавляем новый токен
      await _firestore.collection('partners').doc(userID).update({
        'fcmTokens': FieldValue.arrayUnion([newToken]),
      });
    } catch (e) {
      log('Ошибка при обновлении токена $e');
      rethrow;
    }
  }

  // ***Удаляем токен при выходе из аккаунта
  Future<void> removeToken(String userID) async {
    try {
      String? token = await _messaging.getToken();
      if (token == null) return;

      final docRef = _firestore.collection('partners').doc(userID);
      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        docRef.update({
          'fcmTokens': FieldValue.arrayRemove([token]),
        });
      }

      // Удаляем токен из FCM
      await _messaging.deleteToken();
    } catch (e) {
      // Просто логируем ошибку, но не прерываем процесс выхода
      log('ошибка при удалении токена $e');
    }
  }
} // end FcmTokenRepo
