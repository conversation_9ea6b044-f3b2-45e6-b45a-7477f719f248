import 'dart:developer';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:core_data/core_data.dart' show firebaseAuthProvider, firebaseFirestoreProvider;
import 'package:parnter2024/app/features/authentication/repo/fcm_token/fcm_token_repo.dart';
import 'package:core_domain/core_domain.dart' show Partner;
import 'package:riverpod_annotation/riverpod_annotation.dart';
part 'auth_provider.freezed.dart';
part 'auth_provider.g.dart';

@riverpod
class AuthStateNot extends _$AuthStateNot {
  @override
  AuthState build() {
    return AuthState();
  }

  Future<void> verifyNumber(String phoneNumber) async {
    state =
        state.copyWith(phoneNumber: phoneNumber, status: AuthStatus.loading);

    final fireAuth = ref.read(firebaseAuthProvider);

    // *** verify phone number
    await fireAuth.verifyPhoneNumber(
      phoneNumber: phoneNumber,
      verificationCompleted: (PhoneAuthCredential credential) async {
        await fireAuth.signInWithCredential(credential);
      },
      verificationFailed: (FirebaseAuthException e) {
        String errorMessage;
        switch (e.code) {
          case 'invalid-phone-number':
            errorMessage = 'Неверный формат номера телефона.';
            break;
          case 'too-many-requests':
            errorMessage = 'Слишком много запросов. Попробуйте позже.';
            break;
          default:
            errorMessage = 'Ошибка верификации. Попробуйте еще раз.';
        }

        state = state.copyWith(
            status: AuthStatus.error, errorMessage: errorMessage);
      },
      codeSent: (verificationId, forceResendingToken) {
        state = state.copyWith(
            status: AuthStatus.sentOTP, verificationId: verificationId);

        //log('verificationId sent: $verificationId');
      },
      codeAutoRetrievalTimeout: (verificationId) {
        log('*******************codeAutoRetrievalTimeout   $verificationId*********');
      },
    );
  }

  // *** verifyOTP
  Future<void> verifyOTP(String smsCode) async {
    //final fireAuth = ref.read(firebaseAuthProvider);
    if (state.verificationId == null) {
      state = state.copyWith(
          status: AuthStatus.error,
          errorMessage: 'Отсутствует ИД веривикации. попробуйте еще раз');
      return;
    }

    PhoneAuthCredential credential = PhoneAuthProvider.credential(
        verificationId: state.verificationId!, smsCode: smsCode);
    await signInWithPhone(credential);
  }

  // *** signInWithPhone
  Future<void> signInWithPhone(PhoneAuthCredential credential) async {
    state = state.copyWith(status: AuthStatus.loading);
    try {
      //await ref.read(firebaseAuthProvider).signInWithCredential(credential);
      //state = state.copyWith(status: AuthStatus.loggedIn);

      // *** create partner if not exist
      UserCredential userCredential =
          await ref.read(firebaseAuthProvider).signInWithCredential(credential);

      if (userCredential.user != null) {
        try {
          final partnerCollection =
              ref.read(firebaseFirestoreProvider).collection('partners');
          String? id = userCredential.user!.uid;
          final docSnapshot = await partnerCollection.doc(id).get();

          // *** document exist?
          if (docSnapshot.exists == false) {
            final partner = Partner(
                id: id,
                name: userCredential.user!.phoneNumber!,
                phoneNumber: userCredential.user!.phoneNumber!);

            await partnerCollection.doc(partner.id).set(partner.toJson());
            //await setPartnerData(partner);
          }
          // Сохраняем FCM токен
          final fcmService = FcmTokenRepo();
          await fcmService.saveTokenToDatavase(id);

          // change state
          state = state.copyWith(status: AuthStatus.loggedIn);
        } catch (error) {
          state = state.copyWith(
              status: AuthStatus.error, errorMessage: error.toString());
        }
      } else {
        state = state.copyWith(
          status: AuthStatus.error,
          errorMessage: 'Пользователь не найден. Попробуйте еще раз',
        );
      }
    } on FirebaseAuthException catch (e) {
      state = state.copyWith(status: AuthStatus.error, errorMessage: e.message);
    }
  }
} // END CLASS

@freezed
class AuthState with _$AuthState {
  const factory AuthState({
    @Default('') String phoneNumber,
    @Default(AuthStatus.initial) AuthStatus status,
    String? verificationId,
    String? smsCode,
    String? errorMessage,
  }) = _AuthState;
}

enum AuthStatus {
  initial,
  loading,
  sentOTP,
  loggedIn,
  error,
}
