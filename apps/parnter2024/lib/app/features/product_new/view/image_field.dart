import 'dart:io';

import 'package:flutter/material.dart';
import 'package:core_ui/core_ui.dart';

class ImageField extends StatelessWidget {
  const ImageField({
    super.key,
    this.imagePath,
    this.imageURL,
    this.onChangeImage,
    this.onCancelImage,
  });
  final String? imagePath;
  final String? imageURL;
  final VoidCallback? onChangeImage;
  final VoidCallback? onCancelImage;

  bool get hasImage =>
      imagePath != null || (imageURL != null && imageURL!.isNotEmpty);

  bool get hasNewLocalImage => imagePath != null && imagePath!.isNotEmpty;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Изображение с возможным оверлеем
        _buildImageContainer(context),

        // Панель инструментов (только если есть действия для изображения)
        if (onChangeImage != null ||
            (onCancelImage != null && hasNewLocalImage))
          _buildImageActionsPanel(context),
      ],
    );
  }

  Widget _buildImageContainer(BuildContext context) {
    Widget imageWidget;

    if (imagePath != null && File(imagePath!).existsSync()) {
      imageWidget = Image.file(
        File(imagePath!),
        height: 250,
        width: double.infinity,
        fit: BoxFit.cover,
      );
    } else if (imageURL != null && imageURL!.isNotEmpty) {
      imageWidget = MyNetworkImage.buildNetworkImage(
        imageUrl: imageURL,
        height: 250,
        width: double.infinity,
        cacheHeight: 500,
      );
    } else {
      return PlaceHolder(onTap: onChangeImage);
    }

    return Stack(
      children: [
        // Изображение
        imageWidget,

        // Затемненный оверлей для локального изображения (для наглядности, что оно временное)
        if (imagePath != null)
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.1),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildImageActionsPanel(BuildContext context) {
    // Определяем цветовую схему темы
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      color: theme.cardColor,
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          if (onChangeImage != null)
            TextButton.icon(
              style: TextButton.styleFrom(
                visualDensity: VisualDensity.compact,
              ),
              icon: const Icon(Icons.photo_camera, size: 18),
              label: Text(
                hasImage ? 'Изменить' : 'Добавить',
                style: theme.textTheme.bodyMedium,
              ),
              onPressed: onChangeImage,
            ),
          if (onCancelImage != null && hasNewLocalImage)
            TextButton.icon(
              style: TextButton.styleFrom(
                visualDensity: VisualDensity.compact,
              ),
              icon: const Icon(Icons.close, size: 18, color: Colors.red),
              label: Text(
                'Отменить',
                style: theme.textTheme.bodyMedium?.copyWith(color: Colors.red),
              ),
              onPressed: onCancelImage,
            ),
        ],
      ),
    );
  }
}

class PlaceHolder extends StatelessWidget {
  const PlaceHolder({
    super.key,
    this.onTap,
  });

  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    final content = Container(
      color: Colors.grey[200],
      width: double.infinity,
      height: 250,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.image_outlined,
            size: 80,
            color: Colors.grey[500],
          ),
          const SizedBox(height: 8),
          Text(
            'Нет изображения',
            style: TextStyle(color: Colors.grey[600]),
          ),
          if (onTap != null)
            Padding(
              padding: const EdgeInsets.only(top: 12),
              child: Text(
                'Нажмите, чтобы добавить',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
              ),
            ),
        ],
      ),
    );

    if (onTap == null) {
      return content;
    }

    return GestureDetector(
      onTap: onTap,
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        child: content,
      ),
    );
  }
}
