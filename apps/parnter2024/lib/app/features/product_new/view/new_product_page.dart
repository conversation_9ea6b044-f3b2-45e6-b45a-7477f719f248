import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:core_utils/core_utils.dart';
import 'package:parnter2024/app/common/my_form_text_field.dart';
import 'package:parnter2024/app/common/validators.dart';
import 'package:core_ui/core_ui.dart';
import 'package:parnter2024/app/features/product_new/controller/new_product_controller.dart';
import 'package:parnter2024/app/features/product_new/controller/new_product_provider.dart';
import 'package:parnter2024/app/features/product_new/view/category_selection_field.dart';
import 'package:parnter2024/app/features/product_new/view/image_field.dart';
import 'package:core_domain/core_domain.dart' show DurationType;
import 'package:parnter2024/app/utils/error_dialog.dart';

class NewProductPage extends ConsumerStatefulWidget {
  const NewProductPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _NewProductPageState();
}

class _NewProductPageState extends ConsumerState<NewProductPage> {
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController nameController = TextEditingController();
  final TextEditingController descController = TextEditingController();
  final TextEditingController slotController = TextEditingController();
  final TextEditingController durationController = TextEditingController();
  final TextEditingController priceController = TextEditingController();

  @override
  void dispose() {
    nameController.dispose();
    descController.dispose();
    slotController.dispose();
    durationController.dispose();
    priceController.dispose();
    super.dispose();
  }

  void _saveProduct() {
    final isValid = formKey.currentState?.validate() ?? false;
    final selectedCategories =
        ref.read(newProductStateProvider).selectedCategories;
    final hasCategories = selectedCategories.isNotEmpty;

    if (!hasCategories) {
      // Показываем сообщение о необходимости выбрать категории
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Необходимо выбрать хотя бы одну категорию'),
          duration: Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }

    if (isValid && hasCategories) {
      ref.read(newProductControllerProvider.notifier).saveProduct(
          name: nameController.text,
          description: descController.text,
          slot: slotController.text,
          duration: durationController.text,
          price: priceController.text,
          formKey: formKey,
          context: context);
    }
  }

  @override
  Widget build(BuildContext context) {
// *** List ref
    ref.listen(newProductControllerProvider, (_, next) {
      if (next.hasError) {
        errorDialog(context, next.error.toString());
      }
    });

    final state = ref.watch(newProductStateProvider);
    //final provider = ref.read(newProductStateProvider.notifier);
    final controller = ref.watch(newProductControllerProvider);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Новый продукт'),
      ),
      body: Column(
        children: [
          // Основное содержимое формы с прокруткой
          Expanded(
            child: Form(
              key: formKey,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    ImageField(
                      imagePath: state.imagePath,
                      onChangeImage: () async {
                        final imagePath = await pickAndCropImage();
                        if (imagePath == null) return;
                        ref
                            .read(newProductStateProvider.notifier)
                            .updateImage(imagePath);
                      },
                      onCancelImage:
                          state.imagePath != null && state.imagePath!.isNotEmpty
                              ? () => ref
                                  .read(newProductStateProvider.notifier)
                                  .updateImage(null)
                              : null,
                    ),
                    MyTextFromField(
                      validator: validateString,
                      textController: nameController,
                      labelName: 'Название *',
                    ),
                    MyTextFromField(
                      textController: descController,
                      labelName: 'Описание',
                    ),

                    // Duration ***
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Expanded(
                          child: MyTextFromField(
                            validator: validateNumber,
                            keyboardType: TextInputType.number,
                            textController: durationController,
                            labelName: 'Длительность *',
                          ),
                        ),

                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.only(top: 16),
                            child: Row(
                              children: [
                                // День
                                InkWell(
                                  onTap: () => ref
                                      .read(newProductStateProvider.notifier)
                                      .updateDurationType(DurationType.day),
                                  child: Row(
                                    children: [
                                      Radio<DurationType>(
                                        value: DurationType.day,
                                        groupValue: state.durationType,
                                        onChanged: ref
                                            .read(newProductStateProvider
                                                .notifier)
                                            .updateDurationType,
                                      ),
                                      const Text('день'),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 16),
                                // Час
                                InkWell(
                                  onTap: () => ref
                                      .read(newProductStateProvider.notifier)
                                      .updateDurationType(DurationType.hour),
                                  child: Row(
                                    children: [
                                      Radio<DurationType>(
                                        value: DurationType.hour,
                                        groupValue: state.durationType,
                                        onChanged: ref
                                            .read(newProductStateProvider
                                                .notifier)
                                            .updateDurationType,
                                      ),
                                      const Text('час'),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        //RadioChoice(),
                      ],
                    ),

                    // *** Slot ***
                    MyTextFromField(
                      validator: validateNumber,
                      keyboardType: TextInputType.number,
                      textController: slotController,
                      labelName: 'Количество мест *',
                    ),

                    // *** Price ***
                    MyTextFromField(
                      validator: validateNumber,
                      keyboardType: TextInputType.number,
                      textController: priceController,
                      labelName: 'Цена *',
                    ),

                    // *** Category ***
                    CategorySelectionField(),

                    // Добавляем отступ внизу для скролла под фиксированной кнопкой
                    const SizedBox(height: 60),
                  ],
                ),
              ),
            ),
          ),

          // Фиксированная кнопка внизу экрана
          FormSaveButton.fixed(
            isLoading: controller.isLoading,
            onPressed: _saveProduct,
          ),
        ],
      ),
    );
  }
}
