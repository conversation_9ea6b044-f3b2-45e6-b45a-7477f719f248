import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:core_ui/widgets/common/async_value_widget.dart';
import 'package:parnter2024/app/features/product_new/controller/new_product_provider.dart';
import 'package:core_data/core_data.dart';

class CategorySelectionField extends ConsumerWidget {
  const CategorySelectionField({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final categoryListValue = ref.watch(categoryListProvider);
    final selectedCategories =
        ref.watch(newProductStateProvider).selectedCategories;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: Row(
            children: [
              Text(
                'Категории',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.black87,
                ),
              ),
              Text(
                ' (макс. 2)',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.black87,
                ),
              ),
              if (selectedCategories.isEmpty)
                Text(
                  '   Выберите категорию',
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.error,
                  ),
                ),
            ],
          ),
        ),
        Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: AsyncValueWidget(
                value: categoryListValue,
                data: (categoryList) {
                  return Wrap(
                    spacing: 8,
                    runSpacing: 12,
                    children: categoryList.map((category) {
                      final isSelected =
                          selectedCategories.contains(category.id);
                      return FilterChip(
                        label: Text(category.name),
                        selected: isSelected,
                        showCheckmark: true,
                        checkmarkColor: Colors.white,
                        labelStyle: TextStyle(
                          color: isSelected ? Colors.white : Colors.black87,
                          fontSize: 14,
                        ),
                        backgroundColor: Colors.grey.shade100,
                        selectedColor: Theme.of(context).primaryColor,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                          side: BorderSide(
                            color: isSelected
                                ? Colors.transparent
                                : Colors.grey.shade300,
                          ),
                        ),
                        onSelected: (selected) {
                          if (selected &&
                              selectedCategories.length >= 2 &&
                              !isSelected) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content:
                                    Text('Можно выбрать не более 2 категорий'),
                                duration: Duration(seconds: 2),
                                behavior: SnackBarBehavior.floating,
                              ),
                            );
                            return;
                          }

                          ref
                              .read(newProductStateProvider.notifier)
                              .updateCategoryList(category.id);
                        },
                      );
                    }).toList(),
                  );
                })),
      ],
    );
  }
}

// class CategorySelectionField extends ConsumerWidget {
//   const CategorySelectionField({super.key});

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final categoryListAsync = ref.watch(categoryListFutureProvider);
//     final selectedCategories =
//         ref.watch(newProductStateProvider).selectedCategories;

//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         const Padding(
//           padding: EdgeInsets.only(left: 4),
//           child: Text('Категории'),
//         ),
//         const SizedBox(height: 8),
//         categoryListAsync.when(
//           data: (categories) => Wrap(
//             spacing: 8,
//             runSpacing: 8,
//             children: categories.map((category) {
//               final isSelected = selectedCategories.contains(category.id);
//               return FilterChip(
//                 label: Text(category.name),
//                 selected: isSelected,
//                 onSelected: (selected) {
//                   final newSelection = List<String>.from(selectedCategories);
//                   if (selected) {
//                     newSelection.add(category.id);
//                   } else {
//                     newSelection.remove(category.id);
//                   }
//                   ref
//                       .read(newProductStateProvider.notifier)
//                       .updateSelectedCategories(newSelection);
//                 },
//               );
//             }).toList(),
//           ),
//           loading: () => const CircularProgressIndicator(),
//           error: (_, __) => const Text('Ошибка загрузки категорий'),
//         ),
//       ],
//     );
//   }
// }
