import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:core_domain/core_domain.dart' show DurationType;
import 'package:riverpod_annotation/riverpod_annotation.dart';
part 'new_product_provider.freezed.dart';
part 'new_product_provider.g.dart';

@riverpod
class NewProductState extends _$NewProductState {
  @override
  NewProductModel build() {
    return const NewProductModel();
  }

  void updateImage(String? newImagePath) {
    state = state.copyWith(imagePath: newImagePath);
  }

  void updateDurationType(DurationType? value) {
    state = state.copyWith(durationType: value);
  }

  void updateCategoryList(String categoryID) {
    final selectedCategoires = state.selectedCategories;

    if (selectedCategoires.contains(categoryID)) {
      state = state.copyWith(selectedCategories: [
        ...selectedCategoires.where((id) => id != categoryID)
      ]);
    } else {
      if (selectedCategoires.length == 2) return;
      state = state
          .copyWith(selectedCategories: [...selectedCategoires, categoryID]);
    }
  }
}

@freezed
class NewProductModel with _$NewProductModel {
  const factory NewProductModel({
    @Default(DurationType.day) DurationType? durationType,
    String? imagePath,
    @Default([]) List<String> selectedCategories,
  }) = _NewProductState;
}
