import 'package:flutter/material.dart';
import 'package:parnter2024/app/features/authentication/repo/auth_repo.dart';
import 'package:parnter2024/app/features/product_new/controller/new_product_provider.dart';
import 'package:parnter2024/app/common/providers/partner_auto_inject_providers.dart';
import 'package:core_domain/core_domain.dart' show Product, DurationType;
import 'package:core_data/core_data.dart';
import 'package:parnter2024/app/routes/app_router.dart';
import 'package:core_utils/core_utils.dart' show NotifierMounted;
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uuid/uuid.dart';
part 'new_product_controller.g.dart';

@riverpod
class NewProductController extends _$NewProductController with NotifierMounted {
  @override
  FutureOr<void> build() {
    ref.onDispose(setUnmounted);
  }

  Future<void> saveProduct({
    required String name,
    required String description,
    required String slot,
    required String duration,
    required String price,
    required GlobalKey<FormState> formKey,
    required BuildContext context,
  }) async {
    final pageState = ref.read(newProductStateProvider);

    String imageUrl = '';

    // *** Validate input
    final isValid = _validateInput(formKey, pageState, context);
    if (!isValid) return;

    state = AsyncValue.loading();

    final productID = Uuid().v4();

    // *** upload image to firebase Strorage
    try {
      imageUrl = await ref
          .read(productRepoProvider)
          .upLoadImage(pageState.imagePath!, productID);

      // get current partner data
      final partnerValue = ref.read(partnerSelfStreamProvider);
      final partner = partnerValue.value;
      if (partner == null) {
        throw Exception('Partner data not available');
      }

      // *** new Product
      final newProduct = Product(
        id: productID,
        name: name,
        description: description,
        imageUrl: imageUrl,
        slots: int.parse(slot),
        price: int.parse(price),
        duration: int.parse(duration),
        durationType: pageState.durationType ?? DurationType.day,
        partnerRef: ref.read(authRepoProvider).currentUser!.uid,
        partnerName: partner.name,
        partnerImageUrl: partner.imageURL,
        caterogyIDList: pageState.selectedCategories,
      );

      // add product to database
      final result = await AsyncValue.guard(() async {
        await ref.read(productRepoProvider).addProduct(newProduct);
      });
      //await ref.read(productRepoProvider).addProduct(newProduct);

      //final success = value.hasError == false;
      if (mounted) {
        state = result;
        if (!result.hasError) {
          ref.read(goRouterProvider).pop();
        }
      }
    } catch (e, st) {
      if (mounted) {
        state = AsyncError(e, st);
      }
    }
  }

  // *** Validate imput
  bool _validateInput(
    GlobalKey<FormState> formKey,
    NewProductModel pageState,
    BuildContext context,
  ) {
    // Check form is valid
    if (!(formKey.currentState!.validate())) return false;

    // Check image is not null
    if (pageState.imagePath == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Пожалуйста, добавьте изображение')),
      );
      return false;
    }

    // check category is selected
    if (pageState.selectedCategories.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Выберите хотя бы одну категорию')),
      );
      return false;
    }

    return true;
  }
} // End of class
