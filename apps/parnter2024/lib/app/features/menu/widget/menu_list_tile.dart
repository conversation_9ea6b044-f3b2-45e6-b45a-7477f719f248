import 'package:flutter/material.dart';
import 'package:forui/forui.dart';

class MenuListTile extends StatelessWidget {
  final Widget? leading;
  final Widget? subtitle;
  final Widget? title;
  final VoidCallback? onTap;
  final Widget? trailing;

  const MenuListTile({
    super.key,
    this.leading,
    this.subtitle,
    this.title,
    this.onTap,
    this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ListTile(
          titleTextStyle: context.theme.typography.base.copyWith(
            fontWeight: FontWeight.w500,
          ),
          subtitleTextStyle: context.theme.typography.sm.copyWith(
            color: context.theme.colors.mutedForeground,
          ),
          leading: leading,
          title: title,
          subtitle: subtitle,
          trailing: trailing ?? Icon(FIcons.chevronRight),
          onTap: onTap,
        ),
        const Divider(indent: 16, endIndent: 16, thickness: 0.5),
      ],
    );
  }
}
