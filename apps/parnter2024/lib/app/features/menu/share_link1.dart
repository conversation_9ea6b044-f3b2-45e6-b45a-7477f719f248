import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:core_ui/widgets/common/async_value_widget.dart';
import 'package:parnter2024/app/features/menu/widget/menu_list_tile.dart';
import 'package:parnter2024/app/common/providers/partner_auto_inject_providers.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:share_plus/share_plus.dart';

class ShareLinkWidget extends ConsumerWidget {
  const ShareLinkWidget({super.key});

  String _getPublicUrl(String partnerId) {
    return 'https://event-app-b25e1.web.app/partners/partner/$partnerId';
  }

  Future<void> _copyToClipboard(BuildContext context, String url) async {
    await Clipboard.setData(ClipboardData(text: url));
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Ссылка скопирована'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  Future<void> _openInBrowser(BuildContext context, String url) async {
    final Uri uri = Uri.parse(url);
    try {
      if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Не удалось открыть ссылку в браузере'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ошибка при открытии ссылки: ${e.toString()}'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Future<void> _showShareOptions(BuildContext context, String partnerId) async {
    final String publicUrl = _getPublicUrl(partnerId);

    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.copy),
                title: const Text('Копировать ссылку'),
                onTap: () {
                  _copyToClipboard(context, publicUrl);
                  context.pop();
                },
              ),
              ListTile(
                leading: const Icon(Icons.open_in_browser),
                title: const Text('Открыть в браузере'),
                onTap: () {
                  _openInBrowser(context, publicUrl);
                  context.pop();
                },
              ),
              ListTile(
                leading: const Icon(Icons.share),
                title: const Text('Поделиться'),
                onTap: () {
                  Share.share(publicUrl);
                  context.pop();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final partnerValue = ref.watch(partnerSelfStreamProvider);

    return AsyncValueWidget(
      value: partnerValue,
      data: (partner) {
        return MenuListTile(
          leading: const Icon(Icons.ios_share_outlined),
          title: const Text('Публичная ссылка'),
          subtitle: Text(
              'Отправьте ссылку, чтобы клиенты могли ознакомиться с вашими услугами'),
          onTap: () => _showShareOptions(context, partner.id),
        );
      },
    );
  }
}
