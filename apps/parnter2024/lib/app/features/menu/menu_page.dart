import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:core_ui/widgets/common/async_value_widget.dart';
import 'package:core_utils/core_utils.dart';
import 'package:parnter2024/app/features/authentication/repo/auth_repo.dart';
import 'package:parnter2024/app/features/menu/share_link1.dart';
import 'package:parnter2024/app/features/menu/widget/menu_list_tile.dart';
import 'package:parnter2024/app/common/providers/partner_auto_inject_providers.dart';
import 'package:parnter2024/app/routes/typed_routes.dart';

class MenuPage extends ConsumerStatefulWidget {
  const MenuPage({super.key});

  @override
  ConsumerState<MenuPage> createState() => _MenuPageState();
}

class _MenuPageState extends ConsumerState<MenuPage> {
  bool _isLoading = false;

  Future<bool?> _showLogoutDialog() {
    return showDialog<bool>(
      context: context,
      builder: (context) => FDialog(
        direction: Axis.horizontal,
        title: Text('Подтверждение'),
        body: Text('Вы действительно хотите выйти?'),
        actions: [
          FButton(
            style: FButtonStyle.secondary,
            child: Text('Отмена'),
            onPress: () => Navigator.pop(context, false),
          ),
          FButton(
            child: Text('Выйти'),
            onPress: () => Navigator.pop(context, true),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final partnerValue = ref.watch(partnerSelfStreamProvider);
    final theme = context.theme;
    return Scaffold(
      appBar: AppBar(
        title: Text('Меню'),
      ),
      body: ListView(
        children: [
          // *** profile,
          AsyncValueWidget(
              value: partnerValue,
              data: (partner) {
                return MenuListTile(
                  //leading: Icon(Icons.home_outlined),
                  leading: FAvatar(
                    size: 50,
                    image: NetworkImage(partner.imageURL!),
                    fallback: Text(partner.name.getInitials()),
                  ),

                  title: Text(partner.name),
                  subtitle: Text('Показать профиль'),
                  onTap: () {
                    const ProfileRoute().push(context);
                  },
                );
              }),

          // *** Products Menu Page

          MenuListTile(
            leading: Icon(
              FIcons.airplay,
            ),
            title: Text(
              'Мои услуги',
            ),
            onTap: () => const MenuProductRoute().push(context),
          ),
          ShareLinkWidget(),
          MenuListTile(
            leading: Icon(FIcons.headset),
            title: Text('Поддержка'),
            subtitle: Text('Свяжитесь с нами'),
            onTap: () => const SupportRoute().push(context),
          ),
          MenuListTile(
            leading: Icon(FIcons.info),
            title: Text('О приложении'),
            onTap: () => const AboutRoute().push(context),
          ),
          FutureBuilder<PackageInfo>(
            future: PackageInfo.fromPlatform(),
            builder: (context, snapshot) {
              if (snapshot.hasData) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: Text(
                    'Версия ${snapshot.data!.version}',
                    style: theme.typography.xs.copyWith(
                      color: theme.colors.mutedForeground,
                    ),
                    textAlign: TextAlign.center,
                  ),
                );
              }
              return const SizedBox();
            },
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Align(
              alignment: Alignment.topLeft,
              child: TextButton.icon(
                onPressed: _isLoading
                    ? null
                    : () async {
                        final confirmed = await _showLogoutDialog();
                        if (confirmed == true) {
                          setState(() => _isLoading = true);
                          try {
                            await ref.read(authRepoProvider).logOut();
                          } finally {
                            setState(() => _isLoading = false);
                          }
                        }
                      },
                icon: _isLoading
                    ? SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(FIcons.logOut),
                label: Text(
                  'Выйти',
                  style: TextStyle(decoration: TextDecoration.underline),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
