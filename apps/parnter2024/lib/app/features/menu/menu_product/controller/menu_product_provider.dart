import 'package:parnter2024/app/features/menu/menu_product/controller/menu_product_state.dart';
import 'package:parnter2024/app/common/providers/partner_auto_inject_providers.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
part 'menu_product_provider.g.dart';

@riverpod
class MenuProductNotifier extends _$MenuProductNotifier {
  @override
  MenuProductState build() {
    final productListValue = ref.watch(partnerProductListStreamProvider);
    return MenuProductState(productListValue: productListValue);
  }
}
