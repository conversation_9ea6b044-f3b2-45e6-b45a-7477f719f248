import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:parnter2024/app/routes/typed_routes.dart';

class EmptyWidget extends StatelessWidget {
  const EmptyWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              FIcons.folder,
              size: 64,
              color: theme.colors.mutedForeground,
            ),
            SizedBox(height: 16),
            Text(
              'У вас пока нет услуг',
              style: theme.typography.lg.copyWith(fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 8),
            Text(
              'Добавьте свою первую услугу',
              style: theme.typography.sm
                  .copyWith(color: theme.colors.mutedForeground),
            ),
            <PERSON><PERSON><PERSON><PERSON>(height: 24),
            FButton(
              style: FButtonStyle.secondary,
              onPress: () => NewProductRoute().push(context),
              prefix: Icon(FIcons.plus),
              child: Text('Добавить услугу'),
            ),
          ],
        ),
      ),
    );
  }
}
