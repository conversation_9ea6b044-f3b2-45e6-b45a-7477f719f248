import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:core_ui/core_ui.dart';
import 'package:parnter2024/app/features/menu/menu_product/widgets/product_tile.dart';
import 'package:core_domain/core_domain.dart';

class ProductCard extends StatelessWidget {
  const ProductCard({super.key, required this.product, this.onTap});

  final Product product;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: FCard.raw(
          child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // *** Image
            SizedBox(
              height: 75,
              width: 100,
              child: MyNetworkImage.buildNetworkImage(
                imageUrl: product.imageUrl,
                width: 100,
                height: 75,
                cacheWidth: 200,
                cacheHeight: 150,
                borderRadius: context.theme.style.borderRadius,
              ),
            ),
            SizedBox(width: 12),

            // *** Info
            Expanded(
              child: Column(
                //mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // *** Title,
                  FLabel(
                    axis: Axis.vertical,
                    label: Text(product.name),
                    child: Column(
                      children: [
                        ProductTile(
                          title:
                              '${product.duration} ${product.durationType.formatDuration(product.duration, abbreviated: true)}',
                          icondata: FIcons.calendar,
                        ),
                        ProductTile(
                          title:
                              '${product.slots} ${product.slots > 1 ? 'мест' : 'место'}',
                          icondata: FIcons.usersRound,
                        ),
                        ProductTile(
                          title: '${product.price} тенге',
                          icondata: FIcons.circleDollarSign,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      )),
    );
  }
}
