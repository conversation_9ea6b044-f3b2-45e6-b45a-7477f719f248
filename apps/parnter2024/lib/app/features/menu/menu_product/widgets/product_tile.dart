import 'package:flutter/material.dart';
import 'package:forui/forui.dart';

class ProductTile extends StatelessWidget {
  const ProductTile({
    super.key,
    required this.title,
    required this.icondata,
  });

  final String title;
  final IconData icondata;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Icon(
          icondata,
          size: 14,
        ),
        SizedBox(width: 6),
        Text(title,
            style: context.theme.typography.base.copyWith(fontSize: 14)),
      ],
    );
  }
}
