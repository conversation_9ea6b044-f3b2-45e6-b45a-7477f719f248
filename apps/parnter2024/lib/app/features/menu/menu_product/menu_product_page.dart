import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:core_ui/widgets/common/async_value_widget.dart';
import 'package:parnter2024/app/features/menu/menu_product/controller/menu_product_provider.dart';
import 'package:parnter2024/app/features/menu/menu_product/widgets/empty_widget.dart';
import 'package:parnter2024/app/features/menu/menu_product/widgets/product_card.dart';
import 'package:parnter2024/app/routes/typed_routes.dart';

class MenuProductPage extends ConsumerWidget {
  const MenuProductPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(menuProductNotifierProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text('Мои услуги'),
      ),
      body: AsyncValueWidget(
          value: state.productListValue,
          data: (productList) {
            if (productList.isEmpty) {
              return EmptyWidget();
            }
            return ListView.builder(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              itemCount: productList.length,
              itemBuilder: (context, index) {
                final product = productList[index];
                return ProductCard(
                  product: product,
                  onTap: () => EditProductRoute($extra: product).push(context),
                );
              },
            );
          }),
      floatingActionButton: FloatingActionButton(
        onPressed: () => NewProductRoute().push(context),
        child: Icon(FIcons.plus),
      ),
    );
  }
}
