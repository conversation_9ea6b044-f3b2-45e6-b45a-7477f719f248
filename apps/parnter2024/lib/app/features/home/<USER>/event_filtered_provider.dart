import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:parnter2024/app/features/home/<USER>/event_time_filter.dart';
import 'package:parnter2024/app/features/home/<USER>/calendar_provider.dart';
import 'package:parnter2024/app/features/home/<USER>/event_time_filter_provider.dart';
import 'package:parnter2024/app/features/home/<USER>/selected_product_provider.dart';
import 'package:core_domain/core_domain.dart' show Event;
import 'package:core_data/core_data.dart';
import 'package:parnter2024/app/common/providers/partner_auto_inject_providers.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'event_filtered_provider.g.dart';
part 'event_filtered_provider.freezed.dart';

@riverpod
class HomeEventController extends _$HomeEventController {
  @override
  AsyncValue<HomeEventState> build() {
    final eventListStreamValue = ref.watch(partnerEventListStreamProvider);
    final selectedProduct = ref.watch(selectedProductProvider);
    final selectedDate = ref.watch(calendarStateProvider).selectedDate;
    final eventFilter = ref.watch(eventTimeFilterControllerProvider);

    return eventListStreamValue.when(
      loading: () => const AsyncLoading(),
      error: (e, st) => AsyncError(e, st),
      data: (eventList) {
        final now = DateTime.now();

        // Объединяем все фильтры в один where
        List<Event> filteredEvents = eventList.where((event) {
          // Фильтр по типу события (предстоящие/прошедшие)
          if (eventFilter == EventTimeFilter.upcoming &&
              event.dateStart.isBefore(now.subtract(const Duration(days: 1)))) {
            return false;
          }
          if (eventFilter == EventTimeFilter.past &&
              !event.dateStart
                  .isBefore(now.subtract(const Duration(days: 1)))) {
            return false;
          }

          // Фильтр по продукту
          if (selectedProduct != null &&
              event.productRef != selectedProduct.id) {
            return false;
          }

          // Фильтр по дате
          if (selectedDate != null &&
              !isSameDate(event.dateStart, selectedDate)) {
            return false;
          }

          return true;
        }).toList();

        // Если выбраны прошедшие, меняем сортировку на обратную
        if (eventFilter == EventTimeFilter.past) {
          filteredEvents = filteredEvents.reversed.toList();
        }

        // Вычисляем isEventsEmpty после всех фильтров
        bool isEventsEmpty = filteredEvents.isEmpty;

        // Определяем флаги активных фильтров
        bool hasDateFilter = selectedDate != null;
        bool hasProductFilter = selectedProduct != null;
        bool isPastEvents = eventFilter == EventTimeFilter.past;

        // Проверяем, пусты ли данные до применения фильтров
        bool isNoEventsAtAll = eventList.isEmpty;

        return AsyncData(HomeEventState(
          filteredEvents: filteredEvents,
          isEventsEmpty: isEventsEmpty,
          hasDateFilter: hasDateFilter,
          hasProductFilter: hasProductFilter,
          isPastEvents: isPastEvents,
          isNoEventsAtAll: isNoEventsAtAll,
        ));
      },
    );
  }

  bool isSameDate(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }
}

@freezed
class HomeEventState with _$HomeEventState {
  const factory HomeEventState({
    required List<Event> filteredEvents,
    required bool isEventsEmpty,
    @Default(false) bool hasDateFilter,
    @Default(false) bool hasProductFilter,
    @Default(false) bool isPastEvents,
    @Default(false) bool isNoEventsAtAll,
  }) = _HomeEventState;
}
