import 'package:core_domain/core_domain.dart' show Product;
import 'package:riverpod_annotation/riverpod_annotation.dart';
part 'selected_product_provider.g.dart';

@riverpod
class SelectedProduct extends _$SelectedProduct {
  @override
  Product? build() {
    return null;
  }

  void updateIndex(Product? newProduct) {
    if (newProduct == null || state == newProduct) {
      state = null;
    } else {
      state = newProduct;
    }
  }
}
