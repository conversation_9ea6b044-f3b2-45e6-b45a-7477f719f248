import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:table_calendar/table_calendar.dart';
part 'calendar_provider.freezed.dart';
part 'calendar_provider.g.dart';

@riverpod
class CalendarState extends _$CalendarState {
  @override
  CalendarModel build() {
    return CalendarModel(focusedDay: DateTime.now());
  }

  void updateCalendarFormat(CalendarFormat newCalendarFormat) {
    state = state.copyWith(calendarFormat: newCalendarFormat);
  }

  void updatDate(DateTime newSelectedDay, DateTime newFocusedDay) {
    if (state.selectedDate == newSelectedDay) {
      state = state.copyWith(selectedDate: null, focusedDay: newFocusedDay);
    } else {
      state = state.copyWith(
          selectedDate: newSelectedDay, focusedDay: newFocusedDay);
    }
  }

  void onPageChanged(DateTime newFocusedDay) {
    state = state.copyWith(focusedDay: newFocusedDay);
  }
}

@freezed
class CalendarModel with _$CalendarModel {
  const factory CalendarModel({
    @Default(CalendarFormat.week) CalendarFormat calendarFormat,
    DateTime? selectedDate,
    required DateTime focusedDay,
  }) = _CalendarModel;
}
