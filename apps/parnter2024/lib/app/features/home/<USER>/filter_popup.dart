import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:parnter2024/app/features/home/<USER>/event_time_filter_provider.dart';
import 'package:parnter2024/app/features/home/<USER>/event_time_filter.dart';

class FilterPopUp extends ConsumerStatefulWidget {
  const FilterPopUp({super.key});

  @override
  ConsumerState<FilterPopUp> createState() => _FilterPopUpState();
}

class _FilterPopUpState extends ConsumerState<FilterPopUp>
    with SingleTickerProviderStateMixin {
  late FPopoverController controller;

  @override
  void initState() {
    super.initState();
    controller = FPopoverController(vsync: this);
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final selectedFilter = ref.watch(eventTimeFilterControllerProvider);
    return FPopoverMenu.automatic(
        popoverController: controller,
        menu: [
          FTileGroup(
              children: List.generate(EventTimeFilter.values.length, (index) {
            final filterItem = EventTimeFilter.values[index];
            final isSelected = selectedFilter == filterItem;
            return FTile(
              prefixIcon: isSelected
                  ? Icon(FIcons.check)
                  : Icon(
                      FIcons.check,
                      color: Colors.transparent,
                    ),
              title: Text(filterItem.label),
              onPress: () {
                controller.toggle();
                ref
                    .read(eventTimeFilterControllerProvider.notifier)
                    .updateFilter(filterItem);
              },
            );
          })),
        ],
        child: Row(
          children: [
            Text(selectedFilter.label),
            Icon(
              FIcons.chevronDown,
              size: 16,
            ),
          ],
        ));
  }
}
