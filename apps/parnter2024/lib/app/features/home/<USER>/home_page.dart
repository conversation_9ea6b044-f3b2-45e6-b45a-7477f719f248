import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:core_ui/widgets/common/async_value_widget.dart';
import 'package:parnter2024/app/features/home/<USER>/event_filtered_provider.dart';
import 'package:parnter2024/app/features/home/<USER>/selected_product_provider.dart';
import 'package:parnter2024/app/features/home/<USER>/empty_state/filtered_empty_state.dart';
import 'package:parnter2024/app/features/home/<USER>/empty_state/home_empty_state.dart';
import 'package:parnter2024/app/features/home/<USER>/event_card.dart';
import 'package:parnter2024/app/features/home/<USER>/filter_popup.dart';
import 'package:parnter2024/app/features/home/<USER>/widgets/my_calendar.dart';
import 'package:core_domain/core_domain.dart' show Product;
import 'package:parnter2024/app/common/providers/partner_auto_inject_providers.dart';
import 'package:parnter2024/app/routes/typed_routes.dart';

class MyHomePage extends HookConsumerWidget {
  const MyHomePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final homeEventStateValue = ref.watch(homeEventControllerProvider);
    final selectedProduct = ref.watch(selectedProductProvider);
    final productListValue = ref.watch(partnerProductListStreamProvider);

    return Scaffold(
      // *** body
      body: SafeArea(
        child: Column(
          children: [
            // *** Calendar
            const MyCalendar(),
            SizedBox(
              height: 10,
            ),

            SizedBox(
              height: 50,
              child: AsyncValueWidget<List<Product>>(
                value: productListValue,
                data: (productList) => ListView.separated(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  scrollDirection: Axis.horizontal,
                  itemCount: productList.length + 1,
                  itemBuilder: (context, index) {
                    if (index == 0) {
                      return ChoiceChip(
                        showCheckmark: false,
                        side: BorderSide(
                            width: 2,
                            color: selectedProduct == null
                                ? context.theme.colors.primary
                                : context.theme.colors.secondary),
                        label: const Text('Все'),
                        selected: false,
                        onSelected: (selected) {
                          ref
                              .read(selectedProductProvider.notifier)
                              .updateIndex(null);
                        },
                      );
                    }
                    final product = productList[index - 1];
                    final isSelected = selectedProduct?.id == product.id;
                    return ChoiceChip(
                      showCheckmark: false,
                      label: Text(product.name),
                      side: BorderSide(
                          width: 2,
                          color: isSelected
                              ? context.theme.colors.primary
                              : context.theme.colors.secondary),
                      selected: false,
                      onSelected: (selected) {
                        ref
                            .read(selectedProductProvider.notifier)
                            .updateIndex(product);
                      },
                    );
                  },
                  separatorBuilder: (_, __) => const SizedBox(width: 10),
                ),
              ),
            ),

            // *** Title and Filter,
            SizedBox(
              height: 20,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Расписание',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  FilterPopUp(),
                ],
              ),
            ),
            SizedBox(
              height: 20,
            ),

            // *** Event List ***
            Expanded(
              child: AsyncValueWidget(
                value: homeEventStateValue,
                data: (homeEventState) {
                  if (!homeEventState.isEventsEmpty) {
                    // Если есть события, отображаем их
                    return ListView.separated(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 0),
                      itemCount: homeEventState.filteredEvents.length,
                      itemBuilder: (context, index) => InkWell(
                        child: EventCard(
                            event: homeEventState.filteredEvents[index]),
                        onTap: () {
                          EventDetailRoute(
                            eventId: homeEventState.filteredEvents[index].id,
                          ).push(context);
                        },
                      ),
                      separatorBuilder: (_, __) => const SizedBox(height: 20),
                    );
                  } else if (homeEventState.isNoEventsAtAll) {
                    // Если вообще нет событий в системе - показываем основное пустое состояние
                    return HomeEmptyState(onCreateService: () {
                      const AddEventRoute().push(context);
                    });
                  } else {
                    // Если есть события, но они отфильтрованы - показываем пустое состояние с фильтрами
                    return FilteredEmptyState(
                      hasDateFilter: homeEventState.hasDateFilter,
                      hasProductFilter: homeEventState.hasProductFilter,
                      isPastEvents: homeEventState.isPastEvents,
                    );
                  }
                },
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          const AddEventRoute().push(context);
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
