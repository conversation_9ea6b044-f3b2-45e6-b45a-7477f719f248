import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:parnter2024/app/common/theme/calendar_styles.dart';
import 'package:parnter2024/app/features/home/<USER>/calendar_provider.dart';
import 'package:table_calendar/table_calendar.dart';

class MyCalendar extends ConsumerWidget {
  const MyCalendar({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = context.theme;
    final state = ref.watch(calendarStateProvider);
    final calendarStyles = AppCalendarStyles(theme);

    return TableCalendar(
      locale: 'ru_RU',
      focusedDay: state.focusedDay,
      firstDay: DateTime.utc(2024, 1, 1),
      lastDay: DateTime.utc(2025, 12, 31),
      selectedDayPredicate: (day) {
        return isSameDay(state.selectedDate, day);
      },
      onDaySelected: (selectedDay, focusedDay) {
        ref
            .read(calendarStateProvider.notifier)
            .updatDate(selectedDay, focusedDay);
      },
      onPageChanged: ref.read(calendarStateProvider.notifier).onPageChanged,
      calendarFormat: state.calendarFormat,
      onFormatChanged: (calendarFormat) {
        //controller.updateCalendarFormat(calendarFormat);
        ref
            .read(calendarStateProvider.notifier)
            .updateCalendarFormat(calendarFormat);
      },
      startingDayOfWeek: StartingDayOfWeek.monday,
      availableCalendarFormats: {
        CalendarFormat.month: 'Месяц',
        CalendarFormat.week: 'Неделя',
      },
      calendarStyle: calendarStyles.getCalendarStyle(isRectangular: true),
      // CalendarStyle(
      //   weekendTextStyle: TextStyle(fontWeight: FontWeight.bold),
      //   selectedDecoration: BoxDecoration(
      //     color: theme.colors.primary,
      //     shape: BoxShape.circle,
      //   ),
      //   selectedTextStyle: theme.typography.base.copyWith(
      //     color: theme.colors.primaryForeground,
      //     height: 1,
      //   ),
      //   todayDecoration: BoxDecoration(
      //     //color: theme.colors.primary,
      //     border: Border.all(color: theme.colors.mutedForeground),
      //     shape: BoxShape.circle,
      //   ),
      //   todayTextStyle: theme.typography.sm.copyWith(
      //     color: theme.colors.primary,
      //     height: 1,
      //   ),
      // ),
      headerStyle: HeaderStyle(
        formatButtonVisible: true,
        titleCentered: true,
        titleTextStyle: const TextStyle(fontSize: 14),

        headerPadding: EdgeInsets.all(0),
        formatButtonShowsNext: true,
        formatButtonDecoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: Colors.black26, width: 0.5),
        ),
        //formatButtonPadding: const EdgeInsets.all(0),
      ),
    );
  }
}
