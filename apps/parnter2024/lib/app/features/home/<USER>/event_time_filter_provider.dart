import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:parnter2024/app/features/home/<USER>/event_time_filter.dart';

part 'event_time_filter_provider.g.dart';

@riverpod
class EventTimeFilterController extends _$EventTimeFilterController {
  @override
  EventTimeFilter build() {
    return EventTimeFilter.upcoming;
  }

  void updateFilter(EventTimeFilter filter) {
    state = filter;
  }
}
