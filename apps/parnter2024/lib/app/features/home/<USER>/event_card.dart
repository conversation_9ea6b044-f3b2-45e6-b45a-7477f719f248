import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:intl/intl.dart';
import 'package:core_ui/core_ui.dart';
import 'package:parnter2024/app/features/home/<USER>/widgets/event_tile.dart';
import 'package:core_domain/core_domain.dart' show Event;

class EventCard extends StatelessWidget {
  const EventCard({super.key, required this.event});

  final Event event;

  @override
  Widget build(BuildContext context) {
    final formattedDate = DateFormat('d MMMM', 'ru').format(event.dateStart);
    return FCard.raw(
        child: Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // *** Image
          SizedBox(
            height: 75,
            width: 100,
            child: MyNetworkImage.buildNetworkImage(
              imageUrl: event.imageURL,
              width: 100,
              height: 75,
              cacheWidth: 200,
              cacheHeight: 150,
              borderRadius: context.theme.style.borderRadius,
            ),
          ),
          SizedBox(width: 12),

          // *** Info
          Expanded(
            child: Column(
              //mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // *** Title,
                FLabel(
                  axis: Axis.vertical,
                  label: Text(event.name),
                  child: Column(
                    children: [
                      EventTile(
                        title: formattedDate,
                        icondata: FIcons.calendar,
                      ),
                      EventTile(
                        title: '${event.slotsReserved}/${event.slots}',
                        icondata: FIcons.usersRound,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ));
  }
}
