import 'package:flutter/material.dart';

class FilteredEmptyState extends StatelessWidget {
  const FilteredEmptyState({
    super.key,
    this.hasDateFilter = false,
    this.hasProductFilter = false,
    this.isPastEvents = false,
  });

  final bool hasDateFilter;
  final bool hasProductFilter;
  final bool isPastEvents;

  @override
  Widget build(BuildContext context) {
    final String title;
    final String subtitle;
    final IconData icon;

    // Выбираем сообщения в зависимости от активных фильтров
    if (hasDateFilter) {
      title = 'На выбранную дату событий нет';
      subtitle = 'Выберите другую дату';
      icon = Icons.calendar_today;
    } else if (hasProductFilter) {
      title = 'По выбранной услуге событий нет';
      subtitle = 'Выберите другую услугу';
      icon = Icons.category;
    } else if (isPastEvents) {
      title = 'Прошедших событий нет';
      subtitle = 'Они появятся здесь после завершения ваших первых событий';
      icon = Icons.history;
    } else {
      title = 'Событий не найдено';
      subtitle = 'Попробуйте изменить параметры фильтрации';
      icon = Icons.filter_list;
    }

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
