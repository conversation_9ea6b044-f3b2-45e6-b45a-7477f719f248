// home_empty_state.dart

import 'package:flutter/material.dart';
import 'package:parnter2024/app/features/home/<USER>/empty_state/step_item.dart';

class HomeEmptyState extends StatelessWidget {
  const HomeEmptyState({
    super.key,
    required this.onCreateService,
  });

  final VoidCallback onCreateService;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          //const SizedBox(height: 40),
          // Приветственная карточка
          Card(
            margin: const EdgeInsets.all(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Добро пожаловать!',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Давайте настроим ваши услуги.',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                ],
              ),
            ),
          ),

          // Карточка с шагами
          Card(
            margin: const EdgeInsets.all(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Как начать',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 16),
                  StepItem(
                    number: '1',
                    title: 'Создайте услугу',
                    subtitle: 'Добавьте описание и цену',
                  ),
                  StepItem(
                    number: '2',
                    title: 'Добавьте расписание',
                    subtitle: 'укажите доступные даты',
                  ),
                  StepItem(
                    number: '3',
                    title: 'Начните принимать заявки',
                    subtitle: 'Управляйте бронированиями',
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: FilledButton.icon(
                      onPressed: onCreateService,
                      icon: const Icon(Icons.add),
                      label: const Text('Перейти к созданию расписания'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
