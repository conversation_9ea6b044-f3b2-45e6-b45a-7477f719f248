import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:core_domain/core_domain.dart' show Partner;
part 'profile_state.freezed.dart';

@freezed
class ProfileState with _$ProfileState {
  factory ProfileState({
    required AsyncValue<Partner> partnerValue,
    String? localImage,
    @Default(AsyncData(null)) AsyncValue<void> saveValue,
    @Default(false) bool isEditMode,
  }) = _ProfileState;
}
