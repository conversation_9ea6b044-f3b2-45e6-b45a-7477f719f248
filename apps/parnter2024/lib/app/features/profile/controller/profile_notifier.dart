import 'package:core_utils/core_utils.dart';
import 'package:parnter2024/app/features/profile/controller/profile_state.dart';
import 'package:core_domain/core_domain.dart';
import 'package:core_data/core_data.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:parnter2024/app/common/providers/partner_auto_inject_providers.dart';
part 'profile_notifier.g.dart';

@riverpod
class ProfileNotifier extends _$ProfileNotifier with NotifierMounted {
  @override
  ProfileState build() {
    ref.onDispose(setUnmounted);
    final partnerValue = ref.watch(partnerSelfStreamProvider);

    return ProfileState(
      partnerValue: partnerValue,
      localImage: null,
    );
  }

  Future<void> pickImage() async {
    final localImage = await pickAndCropImage();
    state = state.copyWith(localImage: localImage);
  }

  void clearImage() {
    state = state.copyWith(localImage: null);
  }

  // Метод для сброса состояния сохранения
  void resetSaveState() {
    if (mounted) {
      state = state.copyWith(saveValue: const AsyncData(null));
    }
  }

  // *** edit mode
  void toggleEditMode() {
    if (state.isEditMode) {
      // Если выходим из режима редактирования (нажали "Отменить")
      resetToOriginalData();
    }
    state = state.copyWith(isEditMode: !state.isEditMode);
  }

  // Метод для сброса данных к исходным значениям
  void resetToOriginalData() {
    // Очищаем локальное изображение
    clearImage();
  }

  Future<void> save(Partner partner) async {
    state = state.copyWith(saveValue: const AsyncLoading());
    String? imageUrl;
    // *** upload local image
    if (state.localImage != null) {
      imageUrl = await ref
          .read(storageRepositoryProvider)
          .upLoadPartnerImage(state.localImage!, partner.id);
    }
    // *** update partner
    if (imageUrl != null) {
      partner = partner.copyWith(imageURL: imageUrl);
    }

    // *** save partner
    final value = await AsyncValue.guard(() async {
      await ref.read(partnerRepositoryProvider).updatePartner(partner);
    });

    if (mounted) {
      state = state.copyWith(
        saveValue: value,
        isEditMode: false, // Выключаем режим редактирования после сохранения
      );
    }
  }
}
