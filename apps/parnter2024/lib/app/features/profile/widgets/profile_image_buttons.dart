// import 'package:flutter/material.dart';
// import 'package:forui/forui.dart';
// import 'package:parnter2024/app/common/constants/app_sizes.dart';

// class ProfileImageButtons extends StatelessWidget {
//   final VoidCallback onPickImage;

//   final bool hasLocalImage;
//   final bool hasExistingImage;

//   const ProfileImageButtons({
//     super.key,
//     required this.onPickImage,
//     this.hasLocalImage = false,
//     this.hasExistingImage = false,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.center,
//       children: [
//         FButton(
//           style: FButtonStyle.outline,
//           onPress: onPickImage,
//           prefix: const Icon(Icons.camera_alt_outlined),
//           child: Text(hasExistingImage ? 'Изменить фото' : 'Добавить фото'),
//         ),
//       ],
//     );
//   }
// }
