import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:parnter2024/app/features/profile/controller/profile_notifier.dart';
import 'package:parnter2024/app/features/profile/controller/profile_state.dart';
import 'package:core_domain/core_domain.dart' show Partner;

class ButtonSection extends ConsumerWidget {
  const ButtonSection({
    super.key,
    required this.state,
    required this.partner,
    required this.nameController,
    required this.addressController,
    required this.aboutController,
    required this.instagramController,
  });

  final ProfileState state;
  final Partner partner;
  final TextEditingController nameController;
  final TextEditingController addressController;
  final TextEditingController aboutController;
  final TextEditingController instagramController;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (state.isEditMode) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
        child: FButton(
          onPress: state.saveValue.isLoading
              ? null
              : () {
                  final newPartner = partner.copyWith(
                    name: nameController.text,
                    address: addressController.text,
                    about: aboutController.text,
                    instagramm: instagramController.text,
                  );
                  ref.read(profileNotifierProvider.notifier).save(newPartner);
                },
          child: state.saveValue.isLoading
              ? FProgress.circularIcon()
              : Text('Сохранить'),
        ),
      );
    } else {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
        child: FButton(
          style: FButtonStyle.secondary,
          onPress: () {
            context.pop();
          },
          child: Text('Назад'),
        ),
      );
    }
  }
}
