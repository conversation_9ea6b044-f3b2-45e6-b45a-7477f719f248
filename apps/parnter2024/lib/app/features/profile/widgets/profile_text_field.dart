import 'package:flutter/material.dart';
import 'package:forui/forui.dart';

class ProfileTextField extends StatelessWidget {
  const ProfileTextField({
    super.key,
    required this.textController,
    required this.labelName,
    this.validator,
    this.keyboardType,
    this.maxLines,
    this.enabled,
    this.hintText,
    this.readOnly = false,
  });

  final TextEditingController textController;
  final String labelName;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final int? maxLines;
  final bool? enabled;
  final String? hintText;
  final bool readOnly;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: FTextField(
        controller: textController,
        readOnly: readOnly,
        label: Text(labelName),
        canRequestFocus: !readOnly,
        //focusNode: FocusNode(),
      ),
      // child: TextFormField(
      //   controller: textController,
      //   readOnly: readOnly,
      //   enabled: !readOnly,
      //   decoration: InputDecoration(
      //     labelText: labelName,
      //     border: const OutlineInputBorder(),
      //   ),
      // ),
    );
  }
}
