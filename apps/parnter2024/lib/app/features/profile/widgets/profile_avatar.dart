import 'dart:io';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';

class ProfileAvatar extends StatelessWidget {
  final String? localImagePath;
  final String? networkImageURL;
  final double radius;
  final bool isEditMode;
  final void Function()? onTap;

  const ProfileAvatar({
    super.key,
    this.localImagePath,
    this.networkImageURL,
    this.radius = 140,
    required this.isEditMode,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Stack(
        alignment: Alignment.center,
        children: [
          FAvatar(
            image: _getImageProvider()!,
            size: 140,
            fallback: Text('JD'),
          ),
          if (isEditMode) ...[
            Container(
              width: radius,
              height: radius,
              decoration: BoxDecoration(
                color: Colors.black45,
                shape: BoxShape.circle,
              ),
            ),
            Positioned(
              bottom: 6,
              right: 6,
              child: Material(
                color: Colors.white,
                shape: CircleBorder(),
                elevation: 2,
                child: Padding(
                  padding: const EdgeInsets.all(6.0),
                  child: Icon(
                    FIcons.camera,
                    color: Colors.black87,
                    size: 30,
                  ),
                ),
              ),
            ),
          ]
        ],
      ),
    );
  }

  ImageProvider? _getImageProvider() {
    if (localImagePath != null) {
      return FileImage(File(localImagePath!));
    }
    if (networkImageURL != null) {
      return NetworkImage(networkImageURL!);
    }
    return null;
  }
}
