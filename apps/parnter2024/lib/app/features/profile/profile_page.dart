import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:forui/forui.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:core_ui/core_ui.dart';
import 'package:parnter2024/app/features/profile/controller/profile_notifier.dart';
import 'package:parnter2024/app/features/profile/controller/profile_state.dart';
import 'package:parnter2024/app/features/profile/widgets/button_section.dart';
import 'package:parnter2024/app/features/profile/widgets/profile_avatar.dart';
import 'package:parnter2024/app/features/profile/widgets/profile_text_field.dart';
import 'package:core_domain/core_domain.dart' show Partner;

class ProfilePage extends HookConsumerWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    ref.listen(profileNotifierProvider.select((state) => state.saveValue),
        (previous, next) {
      if (next.isLoading) return;

      if (next.hasValue) {
        ref.read(profileNotifierProvider.notifier).clearImage();
        // Сбрасываем значение saveValue после показа сообщения

        Future.microtask(() {
          ref.read(profileNotifierProvider.notifier).resetSaveState();
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Данные успешно сохранены')),
        );
      } else if (next.hasError) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Ошибка: ${next.error}')),
        );
      }
    });

    final state = ref.watch(profileNotifierProvider);

    return AsyncValueWidget(
      value: state.partnerValue,
      data: (partner) => ProfileForm(
        partner: partner,
        state: state,
      ),
    );
  }
}

class ProfileForm extends HookConsumerWidget {
  const ProfileForm({
    super.key,
    required this.partner,
    required this.state,
  });

  final ProfileState state;
  final Partner partner;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Создаем контроллеры
    final nameController = useTextEditingController();
    final addressController = useTextEditingController();
    final aboutController = useTextEditingController();
    final instagramController = useTextEditingController();

    // Используем useEffect для инициализации контроллеров только один раз
    // или при изменении partner.id (что означает смену пользователя)
    useEffect(() {
      nameController.text = partner.name;
      addressController.text = partner.address ?? '';
      aboutController.text = partner.about ?? '';
      instagramController.text = partner.instagramm ?? '';
      return null;
    }, [partner.id]); // Зависимость только от ID партнера

    // Добавляем useEffect для сброса значений при изменении isEditMode
    useEffect(() {
      // Если вышли из режима редактирования, возвращаем исходные значения
      if (!state.isEditMode) {
        nameController.text = partner.name;
        addressController.text = partner.address ?? '';
        aboutController.text = partner.about ?? '';
        instagramController.text = partner.instagramm ?? '';
      }
      return null;
    }, [state.isEditMode]); // Зависимость от режима редактирования

    return Scaffold(
      appBar: AppBar(
        actions: [
          FButton(
            style: FButtonStyle.ghost,
            prefix: Icon(state.isEditMode ? FIcons.x : FIcons.pencil, size: 16),
            onPress: () =>
                ref.read(profileNotifierProvider.notifier).toggleEditMode(),
            child: Text(state.isEditMode ? 'Отменить' : 'Редактировать'),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Center(
              child: ProfileAvatar(
                localImagePath: state.localImage,
                networkImageURL: partner.imageURL,
                isEditMode: state.isEditMode,
                onTap: ref.read(profileNotifierProvider.notifier).pickImage,
              ),
            ),

            gapH20,

            // *** Name
            ProfileTextField(
              labelName: 'Имя',
              textController: nameController,
              readOnly: !state.isEditMode,
            ),

            // *** Address
            ProfileTextField(
              labelName: 'Адресс',
              textController: addressController,
              readOnly: !state.isEditMode,
            ),

            // *** About
            ProfileTextField(
              labelName: 'О себе',
              textController: aboutController,
              readOnly: !state.isEditMode,
            ),

            // *** Instagram
            ProfileTextField(
              labelName: 'Instagram',
              textController: instagramController,
              readOnly: !state.isEditMode,
            ),

            // Используем ButtonSection вместо встроенных кнопок
            ButtonSection(
              state: state,
              partner: partner,
              nameController: nameController,
              addressController: addressController,
              aboutController: aboutController,
              instagramController: instagramController,
            ),
          ],
        ),
      ),
    );
  }
}
