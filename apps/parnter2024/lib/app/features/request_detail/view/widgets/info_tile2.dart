import 'package:flutter/material.dart';
import 'package:forui/forui.dart';

class InfoTile2 extends StatelessWidget {
  const InfoTile2({
    super.key,
    required this.title,
    required this.text,
    this.valueColor,
    this.isHighlighted = false,
  });

  final String title;
  final String text;
  final Color? valueColor;
  final bool isHighlighted;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: theme.colors.border,
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: theme.typography.sm.copyWith(
              color: theme.colors.mutedForeground,
              fontWeight: FontWeight.w500,
            ),
          ),
          //SizedBox(height: 6),
          Text(
            text,
            style: theme.typography.base.copyWith(
              fontWeight: FontWeight.w600,
              color:
                  valueColor ?? (isHighlighted ? theme.colors.primary : null),
            ),
          ),
        ],
      ),
    );
  }
}
