import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:parnter2024/app/features/request_detail/controller/request_detail_notifier.dart';
import 'package:parnter2024/app/features/request_detail/view/button_section.dart';
import 'package:parnter2024/app/features/request_detail/view/client_section.dart';
import 'package:parnter2024/app/features/request_detail/view/event_section.dart';
import 'package:parnter2024/app/features/request_detail/view/info_section.dart';
import 'package:core_domain/core_domain.dart' show Request;

class RequestDetailPage extends ConsumerWidget {
  const RequestDetailPage({super.key, required this.newRequest});
  final Request newRequest;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(requestDetailNotifierProvider(newRequest));

    return Scaffold(
      appBar: AppBar(
        title: Text('Запрос на бронирование'),
        //elevation: 0.5,
        shape: Border.symmetric(
            horizontal: BorderSide(color: context.theme.colors.border)),
      ),
      body: Column(
        children: [
          //const Divider(height: 1, thickness: 2),
          SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // *** Request Info ***
                //Divider(thickness: 2),
                InfoSection(requestStreamValue: state.requestValue),
                Divider(thickness: 3),

                ClientSection(clientValue: state.clientValue),

                Divider(thickness: 3),

                // *** Event Info ***
                EventSection(eventValue: state.eventValue),

                Divider(thickness: 3),

                // *** Buttons ***
                ButtonSection(requestStreamValue: state.requestValue),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
