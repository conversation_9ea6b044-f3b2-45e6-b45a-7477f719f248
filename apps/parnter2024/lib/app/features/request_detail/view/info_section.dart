import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:core_ui/widgets/common/async_value_widget.dart';
import 'package:core_utils/core_utils.dart';
import 'package:core_domain/core_domain.dart';

class InfoSection extends StatelessWidget {
  const InfoSection({
    super.key,
    required this.requestStreamValue,
  });

  final AsyncValue<Request> requestStreamValue;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return AsyncValueWidget(
        value: requestStreamValue,
        data: (request) {
          Color? statusColor;
          switch (request.status) {
            case RequestStatus.pending:
              statusColor = Colors.orange;
              break;
            case RequestStatus.accepted:
              statusColor = Colors.green;
              break;
            case RequestStatus.declined:
            case RequestStatus.canceled:
              statusColor = Colors.red;
              break;
          }

          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Заголовок секции
                Row(
                  children: [
                    Icon(
                      FIcons.clipboardList,
                      size: 18,
                      color: theme.colors.primary,
                    ),
                    SizedBox(width: 8),
                    Text(
                      'Информация о заявке',
                      style: theme.typography.base.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 10),

                // Информация о статусе
                _buildInfoColumn(
                  context,
                  'Статус',
                  request.status.description,
                  valueColor: statusColor,
                ),

                // Дата создания
                _buildInfoColumn(
                  context,
                  'Дата создания',
                  request.createdAt != null
                      ? request.createdAt!.format('dd.MM.yyyy', 'ru')
                      : 'Не известно',
                ),

                // Резерв
                _buildInfoColumn(
                  context,
                  'Резерв',
                  '${request.slots} слотов',
                ),

                // Номер заявки
                _buildInfoColumn(
                  context,
                  'Номер заявки',
                  '№123456',
                  isHighlighted: true,
                ),
              ],
            ),
          );
        });
  }

  Widget _buildInfoColumn(
    BuildContext context,
    String title,
    String value, {
    Color? valueColor,
    bool isHighlighted = false,
  }) {
    final theme = context.theme;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: theme.typography.sm.copyWith(
              color: theme.colors.mutedForeground,
              fontWeight: FontWeight.w500,
            ),
          ),
          //SizedBox(height: 4),
          Text(
            value,
            style: theme.typography.base.copyWith(
              fontWeight: FontWeight.w500,
              color:
                  valueColor ?? (isHighlighted ? theme.colors.primary : null),
            ),
          ),
        ],
      ),
    );
  }
}
