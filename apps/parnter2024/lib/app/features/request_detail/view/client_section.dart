import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:core_ui/widgets/common/async_value_widget.dart';
import 'package:core_domain/core_domain.dart';
import 'package:parnter2024/app/routes/typed_routes.dart';

const String placeholderImagePath = 'assets/images/placeholder.jpg';

class ClientSection extends StatelessWidget {
  const ClientSection({
    super.key,
    required this.clientValue,
  });

  final AsyncValue<Client> clientValue;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    return AsyncValueWidget(
        value: clientValue,
        data: (client) => Padding(
              padding: const EdgeInsets.fromLTRB(16.0, 6, 16.0, 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        FIcons.user,
                        size: 18,
                        color: theme.colors.primary,
                      ),
                      Sized<PERSON>ox(width: 8),
                      Text(
                        'Клиент',
                        style: theme.typography.base.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 10),

                  // Карточка пользователя
                  InkWell(
                    onTap: () {
                      ClientDetailRoute(id: client.id).push(context);
                    },
                    child: Row(
                      children: [
                        // Аватар
                        CircleAvatar(
                          radius: 24,
                          backgroundImage: client.imageURL != null &&
                                  client.imageURL!.isNotEmpty
                              ? NetworkImage(client.imageURL!) as ImageProvider
                              : AssetImage(placeholderImagePath),
                        ),

                        SizedBox(width: 16),

                        // Информация о пользователе
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                client.name ?? 'Покупатель',
                                style: theme.typography.base.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              SizedBox(height: 4),
                              Text(
                                client.phoneNumber,
                                style: theme.typography.sm.copyWith(
                                  color: theme.colors.mutedForeground,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Стрелка
                        Icon(
                          FIcons.chevronRight,
                          size: 20,
                          color: theme.colors.mutedForeground,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ));
  }
}
