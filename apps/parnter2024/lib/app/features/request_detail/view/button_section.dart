import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:core_ui/widgets/common/async_value_widget.dart';
import 'package:parnter2024/app/common/widgets/bottom_action_button.dart';
import 'package:parnter2024/app/features/requests/controller/requests_notifier.dart';
import 'package:parnter2024/app/features/requests/view/widgets/bottom_sheet_reason.dart';
import 'package:core_domain/core_domain.dart';

class ButtonSection extends ConsumerWidget {
  const ButtonSection({
    super.key,
    required this.requestStreamValue,
    //required this.requestController,
  });

  final AsyncValue<Request> requestStreamValue;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final requestsState = ref.watch(requestsNotifierProvider);

    return AsyncValueWidget(
        value: requestStreamValue,
        data: (request) {
          return BottomActionButton.actions(
            requestStatus: request.status,
            isLoading: requestsState.isUpdating &&
                requestsState.activeRequestID == request.id,
            onAccept: () {
              ref.read(requestsNotifierProvider.notifier).updateRequest(
                  request.copyWith(status: RequestStatus.accepted));
            },
            onReject: () {
              showModalBottomSheet(
                  isScrollControlled: true,
                  useSafeArea: true,
                  context: context,
                  builder: (context) {
                    return Padding(
                      padding: EdgeInsets.only(
                          bottom: MediaQuery.of(context).viewInsets.bottom),
                      child: BottomSheetReason(
                        onConfirm: (reason) {
                          context.pop();
                          log('******hello $reason******');
                          ref
                              .read(requestsNotifierProvider.notifier)
                              .updateRequest(
                                request.copyWith(
                                    status: RequestStatus.declined,
                                    rejectReason: reason,
                                    cancelInitiator: CancelInitiator.partner),
                              );
                        },
                      ),
                    );
                  });
            },
          );
        });
  }
}
