import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:core_ui/core_ui.dart';
import 'package:core_utils/core_utils.dart';
import 'package:core_domain/core_domain.dart' show Event;

import '../../requests/view/widgets/info_tile.dart';

class EventSection extends StatelessWidget {
  const EventSection({
    super.key,
    required this.eventValue,
  });

  final AsyncValue<Event> eventValue;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    return AsyncValueWidget(
        value: eventValue,
        data: (event) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Заголовок секции с иконкой календаря
                Row(
                  children: [
                    Icon(
                      FIcons.calendar,
                      size: 18,
                      color: theme.colors.primary,
                    ),
                    SizedBox(width: 8),
                    Text(
                      'Информация о туре',
                      style: theme.typography.base.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 16),

                // Название и изображение тура
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Миниатюра тура
                    Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        color: theme.colors.muted,
                      ),
                      child:
                          event.imageURL != null && event.imageURL!.isNotEmpty
                              ? MyNetworkImage.buildNetworkImage(
                                  imageUrl: event.imageURL,
                                  width: 100,
                                  height: 100,
                                  cacheWidth: 200,
                                  cacheHeight: 200,
                                  errorWidget: _buildIconPlaceholder(theme),
                                  borderRadius: BorderRadius.circular(8),
                                )
                              : _buildIconPlaceholder(theme),
                    ),

                    SizedBox(width: 16),

                    // Основная информация
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Название тура
                          Text(
                            event.name,
                            style: theme.typography.base.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),

                          SizedBox(height: 6),

                          InfoTile(
                            text: event.dateStart.format('d MMMM, EEEE', 'ru'),
                            icon: FIcons.calendar,
                          ),
                          SizedBox(height: 6),
                          InfoTile(
                            text: '${event.slots} мест',
                            icon: FIcons.users,
                          ),
                          SizedBox(height: 6),
                          InfoTile(
                            text: '${event.duration} дн.',
                            icon: FIcons.clock,
                          ),
                          // Иконки с информацией в строку
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        });
  }

  Widget _buildIconPlaceholder(FThemeData theme) {
    return Center(
      child: Icon(
        FIcons.image,
        size: 24,
        color: theme.colors.mutedForeground,
      ),
    );
  }
}
