import 'package:parnter2024/app/features/request_detail/controller/request_detail_state.dart';
import 'package:core_data/core_data.dart';
import 'package:core_domain/core_domain.dart' show Request;
import 'package:riverpod_annotation/riverpod_annotation.dart';
part 'request_detail_notifier.g.dart';

@riverpod
class RequestDetailNotifier extends _$RequestDetailNotifier {
  @override
  RequestDetailState build(Request request) {
    final requestStreamValue = ref.watch(requestStreamProvider(request.id));
    
    // Convert AsyncValue<Request?> to AsyncValue<Request>
    final AsyncValue<Request> requestValue = requestStreamValue.when(
      data: (data) => data != null 
          ? AsyncValue<Request>.data(data) 
          : AsyncValue<Request>.error(Exception('Request not found'), StackTrace.current),
      loading: () => const AsyncValue<Request>.loading(),
      error: (error, stack) => AsyncValue<Request>.error(error, stack),
    );
    
    final eventValue = ref.watch(fetchEventProvider(request.eventID));
    final clientValue = ref.watch(fetchClientByIdProvider(request.clientID));
    return RequestDetailState(
        requestValue: requestValue,
        eventValue: eventValue,
        clientValue: clientValue);
  }
}
