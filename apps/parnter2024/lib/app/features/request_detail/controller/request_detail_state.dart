import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:core_domain/core_domain.dart' show Request, Client, Event;
part 'request_detail_state.freezed.dart';

@freezed
class RequestDetailState with _$RequestDetailState {
  factory RequestDetailState({
    required AsyncValue<Request> requestValue,
    required AsyncValue<Event> eventValue,
    required AsyncValue<Client> clientValue,
  }) = _RequestDetailState;
}
