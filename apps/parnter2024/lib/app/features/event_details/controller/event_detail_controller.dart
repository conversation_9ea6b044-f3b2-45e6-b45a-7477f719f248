import 'package:core_data/core_data.dart';
import 'package:core_domain/core_domain.dart' show Event;
import 'package:parnter2024/app/common/providers/partner_auto_inject_providers.dart';
import 'package:parnter2024/app/features/event_details/controller/selected_event_provider.dart';
import 'package:parnter2024/app/routes/app_router.dart';
import 'package:core_utils/core_utils.dart' show NotifierMounted;
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'event_detail_controller.g.dart';

@riverpod
class EventDetailController extends _$EventDetailController
    with NotifierMounted {
  @override
  FutureOr<void> build() {
    ref.onDispose(setUnmounted);
  }

  Future<void> deleteEvent(Event event) async {
    state = const AsyncLoading();
    final status = await AsyncValue.guard(() async {
      await ref.read(partnerEventRepoProvider).deleteEvent(event);
    });

    final success = status.hasError == false;
    if (mounted) {
      state = status;
      if (success) {
        ref.read(goRouterProvider).pop();
      }
    }
  }

  Future<void> updateEvent(Event event) async {
    state = const AsyncLoading();
    final newState = await AsyncValue.guard(() async {
      await ref.read(partnerEventRepoProvider).updateEvent(event);
    });
    state = newState;
    if (!newState.hasError) {
      ref.read(selectedEventProvider.notifier).selectEvent(event);
    }
  }
}
