import 'dart:async';

import 'package:core_data/core_data.dart';
import 'package:core_domain/core_domain.dart' show Event;
import 'package:parnter2024/app/common/providers/partner_auto_inject_providers.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'selected_event_provider.g.dart';

@riverpod
class SelectedEvent extends _$SelectedEvent {
  Timer? _timer;

  @override
  FutureOr<Event?> build() async {
    final value = await ref.read(partnerFetchEventListProvider.future);

    final link = ref.keepAlive();
    ref.onDispose(() {
      _timer?.cancel();
    });

    ref.onCancel(() {
      _timer = Timer(Duration(seconds: 10), () {
        link.close();
      });
    });

    ref.onResume(() {
      _timer?.cancel();
    });

    if (value.isEmpty) {
      return null; // Riverpod will wrap this in AsyncData(null)
    }

    // return first value
    return value.first;
  }

  // *** Select event
  void selectEvent(Event event) {
    state = AsyncData(event);
  }
}
