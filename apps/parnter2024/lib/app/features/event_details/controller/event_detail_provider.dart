import 'package:core_data/core_data.dart';
import 'package:core_domain/core_domain.dart' show Event;
import 'package:parnter2024/app/common/providers/partner_auto_inject_providers.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'event_detail_provider.g.dart';

@riverpod
class EventDetailNotifier extends _$EventDetailNotifier {
  @override
  AsyncValue<Event> build(String eventId) {
    final eventListValue = ref.watch(partnerEventListStreamProvider);
    return eventListValue.when(
        data: (eventList) {
          final event = eventList.firstWhere((e) => e.id == eventId);
          return AsyncData(event);
        },
        error: (error, st) => AsyncError(error, st),
        loading: () => AsyncLoading());
  }
}
