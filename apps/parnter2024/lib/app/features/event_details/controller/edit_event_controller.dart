import 'package:parnter2024/app/features/event_details/controller/selected_event_provider.dart';
import 'package:core_domain/core_domain.dart' show Event;
import 'package:core_data/core_data.dart';
import 'package:parnter2024/app/common/providers/partner_auto_inject_providers.dart';
import 'package:parnter2024/app/routes/app_router.dart';
import 'package:parnter2024/app/routes/typed_routes.dart';
import 'package:core_utils/core_utils.dart' show NotifierMounted;
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'edit_event_controller.g.dart';

@riverpod
class EditEventController extends _$EditEventController with NotifierMounted {
  @override
  FutureOr<void> build() {
    ref.onDispose(setUnmounted);
  }

  Future<void> updateEvent({
    required Event event,
    required String description,
    required int slots,
    String? address,
  }) async {
    state = const AsyncLoading();
    final newEvent = event.copyWith(
        description: description, slots: slots, address: address);

    final value = await AsyncValue.guard(() async {
      await ref.read(partnerEventRepoProvider).updateEvent(newEvent);
      return null;
    });

    if (mounted) {
      state = value;
      if (!value.hasError) {
        ref.read(selectedEventProvider.notifier).selectEvent(newEvent);
      }
    }
  }

  Future<void> deleteEvent(Event event) async {
    state = const AsyncLoading();
    if (event.requestIdList.isNotEmpty) {
      state = AsyncError(
          'У вас есть активные заявки на эту услугу', StackTrace.current);
      return;
    }
    final product =
        await ref.read(productRepoProvider).productFuture(event.productRef);
    final value = await AsyncValue.guard(() async {
      await ref.read(partnerEventRepoProvider).deleteEvent(event);
      final newEventList =
          product.eventRefList.where((e) => e != event.id).toList();
      await ref
          .read(productRepoProvider)
          .updateProduct(product.copyWith(eventRefList: newEventList));
    });

    if (mounted) {
      state = value;
      if (!value.hasError) {
        ref.read(goRouterProvider).go(const HomeRoute().location);
      }
    }
  }
}
