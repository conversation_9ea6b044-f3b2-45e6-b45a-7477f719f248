import 'package:flutter/material.dart';
import 'package:core_domain/core_domain.dart';
import 'package:parnter2024/app/routes/typed_routes.dart';

class ClientListEventPage extends StatelessWidget {
  const ClientListEventPage({super.key, required this.clientList});
  final List<Client> clientList;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text('Список участников'),
        ),
        body: ListView.builder(
            itemCount: clientList.length,
            itemBuilder: (context, index) {
              return ListTile(
                //contentPadding: EdgeInsets.symmetric(horizontal: 0),
                title: Text(clientList[index].name ?? ''),
                subtitle: Text(clientList[index].phoneNumber),
                leading: CircleAvatar(
                  backgroundImage: NetworkImage(clientList[index].imageURL ??
                      'https://picsum.photos/200'),
                ),
                onTap: () {
                  ClientDetailRoute(id: clientList[index].id).push(context);
                },
              );
            }));
  }
}
