import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:core_ui/widgets/common/async_value_widget.dart';
import 'package:core_domain/core_domain.dart' show Client, Event;
import 'package:core_data/core_data.dart';
import 'package:parnter2024/app/routes/typed_routes.dart';
import 'package:core_utils/core_utils.dart';

class ClientsSection extends ConsumerWidget {
  const ClientsSection({
    super.key,
    required this.event,
  });

  final Event event;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final clientListValue =
        ref.watch(fetchClientListByIdProvider(event.clientRefList));

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _SlotsSection(event: event),
          SizedBox(height: 30),
          Divider(thickness: 2),
          SizedBox(height: 30),
          AsyncValueWidget(
            value: clientListValue,
            data: (clientList) => _ParticipantsSection(
              event: event,
              clientList: clientList,
            ),
          ),
          SizedBox(height: 20),
          Divider(thickness: 2),
          SizedBox(height: 30),
        ],
      ),
    );
  }
}

class _SlotsSection extends StatelessWidget {
  const _SlotsSection({
    required this.event,
  });

  final Event event;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Слоты',
          style: theme.typography.base.copyWith(fontWeight: FontWeight.w600),
        ),
        SizedBox(height: 12),
        FLabel(
          axis: Axis.vertical,
          label: Text('Занято ${event.slotsReserved} из ${event.slots}'),
          child: FCard.raw(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              child: FProgress(
                value: (event.slotsReserved / event.slots),
                duration: Duration(seconds: 1),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class _ParticipantsSection extends StatelessWidget {
  const _ParticipantsSection({
    required this.event,
    required this.clientList,
  });

  final Event event;
  final List<Client> clientList;

  @override
  Widget build(BuildContext context) {
    final isClientsEmpty = clientList.isEmpty;
    final isShowMore = clientList.length > 3;
    final countToShow = isShowMore ? 3 : clientList.length;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        _HeaderRow(
          eventClientCount: event.clientRefList.length,
          isShowMore: clientList.length > 3,
          clientList: clientList,
        ),
        SizedBox(height: 10),
        if (isClientsEmpty) _EmptyParticipantsCard(),
        _ParticipantsList(
          clientList: clientList,
          countToShow: countToShow,
        ),
      ],
    );
  }
}

class _HeaderRow extends StatelessWidget {
  const _HeaderRow({
    required this.eventClientCount,
    required this.isShowMore,
    required this.clientList,
  });

  final int eventClientCount;
  final bool isShowMore;
  final List<Client> clientList;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Участники ($eventClientCount)',
          style: theme.typography.base.copyWith(
            height: 1.25,
            fontWeight: FontWeight.w600,
          ),
        ),
        if (isShowMore)
          GestureDetector(
            onTap: () {
              ClientListEventRoute($extra: clientList).push(context);
            },
            child: Text(
              'Показать всех',
              style: theme.typography.sm.copyWith(
                decoration: TextDecoration.underline,
                color: theme.colors.primary,
              ),
            ),
          ),
      ],
    );
  }
}

class _EmptyParticipantsCard extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return Padding(
      padding: const EdgeInsets.only(top: 12),
      child: FCard.raw(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(FIcons.info, size: 30),
              SizedBox(height: 12),
              Text(
                'На это событие еще никто не записался.',
                textAlign: TextAlign.center,
                style: theme.typography.lg,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _ParticipantsList extends StatelessWidget {
  const _ParticipantsList({
    required this.clientList,
    required this.countToShow,
  });

  final List<Client> clientList;
  final int countToShow;

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: EdgeInsets.zero,
      shrinkWrap: true,
      itemCount: countToShow,
      itemBuilder: (context, index) {
        final client = clientList[index];
        return _ParticipantListItem(client: client);
      },
    );
  }
}

class _ParticipantListItem extends StatelessWidget {
  const _ParticipantListItem({
    required this.client,
  });

  final Client client;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      minVerticalPadding: 15,
      minTileHeight: 0,
      title: Text(client.name ?? 'Без имени'),
      subtitle: Text(client.phoneNumber),
      leading: FAvatar(
        image: NetworkImage(client.imageURL!),
        fallback: Text((client.name ?? '').getInitials()),
      ),
    );
  }
}
