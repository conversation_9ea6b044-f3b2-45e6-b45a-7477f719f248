import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:intl/intl.dart';
import 'package:core_utils/core_utils.dart' hide NumberFormat;
import 'package:core_domain/core_domain.dart' show Event;

class InfoSection extends StatelessWidget {
  const InfoSection({
    super.key,
    required this.event,
  });

  final Event event;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            event.name,
            style: theme.typography.lg
                .copyWith(fontWeight: FontWeight.w600, height: 1.25),
          ),
          SizedBox(height: 12),
          Wrap(
            spacing: 16,
            runSpacing: 12,
            children: [
              _InfoItem(
                icon: FIcons.calendar,
                label: event.dateStart.toDMW('ru'),
                isHighlighted: true,
              ),
              _InfoItem(
                icon: FIcons.clock,
                label: '${event.duration} дней',
              ),
              _InfoItem(
                icon: FIcons.creditCard,
                label:
                    '${NumberFormat.decimalPattern('ru').format(event.price)} тг',
              ),
              if (event.address != null)
                _InfoItem(
                  icon: FIcons.mapPin,
                  label: event.address!,
                ),
            ],
          ),
          SizedBox(height: 30),
          Divider(
            thickness: 2,
          ),
        ],
      ),
    );
  }
}

class _InfoItem extends StatelessWidget {
  const _InfoItem({
    required this.icon,
    required this.label,
    this.isHighlighted = false,
  });

  final IconData icon;
  final String label;
  final bool isHighlighted;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: isHighlighted ? 16 : 16,
          weight: isHighlighted ? 600 : null,
        ),
        SizedBox(width: 4),
        Text(
          label,
          style: theme.typography.sm.copyWith(
            fontWeight: isHighlighted ? FontWeight.w600 : null,
          ),
        ),
      ],
    );
  }
}
