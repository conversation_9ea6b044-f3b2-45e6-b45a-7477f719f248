import 'package:flutter/material.dart';

class MyListTile extends StatelessWidget {
  const MyListTile({
    super.key,
    //required this.event,
    required this.title,
    this.subtitle,
    this.iconData,
  });

  final String title;

  final Widget? subtitle;
  final IconData? iconData;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding: EdgeInsets.all(0),
      minTileHeight: 0,
      //minLeadingWidth: 0,
      //minVerticalPadding: 0,
      // ***
      style: ListTileStyle.list,
      leading: iconData != null ? Icon(iconData) : null,
      // title: Text(
      //   'Цена',
      // ),
      title: Text(title),
      subtitle: subtitle,
      //dense: true,
    );
  }
}
