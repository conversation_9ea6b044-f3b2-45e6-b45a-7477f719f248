import 'package:flutter/material.dart';

class IconButtonAppbar extends StatelessWidget {
  const IconButtonAppbar({
    super.key,
    required this.iconData,
    required this.onPressed,
  });
  final IconData? iconData;
  final void Function()? onPressed;

  @override
  Widget build(BuildContext context) {
    return DecoratedBox(
      decoration: BoxDecoration(
        color: Colors.white,
        shape: BoxShape.circle,
        border: Border.all(color: Colors.black26),
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(iconData),

        //padding: EdgeInsets.all(24),
        //color: Colors.white,
      ),
    );
  }
}
