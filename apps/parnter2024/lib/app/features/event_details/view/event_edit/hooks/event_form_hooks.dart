import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:parnter2024/app/features/event_details/controller/edit_event_controller.dart';
import 'package:core_domain/core_domain.dart' show Event;

/// Хук для управления сохранением события
ValueNotifier<bool> useSaveEvent({
  required WidgetRef ref,
  required GlobalKey<FormState> formKey,
  required TextEditingController descriptionController,
  required TextEditingController slotsController,
  required TextEditingController addressController,
  required AsyncValue<Event> eventValue,
}) {
  final isSaving = useState(false);

  // Обработчик кнопки "Сохранить"
  final handleSave = useCallback(() {
    // Если уже в процессе загрузки, не делаем ничего
    if (isSaving.value) return;

    // Убираем фокус с текущего поля
    FocusScope.of(useContext()).unfocus();

    final valid = formKey.currentState?.validate() ?? false;
    if (valid && eventValue.hasValue) {
      // Устанавливаем локальное состояние загрузки
      isSaving.value = true;

      final event = eventValue.value!;
      ref.read(editEventControllerProvider.notifier).updateEvent(
            event: event,
            description: descriptionController.text,
            slots: int.parse(slotsController.text),
            address: addressController.text.toString().trim().isEmpty
                ? null
                : addressController.text,
          );
    }
  }, [
    formKey,
    ref,
    eventValue,
    isSaving,
    descriptionController,
    slotsController,
    addressController,
  ]);

  // Слушаем изменения в контроллере
  useEffect(() {
    final subscription =
        ref.listenManual(editEventControllerProvider, (prev, next) {
      if (next.hasError) {
        // При ошибке снимаем состояние загрузки
        isSaving.value = false;
        errorDialog(useContext(), next.error.toString());
      } else if (prev?.isLoading == true && !next.isLoading) {
        // Если завершили загрузку (из любого состояния), снимаем флаг загрузки
        isSaving.value = false;

        // Убираем фокус со всех полей
        FocusScope.of(useContext()).unfocus();

        // Если завершили успешно, показываем уведомление
        if (next.hasValue) {
          ScaffoldMessenger.of(useContext()).showSnackBar(
            SnackBar(
              content: Text('Данные успешно обновлены'),
            ),
          );
        }
      }
    });

    return () => subscription.close();
  }, [ref]);

  return isSaving;
}

/// Хук для управления удалением события
Future<bool> useDeleteEvent({
  required BuildContext context,
  required Event event,
}) async {
  return await showDialog<bool>(
        context: context,
        builder: (context) {
          return AlertDialog(
            title: const Text('Удалить событие'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Событие: ${event.name}'),
                Text(
                    'Дата: ${DateFormat('dd.MM.yyyy').format(event.dateStart)}'),
                const SizedBox(height: 16),
                const Text('Вы действительно хотите удалить?'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text('Отмена'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: Text('Удалить'),
              ),
            ],
          );
        },
      ) ??
      false;
}

/// Вспомогательная функция для отображения диалога ошибки
void errorDialog(BuildContext context, String message) {
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: const Text('Ошибка'),
      content: Text(message),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('OK'),
        ),
      ],
    ),
  );
}
