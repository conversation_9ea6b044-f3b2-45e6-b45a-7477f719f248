import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:intl/intl.dart';
import 'package:core_domain/core_domain.dart' show Event, DurationType;

/// Хук для создания и инициализации контроллеров формы редактирования события
class EventFormControllers {
  final TextEditingController nameController;
  final TextEditingController dateController;
  final TextEditingController descriptionController;
  final TextEditingController slotsController;
  final TextEditingController priceController;
  final TextEditingController durationController;
  final TextEditingController addressController;
  final TextEditingController mainDurationController;
  final ValueNotifier<DurationType> durationType;
  final ValueNotifier<bool> isControllerInitialized;
  final GlobalKey<FormState> formKey;

  EventFormControllers({
    required this.nameController,
    required this.dateController,
    required this.descriptionController,
    required this.slotsController,
    required this.priceController,
    required this.durationController,
    required this.addressController,
    required this.mainDurationController,
    required this.durationType,
    required this.isControllerInitialized,
    required this.formKey,
  });

  /// Инициализирует контроллеры данными события
  void initControllers(Event event) {
    nameController.text = event.name;
    dateController.text = DateFormat('dd.MM.yyyy').format(event.dateStart);
    descriptionController.text = event.description;
    slotsController.text = event.slots.toString();
    priceController.text = event.price.toString();
    durationController.text = event.duration.toString();
    addressController.text = event.address ?? '';
    durationType.value = event.durationType;
    mainDurationController.text = event.duration.toString();
    isControllerInitialized.value = true;
  }
}

/// Создает и возвращает контроллеры для формы редактирования события
EventFormControllers useEventFormControllers() {
  final formKey = useMemoized(() => GlobalKey<FormState>(), const []);
  final nameController = useTextEditingController();
  final dateController = useTextEditingController();
  final descriptionController = useTextEditingController();
  final slotsController = useTextEditingController();
  final priceController = useTextEditingController();
  final durationController = useTextEditingController();
  final addressController = useTextEditingController();
  final mainDurationController = useTextEditingController();
  final durationType = useState(DurationType.day);
  final isControllerInitialized = useState(false);

  return EventFormControllers(
    nameController: nameController,
    dateController: dateController,
    descriptionController: descriptionController,
    slotsController: slotsController,
    priceController: priceController,
    durationController: durationController,
    addressController: addressController,
    mainDurationController: mainDurationController,
    durationType: durationType,
    isControllerInitialized: isControllerInitialized,
    formKey: formKey,
  );
}
