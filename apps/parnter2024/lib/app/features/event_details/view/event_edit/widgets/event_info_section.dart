import 'package:flutter/material.dart';
import 'package:core_domain/core_domain.dart' show DurationType;
import 'package:parnter2024/app/common/my_form_text_field.dart';
import 'package:parnter2024/app/common/validators.dart';

/// Универсальный компонент для отображения информации о событии
/// с возможностью выборочного редактирования полей
class EventInfoSection extends StatelessWidget {
  const EventInfoSection({
    super.key,
    required this.nameController,
    required this.dateController,
    required this.priceController,
    required this.mainDurationController,
    required this.durationType,
    required this.descriptionController,
    required this.slotsController,
    required this.addressController,
    this.canEditName = false,
    this.canEditDate = false,
    this.canEditPrice = false,
    this.canEditDuration = false,
    this.canEditDurationType = false,
    this.canEditDescription = true,
    this.canEditSlots = true,
    this.canEditAddress = true,
    this.onDurationTypeChanged,
  });

  // Обязательные контроллеры
  final TextEditingController nameController;
  final TextEditingController dateController;
  final TextEditingController priceController;
  final TextEditingController mainDurationController;
  final DurationType durationType;

  // Необязательные контроллеры
  final TextEditingController descriptionController;
  final TextEditingController slotsController;
  final TextEditingController addressController;

  // Флаги редактирования для каждого поля
  final bool canEditName;
  final bool canEditDate;
  final bool canEditPrice;
  final bool canEditDuration;
  final bool canEditDurationType;
  final bool canEditDescription;
  final bool canEditSlots;
  final bool canEditAddress;

  // Обработчик изменения типа длительности
  final void Function(DurationType?)? onDurationTypeChanged;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Название события
        MyTextFromField(
          textController: nameController,
          labelName: 'Название',
          readOnly: !canEditName,
          enabled: canEditName,
        ),

        // Дата события
        MyTextFromField(
          textController: dateController,
          labelName: 'Дата события',
          readOnly: !canEditDate,
          enabled: canEditDate,
        ),

        // Цена
        MyTextFromField(
          textController: priceController,
          labelName: 'Цена',
          keyboardType: TextInputType.number,
          enabled: canEditPrice,
          readOnly: !canEditPrice,
        ),

        // Длительность и тип длительности
        _buildDurationSection(),

        // Дополнительные редактируемые поля
        MyTextFromField(
          textController: descriptionController,
          labelName: 'Описание',
          maxLines: 2,
          enabled: canEditDescription,
          readOnly: !canEditDescription,
        ),

        MyTextFromField(
          validator: validateNumber,
          textController: slotsController,
          labelName: 'Количество мест',
          keyboardType: TextInputType.number,
          enabled: canEditSlots,
          readOnly: !canEditSlots,
        ),

        MyTextFromField(
          textController: addressController,
          labelName: 'Адрес',
          keyboardType: TextInputType.text,
          enabled: canEditAddress,
          readOnly: !canEditAddress,
        ),
      ],
    );
  }

  Widget _buildDurationSection() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Поле длительности
        Expanded(
          child: MyTextFromField(
            keyboardType: TextInputType.number,
            textController: mainDurationController,
            labelName: 'Длительность',
            enabled: canEditDuration,
            readOnly: !canEditDuration,
          ),
        ),

        // Радиокнопки типа длительности
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(top: 28),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Radio(
                  value: DurationType.day,
                  groupValue: durationType,
                  onChanged: canEditDurationType ? onDurationTypeChanged : null,
                ),
                Text('день'),
                Radio(
                  value: DurationType.hour,
                  groupValue: durationType,
                  onChanged: canEditDurationType ? onDurationTypeChanged : null,
                ),
                Text('час'),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
