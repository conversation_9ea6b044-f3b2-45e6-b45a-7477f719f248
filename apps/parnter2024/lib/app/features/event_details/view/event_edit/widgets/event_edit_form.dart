import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:core_ui/widgets/buttons/primary_button.dart';
import 'package:parnter2024/app/features/event_details/view/event_edit/hooks/event_controllers_hooks.dart';
import 'package:parnter2024/app/features/event_details/view/event_edit/widgets/event_info_section.dart';

/// Компонент формы редактирования события
class EventEditForm extends ConsumerWidget {
  const EventEditForm({
    super.key,
    required this.controllers,
    required this.isLoading,
    required this.onSave,
  });

  final EventFormControllers controllers;
  final bool isLoading;
  final VoidCallback onSave;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Form(
      key: controllers.formKey,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Объединенная информация о событии
            EventInfoSection(
              // Неизменяемые поля
              nameController: controllers.nameController,
              dateController: controllers.dateController,
              priceController: controllers.priceController,
              mainDurationController: controllers.mainDurationController,
              durationType: controllers.durationType.value,
              // Редактируемые поля
              descriptionController: controllers.descriptionController,
              slotsController: controllers.slotsController,
              addressController: controllers.addressController,
              // При необходимости можно указать, какие поля редактируемы
              onDurationTypeChanged: (value) =>
                  controllers.durationType.value = value!,
            ),

            // Кнопка сохранения
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 28),
              child: PrimaryButton(
                text: 'Сохранить',
                isLoading: isLoading,
                onPressed: onSave,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
