import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:core_ui/widgets/common/async_value_widget.dart';
import 'package:parnter2024/app/features/event_details/controller/edit_event_controller.dart';
import 'package:parnter2024/app/features/event_details/controller/event_detail_provider.dart';
import 'package:parnter2024/app/features/event_details/view/event_edit/hooks/event_controllers_hooks.dart';
import 'package:parnter2024/app/features/event_details/view/event_edit/widgets/delete_event_dialog.dart';
import 'package:parnter2024/app/features/event_details/view/event_edit/widgets/event_edit_form.dart';

class EventEditPage extends HookConsumerWidget {
  const EventEditPage({
    super.key,
    required this.eventId,
  });

  final String eventId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Получаем контроллеры формы из хука
    final controllers = useEventFormControllers();

    // Состояние для отслеживания процесса сохранения
    final isSaving = useState(false);

    // Получаем данные события
    final eventValue = ref.watch(eventDetailNotifierProvider(eventId));

    // Эффект для инициализации контроллеров при загрузке данных
    useEffect(() {
      if (eventValue.hasValue && !controllers.isControllerInitialized.value) {
        controllers.initControllers(eventValue.value!);
      }
      return null;
    }, [eventValue, controllers]);

    // Слушаем изменения в контроллере для обработки результатов сохранения
    useEffect(() {
      final subscription =
          ref.listenManual(editEventControllerProvider, (prev, next) {
        if (next.hasError) {
          // При ошибке снимаем состояние загрузки
          isSaving.value = false;
          _showErrorDialog(context, next.error.toString());
        } else if (prev?.isLoading == true && !next.isLoading) {
          // Если завершили загрузку, снимаем флаг загрузки
          isSaving.value = false;

          // Убираем фокус со всех полей
          FocusScope.of(context).unfocus();

          // Если завершили успешно, показываем уведомление
          if (next.hasValue) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Данные успешно обновлены'),
              ),
            );
          }
        }
      });

      return () => subscription.close();
    }, [ref]);

    // Обработчик сохранения данных формы
    final handleSave = useCallback(() {
      // Если уже в процессе загрузки, не делаем ничего
      if (isSaving.value) return;

      // Убираем фокус с текущего поля
      FocusScope.of(context).unfocus();

      final valid = controllers.formKey.currentState?.validate() ?? false;
      if (valid && eventValue.hasValue) {
        // Устанавливаем локальное состояние загрузки
        isSaving.value = true;

        final event = eventValue.value!;
        ref.read(editEventControllerProvider.notifier).updateEvent(
              event: event,
              description: controllers.descriptionController.text,
              slots: int.parse(controllers.slotsController.text),
              address:
                  controllers.addressController.text.trim().isEmpty
                      ? null
                      : controllers.addressController.text.trim(),
            );
      }
    }, [controllers, ref, eventValue, isSaving, context]);

    return AsyncValueWidget(
      value: eventValue,
      data: (event) {
        return GestureDetector(
          // Снимаем фокус при нажатии вне формы
          onTap: () => FocusScope.of(context).unfocus(),
          child: Scaffold(
            appBar: AppBar(
              title: const Text('Редактирование'),
              actions: [
                DeleteEventButton(
                  event: event,
                  onShowAlertDialog: (event) =>
                      showDeleteEventDialog(context, event),
                ),
              ],
            ),
            body: EventEditForm(
              controllers: controllers,
              isLoading: isSaving.value,
              onSave: handleSave,
            ),
          ),
        );
      },
    );
  }

  // Вспомогательный метод для отображения диалога ошибки
  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Ошибка'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
