import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:parnter2024/app/features/event_details/controller/edit_event_controller.dart';
import 'package:core_domain/core_domain.dart' show Event;

/// Компонент для отображения кнопки удаления события в AppBar
class DeleteEventButton extends ConsumerWidget {
  const DeleteEventButton({
    super.key,
    required this.event,
    required this.onShowAlertDialog,
  });

  final Event event;
  final Future<bool> Function(Event event) onShowAlertDialog;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return TextButton.icon(
      icon: Icon(Icons.delete),
      onPressed: () async {
        final shouldDelete = await onShowAlertDialog(event);
        if (shouldDelete) {
          await ref
              .read(editEventControllerProvider.notifier)
              .deleteEvent(event);
        }
      },
      label: Text('Удалить'),
    );
  }
}

/// Функция для показа диалога удаления
Future<bool> showDeleteEventDialog(BuildContext context, Event event) async {
  return await showDialog<bool>(
        context: context,
        builder: (context) {
          return AlertDialog(
            title: const Text('Удалить событие'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Событие: ${event.name}'),
                Text(
                    'Дата: ${DateFormat('dd.MM.yyyy').format(event.dateStart)}'),
                const SizedBox(height: 16),
                const Text('Вы действительно хотите удалить?'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text('Отмена'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: Text('Удалить'),
              ),
            ],
          );
        },
      ) ??
      false;
}
