import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:core_ui/core_ui.dart';
import 'package:parnter2024/app/features/event_details/controller/event_detail_provider.dart';
import 'package:parnter2024/app/features/event_details/view/appbar_section.dart';
import 'package:parnter2024/app/features/event_details/view/clients_section.dart';
import 'package:parnter2024/app/features/event_details/view/info_section.dart';

class EventDetailPage extends ConsumerWidget {
  const EventDetailPage({
    super.key,
    required this.eventId,
  });

  final String eventId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final eventValue = ref.watch(eventDetailNotifierProvider(eventId));
    return Scaffold(
      body: AsyncValueWidget(
        value: eventValue,
        data: (event) => SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // *** appBar
              AppbarSection(event: event),

              // *** Info
              InfoSection(event: event),

              ClientsSection(event: event),

              // *** Chat

              gapH20,
            ],
          ),
        ),
      ),
    );
  }
}
