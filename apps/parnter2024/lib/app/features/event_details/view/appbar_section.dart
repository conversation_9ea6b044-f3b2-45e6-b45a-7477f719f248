import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:core_ui/core_ui.dart';
import 'package:parnter2024/app/features/event_details/view/widgets/iconbutton_appbar.dart';
import 'package:core_domain/core_domain.dart' show Event;
import 'package:parnter2024/app/routes/typed_routes.dart';

class AppbarSection extends ConsumerWidget {
  const AppbarSection({
    super.key,
    required this.event,
  });

  final Event event;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SizedBox(
      height: 250,
      child: Stack(
        children: [
          // *** image
          Positioned.fill(
            child: MyNetworkImage.buildNetworkImage(
              imageUrl: event.imageURL,
              height: 250,
              cacheHeight: 600,
            ),
          ),

          // *** Icon Buttons,
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  IconButtonAppbar(
                    iconData: Icons.arrow_back,
                    onPressed: () {
                      context.pop();
                    },
                  ),
                  IconButtonAppbar(
                    iconData: Icons.edit_outlined,
                    onPressed: () {
                      EventEditRoute(
                        eventId: event.id,
                      ).push(context);
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
