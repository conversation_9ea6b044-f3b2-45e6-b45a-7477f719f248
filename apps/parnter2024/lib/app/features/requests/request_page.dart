import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:parnter2024/app/features/requests/controller/requests_notifier.dart';
import 'package:parnter2024/app/features/requests/controller/requests_state.dart';
import 'package:parnter2024/app/features/requests/view/request_accept_tab.dart';
import 'package:parnter2024/app/features/requests/view/request_new_tab.dart';
import 'package:parnter2024/app/features/requests/view/request_reject_tab.dart';
import 'package:parnter2024/app/utils/error_dialog.dart';

class RequestPage extends ConsumerStatefulWidget {
  const RequestPage({super.key});

  @override
  ConsumerState<RequestPage> createState() => _RequestPageState();
}

class _RequestPageState extends ConsumerState<RequestPage>
    with TickerProviderStateMixin {
  late TabController tabController;
  @override
  void initState() {
    tabController = TabController(length: 3, vsync: this);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    ref.listen(requestsNotifierProvider, (_, next) {
      log('Слушатель сработал: feedback = ${next.feedback}'); // Лог для отладки
      // Лог для отладки
      final feedbackEvent = next.feedback;
      if (feedbackEvent != null) {
        switch (feedbackEvent) {
          case RequestSuccess():
            String message = 'Заявка успешно обработана';
            log('Попытка показать Snackbar: $message'); // Лог перед показом
            ScaffoldMessenger.of(context)
              ..hideCurrentSnackBar()
              ..showSnackBar(SnackBar(content: Text(message)));
            break;
          case RequestError(message: final errorMessage):
            log('Ошибка: $errorMessage'); // Лог для ошибок
            errorDialog(context, errorMessage);
            break;
        }

        Future.microtask(
            () => ref.read(requestsNotifierProvider.notifier).clearFeedback());
      }
    });
    final theme = context.theme;
    return SafeArea(
      child: Scaffold(
        appBar: TabBar(
          indicatorSize: TabBarIndicatorSize.tab,
          dividerColor: theme.colors.border,
          controller: tabController,
          labelStyle: theme.typography.sm.copyWith(fontWeight: FontWeight.w600),
          unselectedLabelStyle: theme.typography.sm,
          tabs: [
            Tab(text: 'Новые'),
            Tab(text: 'Принятые'),
            Tab(text: 'Отклоненные'),
          ],
        ),
        body: TabBarView(
          controller: tabController,
          children: [
            RequestNewTab(),
            RequestAcceptTab(),
            RequestRejectTab(),
          ],
        ),
      ),
    );
  }
}
