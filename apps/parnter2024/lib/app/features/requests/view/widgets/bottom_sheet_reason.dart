import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:go_router/go_router.dart';
import 'package:parnter2024/app/common/enums/reject_reason.dart';

class BottomSheetReason extends StatefulWidget {
  const BottomSheetReason({super.key, required this.onConfirm});
  final Function(String reasonText) onConfirm;

  @override
  State<BottomSheetReason> createState() => _BottomSheetReasonState();
}

class _BottomSheetReasonState extends State<BottomSheetReason> {
  final TextEditingController _textController = TextEditingController();
  RejectReason? selectedReason;
  final controller = FSelectGroupController<RejectReason>.radio();

  @override
  void initState() {
    super.initState();
    controller.addListener(_updateSelectedReason);
  }

  void _updateSelectedReason() {
    setState(() {
      final values = controller.value;
      selectedReason = values.isNotEmpty ? values.first : null;
    });
  }

  @override
  void dispose() {
    controller.removeListener(_updateSelectedReason);
    _textController.dispose();
    super.dispose();
  }

  void _handleConfirm() {
    if (selectedReason == null) return;

    final reasonText =
        RejectReason.getReasonText(selectedReason!, _textController.text);
    widget.onConfirm(reasonText);
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colors.background,
        border: Border.symmetric(
            horizontal: BorderSide(color: theme.colors.border)),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: SingleChildScrollView(
          child: Column(
            children: [
              Text('Причина отмены',
                  style: theme.typography.xl2.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colors.foreground,
                      height: 1.5)),

              // *** ListTile Radio
              SizedBox(height: 20),
              FSelectGroup<RejectReason>(
                style: FSelectGroupStyle.inherit(
                        colors: theme.colors,
                        typography: theme.typography,
                        style: theme.style)
                    .copyWith(
                  itemPadding: EdgeInsets.symmetric(vertical: 5),
                  radioStyle: FRadioStyle.inherit(
                          colors: theme.colors, style: theme.style)
                      .copyWith(
                          labelTextStyle: FWidgetStateMap({
                    WidgetState.disabled: theme.typography.base.copyWith(
                        color: theme.colors.disable(theme.colors.primary),
                        fontWeight: FontWeight.w500),
                    WidgetState.any: theme.typography.base.copyWith(
                        color: theme.colors.primary,
                        fontWeight: FontWeight.w500),
                  })),
                ),
                controller: controller,
                //label: Text('Причина отмены'),
                description: Text('Выберите причину отмены'),
                validator: (values) =>
                    values?.isEmpty ?? true ? 'Please select a value.' : null,
                children: [
                  FRadio.grouped(
                    value: RejectReason.dateUnavailable,
                    label: Text(RejectReason.dateUnavailable.label),
                  ),
                  FRadio.grouped(
                    value: RejectReason.noSlots,
                    label: Text(RejectReason.noSlots.label),
                  ),
                  FRadio.grouped(
                    value: RejectReason.other,
                    label: Text(RejectReason.other.label),
                  ),
                ],
              ),
              // ...RejectReason.values.map((reason) {
              //   return RadioListTile(
              //       //contentPadding: EdgeInsets.all(0),
              //       //visualDensity: VisualDensity(vertical: -4),
              //       title: Text(reason.label),
              //       value: reason,
              //       groupValue: selectedReason,
              //       onChanged: (value) {
              //         setState(() {
              //           selectedReason = value;
              //         });
              //       });
              // }),

              // *** extra textField
              if (selectedReason == RejectReason.other)
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 0, vertical: 16),
                  child: Column(
                    children: [
                      FTextField.multiline(
                        controller: _textController,
                        hint: 'Укажите причину',
                      ),
                    ],
                  ),
                ),

              // *** button
              SizedBox(height: 50),

              Row(
                children: [
                  Expanded(
                    child: FButton(
                        style: FButtonStyle.secondary,
                        onPress: () {
                          context.pop();
                        },
                        child: Text('Отмена')),
                  ),
                  SizedBox(width: 10),
                  Expanded(
                    child: FButton(
                        style: FButtonStyle.primary,
                        onPress: selectedReason == null
                            ? null
                            : () {
                                _handleConfirm();
                              },
                        child: Text('Подтвердить')),
                  )
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
