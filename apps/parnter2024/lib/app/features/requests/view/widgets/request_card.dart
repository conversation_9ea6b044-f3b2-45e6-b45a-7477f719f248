import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:core_ui/widgets/common/async_value_widget.dart';
import 'package:core_utils/core_utils.dart';
import 'package:parnter2024/app/features/requests/controller/requests_notifier.dart';
import 'package:parnter2024/app/features/requests/view/widgets/info_tile.dart';
import 'package:parnter2024/app/features/requests/view/widgets/request_card_footer.dart';
import 'package:core_data/core_data.dart';
import 'package:core_domain/core_domain.dart';
import 'package:parnter2024/app/routes/typed_routes.dart';

class RequestCard extends ConsumerWidget {
  const RequestCard(
      {super.key, required this.request, this.onAccept, this.onReject});
  final Request request;
  final void Function()? onAccept;
  final void Function()? onReject;

  String _getStatusText() {
    if (request.status == RequestStatus.declined) {
      return 'Отклонено';
    }
    return request.cancelInitiator?.description ?? 'Отменено';
  }

  Color getColorForStatus(RequestStatus status) {
    return switch (status) {
      RequestStatus.pending => Colors.orange.shade800,
      RequestStatus.accepted => Colors.green.shade800,
      RequestStatus.declined => Colors.red.shade800,
      RequestStatus.canceled => Colors.red.shade800,
    };
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    //final controller = ref.watch(requestControllerProvider());
    final statevalue = ref.watch(requestsNotifierProvider);
    final isLoading =
        statevalue.isUpdating && statevalue.activeRequestID == request.id;
    //final value = request.id == selectedRequestValue?.id;
    final clientValue = ref.watch(fetchClientByIdProvider(request.clientID));
    final statusText = _getStatusText();
    final theme = context.theme;
    return InkWell(
      //onTap: () => Get.toNamed(Routes.requestDetail),
      onTap: () => RequestDetailRoute(
        $extra: request,
      ).push(context),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // ***name
              Text(
                request.eventName,
                style:
                    theme.typography.base.copyWith(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 6),

              // ***date
              InfoTile(
                icon: FIcons.calendar,
                text: request.dateStart.toDMW('ru'),
              ),
              SizedBox(height: 4),

              // *** Client,
              AsyncValueWidget(
                  value: clientValue,
                  data: (client) {
                    return InfoTile(
                      icon: FIcons.squareUserRound,
                      //text: request.clientID1,
                      text: client.name ?? client.phoneNumber,
                    );
                  }),

              SizedBox(height: 4),
              InfoTile(
                icon: FIcons.usersRound,
                text: '${request.slots} человека',
              ),

              // ***status if declined or canceled,

              if (request.status == RequestStatus.declined ||
                  request.status == RequestStatus.canceled)
                Padding(
                  padding: const EdgeInsets.only(top: 10),
                  child: Row(
                    children: [
                      Icon(
                        Icons.circle_rounded,
                        size: 14,
                        color: getColorForStatus(request.status),
                      ),
                      SizedBox(width: 8),
                      Text(
                        statusText,
                        style: theme.typography.sm.copyWith(
                          fontWeight: FontWeight.bold,
                          color: getColorForStatus(request.status),
                        ),
                      ),
                    ],
                  ),
                ),

              // *** Buttons ***
              //SizedBox(height: 10),
              if (request.status == RequestStatus.accepted ||
                  request.status == RequestStatus.pending)
                Padding(
                  padding: const EdgeInsets.only(top: 20),
                  child: RequestCardFooter(
                    request: request,
                    //controller: controller,
                    isLoading: isLoading,
                    onAccept: onAccept,
                    onReject: onReject,
                  ),
                ),

              /// ***Временно не будем показывать кнопку одобрения, может получится так что по ошибку одобрит старую заявку,
              // if (request.status == RequestStatus.declined ||
              //     request.status == RequestStatus.canceled)
              //   PrimaryButton(
              //     text: 'Одобрить',
              //     isLoading: controller.isLoading && value,
              //     onPressed: onAccept ?? () {},
              //   ),
            ],
          ),
        ),
      ),
    );
  }
}
