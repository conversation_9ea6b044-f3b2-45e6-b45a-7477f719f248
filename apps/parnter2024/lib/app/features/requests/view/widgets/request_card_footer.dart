import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:core_domain/core_domain.dart';

class RequestCardFooter extends StatelessWidget {
  const RequestCardFooter({
    super.key,
    required this.request,
    //required this.controller,
    required this.isLoading,
    this.onAccept,
    this.onReject,
  });

  final Request request;
  //final AsyncValue<RequestStatus?> controller;
  final bool isLoading;
  final void Function()? onAccept;
  final void Function()? onReject;

  @override
  Widget build(BuildContext context) {
    if (request.status == RequestStatus.pending) {
      if (isLoading) {
        return Center(child: CircularProgressIndicator());
      } else {
        // Иначе показываем кнопки "Отказать" и "Одобрить"
        return Row(
          children: [
            Expanded(
              child: FButton(
                style: FButtonStyle.secondary,
                onPress: onReject ?? () {},
                child: Text('Отклонить'),
              ),
              // CustOutlineButton(
              //   text: 'Отказать',
              //   //isLoading: controller.isLoading && !value, // Сохраняем закомментированным, как в оригинале
              //   onPressed: onReject ?? () {},
              // ),
            ),
            SizedBox(width: 10),
            Expanded(
              child: FButton(
                style: FButtonStyle.primary,
                onPress: onAccept ?? () {},
                child: Text('Одобрить'),
              ),
              // PrimaryButton(
              //   text: 'Одобрить',
              //   //isLoading: controller.isLoading && value, // Сохраняем закомментированным, как в оригинале
              //   onPressed: onAccept ?? () {},
              // ),
            ),
          ],
        );
      }
    } else if (request.status == RequestStatus.accepted) {
      // Если заявка принята, показываем кнопку "Отменить"
      // Индикатор загрузки будет на самой кнопке, если для этой карточки идет операция

      return FButton(
        style: FButtonStyle.secondary,
        onPress: onReject ?? () {},
        child: isLoading ? FProgress.circularIcon() : Text('Отменить'),
      );

      // return CustOutlineButton(
      //   text: 'Отменить',
      //   isLoading: value,
      //   onPressed: onReject ??
      //       () {}, // Предполагается, что onReject используется для отмены
      // );
    }
    // Для других статусов или если кнопки не нужны, возвращаем пустой виджет
    return SizedBox.shrink();
  }
}
