import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:core_ui/widgets/common/async_value_widget.dart';
import 'package:core_domain/core_domain.dart';
import 'package:parnter2024/app/features/requests/controller/requests_notifier.dart';
import 'package:parnter2024/app/features/requests/view/widgets/request_card.dart';

class RequestRejectTab extends ConsumerWidget {
  const RequestRejectTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // *** providers
    final requestListValue =
        ref.watch(requestsNotifierProvider).requestsRejected;

    // *** widget
    return AsyncValueWidget(
        value: requestListValue,
        data: (requestList) {
          return ListView.builder(
            itemCount: requestList.length,
            padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            itemBuilder: (context, index) {
              return RequestCard(
                request: requestList[index],
                onAccept: () {
                  ref.read(requestsNotifierProvider.notifier).updateRequest(
                      requestList[index]
                          .copyWith(status: RequestStatus.accepted));
                },
              );
            },
          );
        });
  }
}
