import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:go_router/go_router.dart';
import 'package:core_ui/widgets/common/async_value_widget.dart';
import 'package:core_domain/core_domain.dart';
import 'package:parnter2024/app/features/requests/controller/requests_notifier.dart';
import 'package:parnter2024/app/features/requests/view/widgets/bottom_sheet_reason.dart';
import 'package:parnter2024/app/features/requests/view/widgets/request_card.dart';

class RequestAcceptTab extends ConsumerWidget {
  const RequestAcceptTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // *** providers
    final requestListValue =
        ref.watch(requestsNotifierProvider).requestsAccepted;

    // *** widget
    return AsyncValueWidget(
        value: requestListValue,
        data: (requestList) {
          return ListView.builder(
            itemCount: requestList.length,
            padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            itemBuilder: (context, index) {
              return RequestCard(
                request: requestList[index],
                onReject: () {
                  showFSheet(
                    context: context,
                    side: FLayout.btt,
                    mainAxisMaxRatio: 0.9,
                    builder: (context) => BottomSheetReason(
                      onConfirm: (reason) {
                        context.pop();
                        log('******hello $reason******');
                        ref
                            .read(requestsNotifierProvider.notifier)
                            .updateRequest(
                              requestList[index].copyWith(
                                status: RequestStatus.canceled,
                                rejectReason: reason,
                                cancelInitiator: CancelInitiator.partner,
                              ),
                            );
                      },
                    ),
                  );
                  // showModalBottomSheet(
                  //     isScrollControlled: true,
                  //     useSafeArea: true,
                  //     context: context,
                  //     builder: (context) {
                  //       return Padding(
                  //         padding: EdgeInsets.only(
                  //             bottom: MediaQuery.of(context).viewInsets.bottom),
                  //         child: BottomSheetReason(
                  //           onConfirm: (reason) {
                  //             context.pop();
                  //             log('******hello $reason******');

                  //             ref
                  //                 .read(requestsNotifierProvider.notifier)
                  //                 .updateRequest(
                  //                   requestList[index].copyWith(
                  //                       status: RequestStatus.canceled,
                  //                       rejectReason: reason,
                  //                       cancelInitiator:
                  //                           CancelInitiator.partner),
                  //                 );
                  //           },
                  //         ),
                  //       );
                  //     });
                },
              );
            },
          );
        });
  }
}
