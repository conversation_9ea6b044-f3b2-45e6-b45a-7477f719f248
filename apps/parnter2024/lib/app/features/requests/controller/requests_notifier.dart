import 'dart:developer';
import 'package:core_data/core_data.dart';
import 'package:core_domain/core_domain.dart';
import 'package:parnter2024/app/common/providers/partner_auto_inject_providers.dart';
import 'package:parnter2024/app/features/requests/controller/requests_state.dart';
import 'package:parnter2024/app/features/authentication/repo/auth_repo.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'requests_notifier.g.dart';

@riverpod
class RequestsNotifier extends _$RequestsNotifier {
  @override
  RequestsState build() {
    log('rebuild*****************--------');
    _initSubscriptions();

    return RequestsState();
  }

  void _initSubscriptions() {
    final partnerID = ref.watch(authRepoProvider).currentUser!.uid;
    
    ref.listen<AsyncValue<List<Request>>>(
      acceptedRequestListStreamByPartnerProvider(partnerID),
      (_, accepted) => state = state.copyWith(requestsAccepted: accepted),
    );

    ref.listen<AsyncValue<List<Request>>>(
      newRequestListStreamByPartnerProvider(partnerID),
      (_, pending) => state = state.copyWith(requestsPending: pending),
    );

    ref.listen<AsyncValue<List<Request>>>(
      rejectedAndCanceledRequestListStreamByPartnerProvider(partnerID),
      (_, rejected) => state = state.copyWith(requestsRejected: rejected),
    );
  }

  Future<void> updateRequest(Request request) async {
    state = state.copyWith(isUpdating: true, activeRequestID: request.id);
    log("[Partner App] Attempting to update request ${request.id} to status ${request.status}");

    final requestRepo = ref.read(requestRepositoryProvider);
    final eventRepo = ref.read(partnerEventRepoProvider); // Для проверки слотов

    try {
      // --- ВАЖНО: ПРОВЕРКА ПЕРЕД ОДОБРЕНИЕМ ---
      // Если мы пытаемся одобрить заявку (независимо от предыдущего статуса)
      if (request.status == RequestStatus.accepted) {
        log("[Partner App] Checking slots for accepting request ${request.id}");
        // Получаем *текущее* состояние Event ДО изменения статуса Request
        final currentEvent = await eventRepo.fetchEvent(request.eventID);
        final availableSlots = currentEvent.slots - currentEvent.slotsReserved;

        if (request.slots > availableSlots) {
          log("[Partner App] Not enough slots for request ${request.id}. Available: $availableSlots, Requested: ${request.slots}");
          // Выбрасываем ошибку, которая будет поймана ниже и показана пользователю
          throw 'Недостаточно свободных мест. Доступно: $availableSlots, Запрошено: ${request.slots}';
        }
        log("[Partner App] Slots available for request ${request.id}. Proceeding to update request status.");
      }
      // --- КОНЕЦ ВАЖНОЙ ПРОВЕРКИ ---

      // Теперь, когда проверка (если нужна) пройдена, просто обновляем Request
      // Cloud Function сама обработает обновление Event
      await requestRepo.updateRequest(request);
      log("[Partner App] Request ${request.id} status updated to ${request.status} in Firestore.");

      // Обновляем состояние контроллера после успешного обновления Request
      state = state.copyWith(
        isUpdating: false,
        activeRequestID: null,
        feedback: RequestFeedbackState.success(request.status),
      );
      log("[Partner App] State updated successfully for request --------------------------------${request.id}.");
      log("[Partner App] State: *******************************---------------${state.feedback}");
    } catch (e, st) {
      log("[Partner App] Error updating request ${request.id}: $e\n$st");
      state = state.copyWith(
        isUpdating: false,
        feedback: RequestFeedbackState.error(
          e.toString(),
        ),
        activeRequestID: null,
      );
    }
  }

  void clearFeedback() {
    state = state.copyWith(feedback: null);
  }
}
