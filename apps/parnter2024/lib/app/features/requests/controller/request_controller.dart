// import 'dart:developer';
// import 'package:core_data/core_data.dart';
// import 'package:core_domain/core_domain.dart';
// import 'package:parnter2024/app/common/enums/request_status.dart';
// import 'package:parnter2024/app/common/providers/partner_event_providers.dart';
// import 'package:parnter2024/app/repo/request_repo/request_repo.dart';
// import 'package:core_utils/core_utils.dart' show NotifierMounted;
// import 'package:riverpod_annotation/riverpod_annotation.dart';
// part 'request_controller.g.dart';

// @riverpod
// class RequestController extends _$RequestController with NotifierMounted {
//   @override
//   AsyncValue<RequestStatus?> build([String main = 'main']) {
//     ref.onDispose(setUnmounted);
//     return const AsyncValue.data(null);
//   }

//   Future<void> updateRequest(Request request) async {
//     state = const AsyncLoading();
//     log("[Partner App] Attempting to update request ${request.id} to status ${request.status}");

//     final requestRepo = ref.read(requestRepoProvider);
//     final eventRepo = ref.read(eventRepoProvider); // Для проверки слотов

//     try {
//       // --- ВАЖНО: ПРОВЕРКА ПЕРЕД ОДОБРЕНИЕМ ---
//       // Если мы пытаемся одобрить заявку (независимо от предыдущего статуса)
//       if (request.status == RequestStatus.accepted) {
//         log("[Partner App] Checking slots for accepting request ${request.id}");
//         // Получаем *текущее* состояние Event ДО изменения статуса Request
//         final currentEvent = await eventRepo.fetchEvent(request.eventID);
//         final availableSlots = currentEvent.slots - currentEvent.slotsReserved;

//         if (request.slots > availableSlots) {
//           log("[Partner App] Not enough slots for request ${request.id}. Available: $availableSlots, Requested: ${request.slots}");
//           // Выбрасываем ошибку, которая будет поймана ниже и показана пользователю
//           throw 'Недостаточно свободных мест. Доступно: $availableSlots, Запрошено: ${request.slots}';
//         }
//         log("[Partner App] Slots available for request ${request.id}. Proceeding to update request status.");
//       }
//       // --- КОНЕЦ ВАЖНОЙ ПРОВЕРКИ ---

//       // Теперь, когда проверка (если нужна) пройдена, просто обновляем Request
//       // Cloud Function сама обработает обновление Event
//       await requestRepo.updateRequest(request);
//       log("[Partner App] Request ${request.id} status updated to ${request.status} in Firestore.");

//       // Обновляем состояние контроллера после успешного обновления Request
//       if (mounted) {
//         state = AsyncValue.data(request.status);
//         log("[Partner App] State updated successfully for request ${request.id}.");
//       }
//     } catch (e, st) {
//       log("[Partner App] Error updating request ${request.id}: $e\n$st");
//       if (mounted) {
//         state = AsyncValue.error(e, st);
//       }
//     }
//   }
// }
