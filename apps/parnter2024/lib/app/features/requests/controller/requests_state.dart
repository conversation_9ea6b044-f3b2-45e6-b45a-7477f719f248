import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:core_domain/core_domain.dart';

part 'requests_state.freezed.dart';

@freezed
class RequestsState with _$RequestsState {
  factory RequestsState({
    @Default(false) bool isUpdating,
    String? activeRequestID,
    RequestFeedbackState? feedback,
    @Default(AsyncData([])) AsyncValue<List<Request>> requestsAccepted,
    @Default(AsyncData([])) AsyncValue<List<Request>> requestsPending,
    @Default(AsyncData([])) AsyncValue<List<Request>> requestsRejected,
  }) = _RequestsState;
}

@freezed
sealed class RequestFeedbackState with _$RequestFeedbackState {
  const factory RequestFeedbackState.success(RequestStatus status) =
      RequestSuccess;
  const factory RequestFeedbackState.error(String message) = RequestError;
}
