import 'dart:developer';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:parnter2024/app/features/authentication/repo/fcm_token/fcm_token_repo.dart';
import 'package:parnter2024/app/features/authentication/repo/auth_repo.dart';
import 'package:parnter2024/app/routes/go_router_refresh_stream.dart';
import 'package:parnter2024/app/routes/typed_routes.dart';
import 'package:parnter2024/main.dart';

final goRouterProvider = Provider<GoRouter>((ref) {
  final firebaseAuth = ref.watch(authRepoProvider);
  return GoRouter(
    navigatorKey: navigatorKey,
    debugLogDiagnostics: true,
    initialLocation: '/signin',
    redirect: (context, state) async {
      final isLoggedIn = firebaseAuth.currentUser != null;

      // Сохраняем токен если пользователь авторизован
      if (isLoggedIn) {
        try {
          await FcmTokenRepo()
              .saveTokenToDatavase(firebaseAuth.currentUser!.uid);
        } catch (e) {
          log('Ошибка сохранения токена: $e');
        }
      }
      log('*****************************$isLoggedIn   partner not null');

      if (!isLoggedIn) {
        if (state.uri.path != '/otp' && state.uri.path != '/signin') {
          return '/signin';
        }
      }

      if (isLoggedIn) {
        if (state.uri.path == '/signin' || state.uri.path == '/otp') {
          return '/home';
        }
      }

      return null;
    },
    refreshListenable: GoRouterRefreshStream(firebaseAuth.authStateChanges()),
    routes: $appRoutes,
  );
});
