import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:parnter2024/app/features/addEvent/view/add_event_page.dart';
import 'package:parnter2024/app/features/authentication/view/otp_page.dart';
import 'package:parnter2024/app/features/authentication/view/sign_in_page.dart';
import 'package:parnter2024/app/features/client/client_detail_page.dart';
import 'package:parnter2024/app/features/event_details/view/client_list_page.dart';
import 'package:parnter2024/app/features/event_details/view/event_detail_page.dart';
import 'package:parnter2024/app/features/event_details/view/event_edit/event_edit_page.dart';
import 'package:parnter2024/app/features/home/<USER>/home_page.dart';
import 'package:parnter2024/app/features/menu/about/about_page.dart';
import 'package:parnter2024/app/features/menu/menu_page.dart';
import 'package:parnter2024/app/features/menu/menu_product/menu_product_page.dart';
import 'package:parnter2024/app/features/menu/support/support_page.dart';
import 'package:parnter2024/app/features/profile/profile_page.dart';
import 'package:parnter2024/app/features/product_edit/product_edit_page.dart';
import 'package:parnter2024/app/features/product_new/view/new_product_page.dart';
import 'package:parnter2024/app/features/request_detail/view/request_detail_page.dart';
import 'package:parnter2024/app/features/requests/request_page.dart';
import 'package:core_domain/core_domain.dart' show Client, Request;
import 'package:core_domain/core_domain.dart' show Product;
import 'package:parnter2024/root_page.dart';

part 'typed_routes.g.dart';

// Типизированный StatefulShellRoute для нижней навигации
@TypedStatefulShellRoute<MainShellRouteData>(
  branches: <TypedStatefulShellBranch<StatefulShellBranchData>>[
    TypedStatefulShellBranch<HomeBranchData>(
      routes: <TypedRoute<RouteData>>[
        TypedGoRoute<HomeRoute>(path: '/home'),
      ],
    ),
    TypedStatefulShellBranch<RequestBranchData>(
      routes: <TypedRoute<RouteData>>[
        TypedGoRoute<RequestRoute>(path: '/request'),
      ],
    ),
    TypedStatefulShellBranch<MenuBranchData>(
      routes: <TypedRoute<RouteData>>[
        TypedGoRoute<MenuRoute>(path: '/menu'),
      ],
    ),
  ],
)
class MainShellRouteData extends StatefulShellRouteData {
  const MainShellRouteData();

  @override
  Widget builder(
    BuildContext context,
    GoRouterState state,
    StatefulNavigationShell navigationShell,
  ) {
    return RootPage(navigationShell: navigationShell);
  }
}

// Классы веток для StatefulShellRoute
class HomeBranchData extends StatefulShellBranchData {
  const HomeBranchData();
}

class RequestBranchData extends StatefulShellBranchData {
  const RequestBranchData();
}

class MenuBranchData extends StatefulShellBranchData {
  const MenuBranchData();
}

// Классы маршрутов (без отдельных аннотаций, так как они часть shell)
class HomeRoute extends GoRouteData {
  const HomeRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const MyHomePage();
}

class RequestRoute extends GoRouteData {
  const RequestRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) =>
      const RequestPage();
}

class MenuRoute extends GoRouteData {
  const MenuRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const MenuPage();
}

// Страница авторизации
@TypedGoRoute<SignInRoute>(
  path: '/signin',
)
class SignInRoute extends GoRouteData {
  const SignInRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const SignInPage();
}

// Страница OTP
@TypedGoRoute<OtpRoute>(
  path: '/otp',
)
class OtpRoute extends GoRouteData {
  const OtpRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const OtpPage();
}

// Страница профиля
@TypedGoRoute<ProfileRoute>(
  path: '/profile',
)
class ProfileRoute extends GoRouteData {
  const ProfileRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) =>
      const ProfilePage();
}

// Страница поддержки
@TypedGoRoute<SupportRoute>(
  path: '/support',
)
class SupportRoute extends GoRouteData {
  const SupportRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) =>
      const SupportPage();
}

// Страница "О приложении"
@TypedGoRoute<AboutRoute>(
  path: '/about',
)
class AboutRoute extends GoRouteData {
  const AboutRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) => const AboutPage();
}

// Страница деталей события с вложенным маршрутом для редактирования
@TypedGoRoute<EventDetailRoute>(
  path: '/event-details',
  routes: [
    TypedGoRoute<EventEditRoute>(
      path: 'event-edit',
    ),
  ],
)
class EventDetailRoute extends GoRouteData {
  const EventDetailRoute({required this.eventId});

  final String eventId;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return EventDetailPage(eventId: eventId);
  }
}

// Маршрут для редактирования события
class EventEditRoute extends GoRouteData {
  const EventEditRoute({required this.eventId});

  final String eventId;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return EventEditPage(eventId: eventId);
  }
}

// Страница добавления события
@TypedGoRoute<AddEventRoute>(
  path: '/home/<USER>',
)
class AddEventRoute extends GoRouteData {
  const AddEventRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) =>
      const AddEventPage();
}

// Страница списка клиентов
@TypedGoRoute<ClientListEventRoute>(
  path: '/client-list-event',
)
class ClientListEventRoute extends GoRouteData {
  const ClientListEventRoute({required this.$extra});

  final List<Client> $extra;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return ClientListEventPage(clientList: $extra);
  }
}

// Страница с деталями заявки (с использованием $extra)
@TypedGoRoute<RequestDetailRoute>(
  path: '/request-detail',
)
class RequestDetailRoute extends GoRouteData {
  const RequestDetailRoute({required this.$extra});

  final Request $extra;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return RequestDetailPage(newRequest: $extra);
  }
}

// Страница с деталями клиента (с использованием параметра id)
@TypedGoRoute<ClientDetailRoute>(
  path: '/client-detail/:id',
)
class ClientDetailRoute extends GoRouteData {
  const ClientDetailRoute({required this.id});

  final String id;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return ClientDetailPage(clientId: id);
  }
}

// Главная страница продуктов
@TypedGoRoute<MenuProductRoute>(
  path: '/menu-product',
)
class MenuProductRoute extends GoRouteData {
  const MenuProductRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) =>
      const MenuProductPage();
}

// Страница создания нового продукта
@TypedGoRoute<NewProductRoute>(
  path: '/new-product',
)
class NewProductRoute extends GoRouteData {
  const NewProductRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const NewProductPage();
  }
}

// Страница редактирования продукта (с использованием $extra)
@TypedGoRoute<EditProductRoute>(
  path: '/edit-product',
)
class EditProductRoute extends GoRouteData {
  const EditProductRoute({required this.$extra});

  final Product $extra;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return EditProductPage(product: $extra);
  }
}
