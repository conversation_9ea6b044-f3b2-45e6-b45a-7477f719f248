import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

void errorDialog(BuildContext context, String errorText) {
  showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('Ошибка'),
          content: Text(errorText),
          actions: [
            TextButton(
                onPressed: () {
                  context.pop();
                },
                child: Text('Ok111')),
          ],
        );
      });
}
