import 'package:flutter/material.dart';
import 'package:forui/forui.dart';

class MyTextFromField extends StatelessWidget {
  const MyTextFromField({
    super.key,
    required this.textController,
    required this.labelName,
    this.validator,
    this.keyboardType,
    this.maxLines,
    this.readOnly,
    this.enabled,
  });
  final TextEditingController textController;
  final String labelName;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final int? maxLines;
  final bool? readOnly;
  final bool? enabled;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 16, right: 16, left: 16),
      child: FTextFormField(
        controller: textController,
        label: Text(labelName),
        maxLines: maxLines,
        enabled: enabled ?? true,
        keyboardType: keyboardType,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        validator: validator,
        clearable: (controller) =>
            controller.text.isNotEmpty && !(readOnly ?? false),
        readOnly: readOnly ?? false,
      ),
    );
  }
}
