enum RejectReason {
  noSlots('Недостаточно свободных мест'),
  dateUnavailable('Дата уже недоступна'),
  other('Другое');

  final String label;
  const RejectReason(this.label);

  // Вспомогательный метод для получения текста причины
  static String getReasonText(RejectReason reason, String? customText) {
    if (reason == RejectReason.other && customText?.isNotEmpty == true) {
      return customText!;
    } else {
      return reason.label;
    }
  }
}
