import 'package:talker_flutter/talker_flutter.dart';

class TalkerService {
  TalkerService._();

  static final TalkerService _instance = TalkerService._();
  static final Talker _talker = TalkerFlutter.init();

  static TalkerService get instance => _instance;

  static Talker get talker => _talker;
}


/// Вспомогательные методы для удобства использования
  // void debug(String message) => _talker.debug(message);
  // void info(String message) => _talker.info(message);
  // void error(String message) => _talker.error(message);
  // void warning(String message) => _talker.warning(message);
  // void critical(String message) => _talker.critical(message);
  // void handle(Object error, StackTrace stackTrace) => _talker.handle(error, stackTrace);