import 'dart:developer';
import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:parnter2024/app/common/services/talker_service.dart';
import 'package:parnter2024/app/routes/typed_routes.dart';
import 'package:parnter2024/main.dart';

@pragma('vm:entry-point')
Future<void> backgroudHandler(RemoteMessage message) async {
  final talker = TalkerService.talker;
  talker
      .debug('************backgroudHandler ${message.data.toString()}*******');
  talker.debug(
      '************backgroudHandler  ${message.notification?.title}*********');
  openNotification();
}

class PushNotificationHelper {
  static String fcmToken = '';
  static Future<void> initialized() async {
    await Firebase.initializeApp();
    //await FirebaseMessaging.instance.requestPermission();

    if (Platform.isAndroid) {
      // *** local notification initialize
      NotificationHelper.initialized();
      await FirebaseMessaging.instance.requestPermission();
    } else if (Platform.isIOS) {
      await FirebaseMessaging.instance.requestPermission();
    }

    FirebaseMessaging.onBackgroundMessage(backgroudHandler);
    getDeviceTokenToSendNotification();
    await FirebaseMessaging.instance
        .setForegroundNotificationPresentationOptions(
            alert: true, badge: true, sound: true);

    // *** if APP is Terminated state & used click notification
    FirebaseMessaging.instance.getInitialMessage().then((message) {
      log('FirebaseMessaging.instance.getInitialMessage');

      if (message != null) {
        log('***********new Notification**********');
      }
    });

    // *** if App is Forground, this method work
    FirebaseMessaging.onMessage.listen((message) {
      log('*************FirebaseMessaging.onMessage.listen***********');

      if (message.notification != null) {
        log('**********${message.notification!.title}**********');
        log('**********${message.notification!.body}**********');
        log('**********${message.data}**********');

        // *** local Notification to display
        if (Platform.isAndroid) {
          NotificationHelper.displayNotification(message);
        }
      }
    });

    // *** App on Backroud, not Terminated
    FirebaseMessaging.onMessageOpenedApp.listen((message) {
      log('*************FirebaseMessaging.onMessageOpenedApp***********');

      if (message.notification != null) {
        log('**********${message.notification!.title}**********');
        log('**********${message.notification!.body}**********');
        log('**********${message.data}**********');
        openNotification();
      }
    });
  }

  static Future<void> getDeviceTokenToSendNotification() async {
    fcmToken = (await FirebaseMessaging.instance.getToken()).toString();
    log('token: $fcmToken');
  }
} // end PushNotificationHelper

class NotificationHelper {
  static final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // *** initialize local notification
  static void initialized() {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    flutterLocalNotificationsPlugin.initialize(
      InitializationSettings(
        android: initializationSettingsAndroid,
      ),
      onDidReceiveBackgroundNotificationResponse: localBackgroundHandler,
      onDidReceiveNotificationResponse: localBackgroundHandler,
    );
  }

  static void displayNotification(RemoteMessage message) async {
    log('**********************displayNotification ********');
    try {
      final id = DateTime.now().minute ~/ 1000;
      const notificationDetail = NotificationDetails(
        android: AndroidNotificationDetails(
          'push_notification',
          'push_notification_channelName',
          importance: Importance.max,
          priority: Priority.high,
        ),
      );

      // *** display local notification
      await flutterLocalNotificationsPlugin.show(
        id,
        message.notification!.title,
        message.notification!.body,
        notificationDetail,
        payload: message.data['route'],
      );
    } on Exception catch (e) {
      log(e.toString());
    }
  }
}

// *** local notification
Future<void> localBackgroundHandler(NotificationResponse data) async {
  log('localBackgroundHandler ${data.toString()}');
  openNotification();
}

void openNotification() {
  final context = navigatorKey.currentContext;

  if (context != null) {
    log('**************** context not null');
    const HomeRoute().go(context);
  } else if (navigatorKey.currentState != null) {
    log('********* navigatorKey.currentState not null');
    const AddEventRoute().go(navigatorKey.currentContext!);
  }
}
