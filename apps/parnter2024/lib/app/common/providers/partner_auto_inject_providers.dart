/// Wrapper providers для автоматической инъекции partner ID
/// 
/// Эти провайдеры предоставляют удобный интерфейс для работы с данными
/// текущего партнера без необходимости вручную передавать partner ID.
/// 
/// ВАЖНО: Используйте эти провайдеры вместо прямого обращения к core_data
/// провайдерам, чтобы автоматически фильтровать данные по текущему партнеру.
/// 
/// Примеры использования:
/// ```dart
/// // Получить список событий текущего партнера
/// final events = ref.watch(partnerEventListStreamProvider);
/// 
/// // Получить список продуктов текущего партнера
/// final products = ref.watch(partnerProductListStreamProvider);
/// 
/// // Получить данные текущего партнера
/// final partner = ref.watch(partnerSelfStreamProvider);
/// ```

import 'dart:developer';

import 'package:core_data/core_data.dart';
import 'package:core_domain/core_domain.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:parnter2024/app/features/authentication/repo/auth_repo.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'partner_auto_inject_providers.g.dart';

// ============================================================================
// PARTNER PROVIDERS
// ============================================================================

/// Поток данных текущего партнёра
/// Автоматически использует UID текущего авторизованного пользователя
@riverpod
AsyncValue<Partner> partnerSelfStream(Ref ref) {
  final currentUser = ref.watch(authRepoProvider).currentUser;
  if (currentUser == null) {
    log('User not authenticated in partnerSelfStream');
    return const AsyncValue.loading();
  }
  return ref.watch(partnerStreamProviderProvider(currentUser.uid));
}

// ============================================================================
// EVENT PROVIDERS
// ============================================================================

/// Получить список событий текущего партнера (одноразовый запрос)
@riverpod
Future<List<Event>> partnerFetchEventList(Ref ref) async {
  final userID = ref.watch(authRepoProvider).currentUser!.uid;
  return ref.watch(fetchEventListByPartnerProvider(userID).future);
}

/// Поток событий текущего партнера
@riverpod
AsyncValue<List<Event>> partnerEventListStream(Ref ref) {
  final userID = ref.watch(authRepoProvider).currentUser!.uid;
  return ref.watch(eventListStreamByPartnerProvider(userID));
}

/// Репозиторий событий с CRUD операциями
/// Примечание: Этот провайдер не требует partner ID, так как
/// операции уже учитывают контекст партнера внутри репозитория
@Riverpod(keepAlive: true)
IEventRepository partnerEventRepo(Ref ref) {
  return ref.watch(myServiceRepositoryProvider);
}

// ============================================================================
// PRODUCT PROVIDERS
// ============================================================================

/// Получить список продуктов текущего партнера (одноразовый запрос)
@riverpod
Future<List<Product>> partnerFetchProductList(Ref ref) async {
  final userID = ref.watch(authRepoProvider).currentUser!.uid;
  return await ref.watch(
    fetchProductListByPartnerProvider(userID).future,
  );
}

/// Поток продуктов текущего партнера
@riverpod
AsyncValue<List<Product>> partnerProductListStream(Ref ref) {
  final userID = ref.watch(authRepoProvider).currentUser!.uid;
  return ref.watch(productListStreamByPartnerProvider(userID));
}

// ============================================================================
// REQUEST PROVIDERS
// ============================================================================
// Примечание: На данный момент нет wrapper-провайдеров для заявок,
// так как они используют core_data провайдеры напрямую.
// При необходимости можно добавить:
// - partnerRequestListStream
// - partnerAcceptedRequestsStream
// - partnerNewRequestsStream
// - partnerRejectedRequestsStream