# Provider Architecture for Partner App

## Overview

This directory contains provider files that manage state and data access for the partner app. The providers are organized by their functionality rather than by domain objects.

## Files

### partner_auto_inject_providers.dart
**Purpose**: Wrapper providers that automatically inject the current partner's ID

These providers simplify data access by automatically filtering data for the currently authenticated partner. They wrap core_data providers and add partner-specific context.

**Available providers**:
- `partnerSelfStreamProvider` - Stream of current partner's data
- `partnerEventListStreamProvider` - Stream of partner's events
- `partnerFetchEventListProvider` - One-time fetch of partner's events
- `partnerEventRepoProvider` - Event repository with CRUD operations
- `partnerProductListStreamProvider` - Stream of partner's products
- `partnerFetchProductListProvider` - One-time fetch of partner's products

**Usage**:
```dart
// Instead of manually passing partner ID:
ref.watch(eventListStreamByPartnerProvider(partnerID))

// Use the wrapper that auto-injects current partner ID:
ref.watch(partnerEventListStreamProvider)
```

### firebase_providers.dart
**Purpose**: Core Firebase service providers (Firestore, Auth, Storage, etc.)

### Deprecated Files (to be removed)
- `partner_providers.dart` - Moved to partner_auto_inject_providers.dart
- `product_providers.dart` - Moved to partner_auto_inject_providers.dart
- `partner_event_providers.dart` - Moved to partner_auto_inject_providers.dart

## Architecture Decisions

1. **Why wrapper providers?**
   - Reduces boilerplate by auto-injecting partner ID
   - Ensures consistent partner context across the app
   - Simplifies code in UI components

2. **Naming convention**:
   - All partner-specific wrappers use `partner` prefix
   - Example: `partnerEventListStreamProvider` instead of `eventListStreamProvider`
   - This prevents naming conflicts with core_data providers

3. **When to use wrappers vs core_data directly**:
   - Use wrappers when you need current partner's data
   - Use core_data directly when you need data from other partners or unfiltered access

## Migration Guide

If you're updating existing code:

```dart
// Old (using individual provider files):
import 'package:parnter2024/app/common/providers/partner_event_providers.dart';
final events = ref.watch(eventListStreamProvider);

// New (using consolidated file):
import 'package:parnter2024/app/common/providers/partner_auto_inject_providers.dart';
final events = ref.watch(partnerEventListStreamProvider);
```

## Future Considerations

- Add request-specific wrappers if needed
- Consider adding client-specific wrappers
- Monitor for performance implications of wrapper layers