import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:core_domain/core_domain.dart';

/// Унифицированный виджет для нижних кнопок действий
/// Поддерживает как одиночные, так и двойные кнопки
class BottomActionButton extends StatelessWidget {
  const BottomActionButton._({
    super.key,
    required this.padding,
    required this.child,
  });

  /// Создает простую одиночную кнопку
  BottomActionButton.simple({
    Key? key,
    required String text,
    required VoidCallback? onPressed,
    bool isLoading = false,
    EdgeInsets padding = const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
  }) : this._(
          key: key,
          padding: padding,
          child: FButton(
            onPress: onPressed,
            child: isLoading
                ? const FProgress.circularIcon()
                : Text(text),
          ),
        );

  /// Создает двойные кнопки для управления заявками
  BottomActionButton.actions({
    Key? key,
    required RequestStatus requestStatus,
    required VoidCallback? onAccept,
    required VoidCallback? onReject,
    bool isLoading = false,
    EdgeInsets padding = const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
  }) : this._(
          key: key,
          padding: padding,
          child: _ActionButtons(
            requestStatus: requestStatus,
            onAccept: onAccept,
            onReject: onReject,
            isLoading: isLoading,
          ),
        );

  /// Создает кастомную конфигурацию кнопок
  const BottomActionButton.custom({
    Key? key,
    required Widget child,
    EdgeInsets padding = const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
  }) : this._(
          key: key,
          padding: padding,
          child: child,
        );

  final EdgeInsets padding;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      child: child,
    );
  }
}

/// Внутренний виджет для отображения кнопок действий
class _ActionButtons extends StatelessWidget {
  const _ActionButtons({
    required this.requestStatus,
    required this.onAccept,
    required this.onReject,
    required this.isLoading,
  });

  final RequestStatus requestStatus;
  final VoidCallback? onAccept;
  final VoidCallback? onReject;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // *** Для статуса pending - показываем обе кнопки
        if (requestStatus == RequestStatus.pending) ...[
          Expanded(
            child: FButton(
              style: FButtonStyle.secondary,
              onPress: onReject,
              child: isLoading
                  ? const FProgress.circularIcon()
                  : const Text('Отклонить'),
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: FButton(
              style: FButtonStyle.primary,
              onPress: onAccept,
              child: isLoading
                  ? const FProgress.circularIcon()
                  : const Text('Одобрить'),
            ),
          ),
        ]
        
        // *** Для статуса accepted - показываем только кнопку отмены
        else if (requestStatus == RequestStatus.accepted) ...[
          Expanded(
            child: FButton(
              style: FButtonStyle.secondary,
              onPress: onReject,
              child: isLoading
                  ? const FProgress.circularIcon()
                  : const Text('Отменить'),
            ),
          ),
        ]
        
        // *** Для остальных статусов - ничего не показываем
        else ...[
          const SizedBox.shrink(),
        ]
      ],
    );
  }
}