// Функция валидации числового значения
String? validateNumber(String? value) {
  if (value == null || value.isEmpty) {
    return 'Это поле не может быть пустым';
  }
  final number = int.tryParse(value);
  if (number == null) {
    return 'Пожалуйста, введите корректное число';
  }
  return null;
}

// Функция валидации строковых значении
String? validateString(String? value) {
  if (value == null || value.isEmpty) {
    return 'Это поле не может быть пустым';
  }
  return null;
}
