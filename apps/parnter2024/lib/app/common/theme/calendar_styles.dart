import 'package:flutter/material.dart';
import 'package:forui/forui.dart';
import 'package:table_calendar/table_calendar.dart';

/// Класс для унифицированных стилей календаря в приложении
class AppCalendarStyles {
  final FThemeData theme;

  AppCalendarStyles(this.theme);

  /// Получение стилей календаря
  CalendarStyle getCalendarStyle({bool isRectangular = false}) {
    if (isRectangular) {
      return _getRectangularCalendarStyle();
    } else {
      return _getCircularCalendarStyle();
    }
  }

  /// Стили для календаря с прямоугольными ячейками
  CalendarStyle _getRectangularCalendarStyle() {
    final borderRadius = theme.style.borderRadius;
    final colors = theme.colors;

    final defaultDecoration = BoxDecoration(
      shape: BoxShape.rectangle,
      borderRadius: borderRadius,
    );

    return CalendarStyle(
      outsideDaysVisible: true,
      defaultDecoration: defaultDecoration,
      selectedDecoration: BoxDecoration(
        shape: BoxShape.rectangle,
        borderRadius: borderRadius,
        color: colors.primary,
      ),
      todayDecoration: BoxDecoration(
        shape: BoxShape.rectangle,
        borderRadius: borderRadius,
        // color: colors.border,
        //border: Border.all(color: colors.border),
      ),
      todayTextStyle: theme.typography.sm.copyWith(
        color: colors.primary,
        height: 1,
        decoration: TextDecoration.underline,
      ),
      selectedTextStyle: theme.typography.base.copyWith(
        color: colors.primaryForeground,
        height: 1,
      ),
      defaultTextStyle: theme.typography.sm.copyWith(
        color: colors.primary,
        height: 1,
      ),
      weekendTextStyle: theme.typography.sm.copyWith(
        color: colors.primary,
        height: 1,
      ),
      weekendDecoration: defaultDecoration,
      outsideDecoration: defaultDecoration,
    );
  }

  /// Стили для календаря с круглыми ячейками
  CalendarStyle _getCircularCalendarStyle() {
    final colors = theme.colors;

    return CalendarStyle(
      weekendTextStyle: TextStyle(fontWeight: FontWeight.bold),
      selectedDecoration: BoxDecoration(
        color: colors.primary,
        shape: BoxShape.circle,
      ),
      selectedTextStyle: theme.typography.base.copyWith(
        color: colors.primaryForeground,
        height: 1,
      ),
      todayDecoration: BoxDecoration(
        border: Border.all(color: colors.mutedForeground),
        shape: BoxShape.circle,
      ),
      todayTextStyle: theme.typography.sm.copyWith(
        color: colors.primary,
        height: 1,
      ),
    );
  }

  /// Получение стилей заголовка календаря
  HeaderStyle getHeaderStyle(
      {bool showFormatButton = true, bool titleCentered = true}) {
    return HeaderStyle(
      formatButtonVisible: showFormatButton,
      titleCentered: titleCentered,
      titleTextStyle: theme.typography.sm,
      headerPadding: EdgeInsets.zero,
      formatButtonShowsNext: false,
      formatButtonDecoration: BoxDecoration(
        borderRadius: theme.style.borderRadius,
        border: Border.all(color: theme.colors.border, width: 0.5),
      ),
    );
  }

  /// Доступные форматы календаря с локализованными названиями
  Map<CalendarFormat, String> getAvailableCalendarFormats(
      {bool includeWeeks = true}) {
    if (includeWeeks) {
      return const {
        CalendarFormat.month: 'Месяц',
        CalendarFormat.twoWeeks: '2 недели',
        CalendarFormat.week: 'Неделя',
      };
    } else {
      return const {
        CalendarFormat.month: 'Месяц',
        CalendarFormat.week: 'Неделя',
      };
    }
  }
}
