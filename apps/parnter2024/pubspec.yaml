name: parnter2024
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.5.3

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  table_calendar: ^3.1.2
  intl: ^0.20.2
  flutter_localizations:
    sdk: flutter
  flutter_form_builder: ^10.0.1
  form_builder_image_picker: ^4.1.0
  image_picker: ^1.1.2
  form_builder_validators: ^11.0.0
  intl_phone_number_input: ^0.7.4
  pinput: ^5.0.0
  freezed_annotation: ^2.4.4
  json_annotation: ^4.9.0
  image_cropper: ^8.0.2
  flutter_riverpod: ^2.6.1
  riverpod_annotation: ^2.6.1
  talker_riverpod_logger: ^4.5.1
  go_router: ^15.1.2
  uuid: ^4.5.1
  firebase_core: ^3.8.0
  cloud_firestore: ^5.5.0
  firebase_storage: ^12.3.6
  firebase_auth: ^5.3.3
  firebase_messaging: ^15.1.5
  flutter_local_notifications: ^18.0.1
  flex_color_scheme: ^8.0.1
  talker_flutter: ^4.5.2
  hooks_riverpod: ^2.6.1
  flutter_hooks: ^0.20.5
  url_launcher: ^6.3.1
  share_plus: ^10.1.3
  firebase_app_check: ^0.3.2+4
  font_awesome_flutter: ^10.7.0
  package_info_plus: ^8.3.0
  forui: ^0.12.0
  cached_network_image: ^3.4.1
  shimmer: ^3.0.0
  core_domain:
    path: ../../packages/core_domain
  core_data:
    path: ../../packages/core_data
  core_ui:
    path: ../../packages/core_ui
  core_utils:
    path: ../../packages/core_utils

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  build_runner: ^2.4.13
  freezed: ^2.5.7
  json_serializable: ^6.9.0
  riverpod_generator: ^2.6.3
  custom_lint: ^0.7.0
  riverpod_lint: ^2.6.3
  go_router_builder: ^2.9.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  assets:
    - shorebird.yaml
    - assets/images/
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
