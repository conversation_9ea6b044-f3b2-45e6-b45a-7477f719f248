import 'package:core_theme/core_theme.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class RootPage extends StatelessWidget {
  const RootPage({super.key, required this.navigationShell});
  final StatefulNavigationShell navigationShell;

  static const double _iconSize = 24.0;
  static const double _selectedIconSize = 28.0;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return Scaffold(
      body: navigationShell,
      bottomNavigationBar: NavigationBar(
        onDestinationSelected: (value) {
          navigationShell.goBranch(value,
              initialLocation: value == navigationShell.currentIndex);
        },
        selectedIndex: navigationShell.currentIndex,
        shadowColor: theme.colors.primary,
        labelTextStyle: WidgetStateProperty.resolveWith<TextStyle?>((states) {
          if (states.contains(WidgetState.selected)) {
            return TextStyle(
              color: theme.colors.primary,
              fontWeight: FontWeight.w600,
            );
          }
          return TextStyle(
            color: theme.colors.mutedForeground,
            fontWeight: FontWeight.w500,
          );
        }),
        labelBehavior: NavigationDestinationLabelBehavior.alwaysShow,
        height: 64,
        animationDuration: const Duration(milliseconds: 300),
        destinations: [
          NavigationDestination(
            icon: Icon(
              FIcons.house,
              size: _iconSize,
              color: theme.colors.mutedForeground,
            ),
            selectedIcon: Icon(
              FIcons.house,
              size: _selectedIconSize,
              color: theme.colors.primary,
            ),
            label: 'Главная',
          ),
          NavigationDestination(
            icon: Icon(
              FIcons.user,
              size: _iconSize,
              color: theme.colors.mutedForeground,
            ),
            selectedIcon: Icon(
              FIcons.user,
              size: _selectedIconSize,
              color: theme.colors.primary,
            ),
            label: 'Партнеры',
          ),
        ],
      ),
    );
  }
}
