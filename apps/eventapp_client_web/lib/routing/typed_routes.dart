import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

// Import all screen widgets
import 'package:eventapp_client_web/features/home/<USER>';
import 'package:eventapp_client_web/features/partnetNav/partner_nav.dart';
import 'package:eventapp_client_web/features/home/<USER>/category_products/category_products_page.dart';
import 'package:eventapp_client_web/features/productDetail/product_detail_page.dart';
import 'package:eventapp_client_web/features/partner/partner_detail_page.dart';
import 'package:eventapp_client_web/root_page.dart';

// Import data types
import 'package:eventapp_client_web/features/productDetail/product_detail_args.dart';
import 'package:eventapp_client_web/features/home/<USER>/category_product_list_args.dart';

part 'typed_routes.g.dart';

// =============================================================================
// MAIN SHELL ROUTE - Web-specific 2-tab structure
// =============================================================================

@TypedStatefulShellRoute<WebAppShellRouteData>(
  branches: <TypedStatefulShellBranch<StatefulShellBranchData>>[
    // ВЕТКА 1: Home/Catalog Tab
    TypedStatefulShellBranch<HomeBranchData>(
      routes: <TypedRoute<RouteData>>[
        TypedGoRoute<HomeRouteData>(
          path: '/home',
          routes: <TypedRoute<RouteData>>[
            // Вложенный маршрут для ProductDetail
            TypedGoRoute<ProductDetailFromHomeRouteData>(
              path: 'product/:productID',
              routes: <TypedRoute<RouteData>>[
                TypedGoRoute<PartnerDetailFromProductRouteData>(
                  path: 'partner/:partnerID',
                ),
              ],
            ),
          ],
        ),
        // CategoryProductList как отдельный route под Home branch (сохраняет текущую URL структуру)
        TypedGoRoute<CategoryProductListRouteData>(
            path: '/categoryProductList'),
      ],
    ),

    // ВЕТКА 2: Partners Tab
    TypedStatefulShellBranch<PartnersBranchData>(
      routes: <TypedRoute<RouteData>>[
        TypedGoRoute<PartnersRouteData>(
          path: '/partners',
          routes: <TypedRoute<RouteData>>[
            TypedGoRoute<PartnerDetailRouteData>(
              path: ':partnerID', // Относительный путь (без начального слеша)
            ),
          ],
        ),
      ],
    ),
  ],
)
class WebAppShellRouteData extends StatefulShellRouteData {
  const WebAppShellRouteData();

  @override
  Widget builder(
    BuildContext context,
    GoRouterState state,
    StatefulNavigationShell navigationShell,
  ) {
    return RootPage(navigationShell: navigationShell);
  }
}

// =============================================================================
// BRANCH DATA CLASSES
// =============================================================================

class HomeBranchData extends StatefulShellBranchData {
  const HomeBranchData();
}

class PartnersBranchData extends StatefulShellBranchData {
  const PartnersBranchData();
}

// =============================================================================
// MAIN BRANCH ROUTES
// =============================================================================

class HomeRouteData extends GoRouteData {
  const HomeRouteData();
  @override
  Widget build(BuildContext context, GoRouterState state) => const HomePage();
}

class PartnersRouteData extends GoRouteData {
  const PartnersRouteData();
  @override
  Widget build(BuildContext context, GoRouterState state) =>
      const PartnerNavPage();
}

// =============================================================================
// PRODUCT DETAIL ROUTE
// =============================================================================

class ProductDetailFromHomeRouteData extends GoRouteData {
  const ProductDetailFromHomeRouteData({
    required this.productID,
    this.$extra,
  });

  final String productID;
  final ProductDetailArgs? $extra;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return ProductDetailsPage(
      productID: productID,
      args: $extra,
    );
  }
}

// =============================================================================
// PARTNER DETAIL ROUTE
// =============================================================================

class PartnerDetailRouteData extends GoRouteData {
  const PartnerDetailRouteData({required this.partnerID});

  final String partnerID;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return PartnerDetailPage(partnerID: partnerID);
  }
}

// Partner detail from product page (with productID in URL)
class PartnerDetailFromProductRouteData extends GoRouteData {
  const PartnerDetailFromProductRouteData({
    required this.productID,
    required this.partnerID,
  });

  final String productID;
  final String partnerID;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return PartnerDetailPage(partnerID: partnerID);
  }
}

// =============================================================================
// CATEGORY PRODUCT LIST ROUTE
// =============================================================================

class CategoryProductListRouteData extends GoRouteData {
  const CategoryProductListRouteData({required this.$extra});

  final CategoryProductListArgs $extra;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return CategoryProductListPage();
  }
}
