import 'package:eventapp_client_web/app.dart';
import 'package:eventapp_client_web/firebase_options.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_web_plugins/url_strategy.dart';
import 'package:core_logging/core_logging.dart';
import 'package:talker_riverpod_logger/talker_riverpod_logger.dart';

// *** WEB ***
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  usePathUrlStrategy();
// Initialize Talker from the core_logging package
  final talker = TalkerService.talker;
  talker.info('Talker initialized for eventapp_client_web');

  runApp(
    ProviderScope(
      observers: [
        TalkerRiverpodObserver(
          talker: talker,
          // Optional: Add settings for the observer if needed
          // settings: const TalkerRiverpodLoggerSettings(printProviderAdded: true),
        ),
      ],
      child: const <PERSON><PERSON>pp(),
    ),
  );
}
