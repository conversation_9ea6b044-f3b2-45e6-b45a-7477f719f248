import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:core_data/core_data.dart';
import 'package:eventapp_client_web/routing/typed_routes.dart';
import 'package:core_ui/core_ui.dart';

class PartnerNavPage extends ConsumerWidget {
  const PartnerNavPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final partnerListValue = ref.watch(partnersStreamProvider);
    return Scaffold(
      appBar: AppBar(
        title: const Text('Партнеры'),
      ),
      body: AsyncValueWidget(
          value: partnerListValue,
          data: (partnerList) {
            return ListView.builder(
              padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              itemCount: partnerList.length,
              itemBuilder: (context, index) => PartnerCard(
                partner: partnerList[index],
                onTap: () {
                  PartnerDetailRouteData(
                    partnerID: partnerList[index].id,
                  ).go(context);
                },
              ),
            );
          }),
    );
  }
}
