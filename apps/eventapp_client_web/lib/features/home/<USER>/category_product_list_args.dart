import 'package:flutter/foundation.dart';

/// Arguments class for navigating to the CategoryProductListScreen.
@immutable
class CategoryProductListArgs {
  final String categoryId;
  final String categoryName;
  final bool isCategory;

  const CategoryProductListArgs({
    required this.categoryId,
    required this.categoryName,
    required this.isCategory,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CategoryProductListArgs &&
          runtimeType == other.runtimeType &&
          categoryId == other.categoryId &&
          categoryName == other.categoryName;

  @override
  int get hashCode => categoryId.hashCode ^ categoryName.hashCode;
}
