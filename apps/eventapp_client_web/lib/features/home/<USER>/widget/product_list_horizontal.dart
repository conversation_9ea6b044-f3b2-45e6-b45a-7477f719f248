import 'package:core_domain/core_domain.dart';
import 'package:eventapp_client_web/features/productDetail/product_detail_args.dart';
import 'package:eventapp_client_web/routing/typed_routes.dart';
import 'package:flutter/material.dart';
import 'package:core_ui/core_ui.dart';

class ProductListHorizontal extends StatelessWidget {
  const ProductListHorizontal({super.key, required this.products});
  final List<Product> products;

  @override
  Widget build(BuildContext context) {
    if (products.isEmpty) {
      return SizedBox.shrink();
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Row(
          children: products
              .map((product) {
                // Map products to ProductCard widgets
                return ProductCard(
                  product: product,
                  isCompactMode: true,
                  onCardTap: () => ProductDetailFromHomeRouteData(
                    productID: product.id,
                    $extra: ProductDetailArgs(product: product),
                  ).go(context),
                );
              })
              .expand(
                  (widget) => [widget, gapW16]) // Add gapW16 after each widget
              .toList()
            ..removeLast(), // Remove the trailing gapW16
        ),
      ),
    );
  }
}
