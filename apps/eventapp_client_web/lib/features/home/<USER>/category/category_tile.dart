import 'package:core_domain/core_domain.dart';
import 'package:core_theme/core_theme.dart';
import 'package:flutter/material.dart';

class CategoryTile extends StatelessWidget {
  const CategoryTile({
    super.key,
    required this.category,
    this.onTap,
  });

  final CategoryModel category;
  final void Function()? onTap;

  IconData _getCategoryIcon(String categoryName) {
    switch (categoryName.toLowerCase()) {
      case 'семья':
        return FIcons.personStanding;
      case 'зима':
        return Icons.ac_unit_outlined;
      case 'эко':
        return Icons.park_outlined;
      case 'горный':
        return FIcons.mountain;
      default:
        return Icons.explore_outlined;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: InkWell(
        onTap: onTap,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            FCard(
              child: Icon(
                _getCategoryIcon(category.name),
                color: theme.colors.primary,
                size: 28,
              ),
            ),
            const SizedBox(height: 8),
            ConstrainedBox(
              constraints: BoxConstraints(maxWidth: 80),
              child: Text(
                category.name,
                style:
                    theme.typography.sm.copyWith(fontWeight: FontWeight.w400),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
