import 'package:core_data/core_data.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:eventapp_client_web/features/home/<USER>/category/category_tile.dart';
import 'package:eventapp_client_web/features/home/<USER>/category_product_list_args.dart';
import 'package:eventapp_client_web/routing/typed_routes.dart';
import 'package:core_ui/core_ui.dart';

class CategoryList extends ConsumerWidget {
  const CategoryList({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final categoryListValue = ref.watch(categoryListProvider);
    return AsyncValueWidget(
      value: categoryListValue,
      data: (categoryList) {
        final categories = categoryList;

        if (categories.isEmpty) {
          return SizedBox.shrink();
        }

        // Используем SingleChildScrollView и Row вместо ListView.builder
        return SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12.0),
            child: Row(
              children: categories
                  .map((category) {
                    return CategoryTile(
                      category: category,
                      onTap: () {
                        final args = CategoryProductListArgs(
                          categoryName: category.name,
                          categoryId: category.id,
                          isCategory: true,
                        );
                        CategoryProductListRouteData(
                          $extra: args,
                        ).push(context);
                      },
                    );
                  })
                  .expand((widget) => [
                        widget,
                        gapW8,
                      ]) // Добавляем отступ после каждого элемента
                  .toList()
                ..removeLast(), // Убираем последний отступ
            ),
          ),
        );
      },
    );
  }
}
