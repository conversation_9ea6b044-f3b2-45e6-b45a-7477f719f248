import 'package:core_data/core_data.dart';
import 'package:core_ui/core_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'controller/home_controller.dart';
import 'view/category/category_list.dart';
import 'view/widget/category_section.dart';
import 'view/widget/product_list_horizontal.dart';
import 'view/widget/product_list_vertical.dart';

class HomePage extends ConsumerWidget {
  const HomePage({super.key});

  // _onCategoryPressed method is removed

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get the controller instance
    final controller = ref.read(homeControllerProvider.notifier);
    final newProductsValue = ref.watch(newProductsStreamProvider);
    final popularProductsValue =
        ref.watch(productsByTagStreamProvider('popular'));
    final allproductsValue = ref.watch(productListStreamProvider);
    return Scaffold(
      body: <PERSON><PERSON>rea(
        child: <PERSON>View(
          children: [
            gapH24,

            // *** CategoryList
            CategoryList(),
            gapH32,

            // *** Pr,oducts "New"
            AsyncValueWidget(
                value: newProductsValue,
                data: (products) {
                  if (products.isEmpty) return SizedBox.shrink();
                  return Padding(
                    padding: EdgeInsets.only(bottom: Sizes.p32),
                    child: CategorySection(
                        title: 'Новые',
                        categoryId: 'new',
                        productList: ProductListHorizontal(
                          products: products,
                        ),
                        onSeeAllPressed: (tag) => controller.selectCategory(
                            context, tag, 'Новые',
                            isTag: true)),
                  );
                }),

            // *** Popular products
            AsyncValueWidget(
                value: popularProductsValue,
                data: (products) {
                  if (products.isEmpty) return SizedBox.shrink();
                  return Padding(
                    padding: EdgeInsets.only(bottom: Sizes.p32),
                    child: CategorySection(
                        title: 'Популярные',
                        categoryId: 'popular',
                        productList: ProductListHorizontal(
                          products: products,
                        ),
                        onSeeAllPressed: (tag) => controller.selectCategory(
                            context, tag, 'Популярные',
                            isTag: true)),
                  );
                }),

            // *** Other products
            AsyncValueWidget(
                value: allproductsValue,
                data: (products) {
                  if (products.isEmpty) return SizedBox.shrink();
                  return Padding(
                    padding: EdgeInsets.only(bottom: Sizes.p32),
                    child: CategorySection(
                        title: 'Другие',
                        categoryId: 'other',
                        productList: ProductListVertical(
                          products: products,
                        ),
                        onSeeAllPressed: (tag) => controller.selectCategory(
                            context, tag, 'Другие',
                            isTag: true)),
                  );
                }),
          ],
        ),
      ),
    );
  }
}
