import 'package:eventapp_client_web/features/productDetail/product_detail_args.dart';
import 'package:eventapp_client_web/routing/typed_routes.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:core_domain/core_domain.dart';
import 'package:core_data/core_data.dart';
import 'package:core_ui/core_ui.dart';
import 'package:core_theme/core_theme.dart';

class PartnerServiceTypeList extends ConsumerWidget {
  const PartnerServiceTypeList({
    super.key,
    required this.partner,
  });

  final Partner partner;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final productListValue =
        ref.watch(productListPartnerFutureProvider(partner.id));
    final theme = context.theme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Text(
            'Услуги',
            style: theme.typography.lg,
          ),
        ),
        gapH16,
        AsyncValueWidget(
            value: productListValue,
            data: (productList) {
              return productList.isNotEmpty
                  ? SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Padding(
                        padding: const EdgeInsets.only(left: 20, bottom: 20),
                        child: Row(
                          children: [
                            ...List.generate(productList.length, (index) {
                              return Padding(
                                padding: const EdgeInsets.only(right: 20),
                                child: ProductCard(
                                  product: productList[index],
                                  fromPartnerDetail: true,
                                  onCardTap: () =>
                                      ProductDetailFromHomeRouteData(
                                    productID: productList[index].id,
                                    $extra: ProductDetailArgs(
                                        hidePartnerField: true,
                                        product: productList[index]),
                                  ).go(context),
                                ),
                              );
                            })
                          ],
                        ),
                      ),
                    )
                  : Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Center(
                        child: FCard(
                          child: Text("У  партнера пока нет доступных услуг.",
                              style: theme.typography.xl.copyWith(
                                color: theme.colors.mutedForeground,
                              ),
                              textAlign: TextAlign.center),
                        ),
                      ),
                    );
            }),
      ],
    );
  }
}
