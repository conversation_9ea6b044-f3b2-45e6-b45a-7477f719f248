import 'package:eventapp_client_web/features/partner/widgets/partner_header_section.dart';
import 'package:eventapp_client_web/features/partner/widgets/partner_contact_section_enhanced.dart';
import 'package:eventapp_client_web/features/partner/widgets/products_section_enhanced.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:core_data/core_data.dart';
import 'package:core_ui/core_ui.dart';
import 'package:core_theme/core_theme.dart';

class PartnerDetailPage extends ConsumerWidget {
  const PartnerDetailPage({super.key, required this.partnerID});
  final String partnerID;
  // Константы для отступов
  static const labelH = gapH32;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final partnerValue = ref.watch(fetchPartnerProvider(partnerID));
    final theme = context.theme;
    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colors.background,
        elevation: 0,
        actions: [
          // Web version: Visual consistency with mobile, with info dialog
          IconButton(
            icon: Icon(
              Icons.favorite_border,
              color: theme.colors.mutedForeground,
            ),
            onPressed: () => _showMobileAppDialog(context),
          ),
        ],
      ),
      body: AsyncValueWidget(
        value: partnerValue,
        data: (partner) {
          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Enhanced header section with partner info
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: PartnerHeaderSection(partner: partner),
                ),

                // Elegant divider
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Divider(
                    color: theme.colors.border,
                    thickness: 1,
                  ),
                ),
                gapH32,

                // Enhanced contact information section
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: PartnerContactSectionEnhanced(
                    partner: partner.copyWith(
                      about:
                          'Наша компания специализируется на организации и проведении мероприятий любого масштаба и формата. Мы предоставляем полный спектр услуг от концепции до реализации: разработка сценария, техническое обеспечение, кейтеринг, декорирование, развлекательная программа. За более чем 10 лет работы мы организовали свыше 500 успешных мероприятий, включая корпоративные праздники, свадьбы, дни рождения, конференции и выставки. Наша команда состоит из опытных профессионалов, которые внимательно относятся к каждой детали и всегда стремятся превзойти ожидания клиентов. Мы гордимся индивидуальным подходом к каждому проекту и гарантируем высокое качество услуг.',
                    ),
                  ),
                ),

                // Another divider
                Padding(
                  padding: const EdgeInsets.fromLTRB(20, 32, 20, 32),
                  child: Divider(
                    color: theme.colors.border,
                    thickness: 1,
                  ),
                ),

                // Enhanced products section
                ProductsSectionEnhanced(
                  serviceTypeListID: partner.productRefList,
                  partner: partner,
                  fromPartnerDetail: true,
                ),

                // Bottom padding for better scroll experience
                gapH64,
              ],
            ),
          );
        },
      ),
    );
  }

  void _showMobileAppDialog(BuildContext context) {
    final theme = context.theme;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        icon: Icon(
          Icons.smartphone,
          size: 48,
          color: theme.colors.primary,
        ),
        title: Text(
          'Избранное в мобильном приложении',
          style: theme.typography.lg.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          'Функция избранного доступна только в мобильном приложении. Скачайте приложение, чтобы сохранять понравившихся партнеров и получать персональные рекомендации.',
          style: theme.typography.base,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Понятно',
              style: theme.typography.sm.copyWith(
                color: theme.colors.mutedForeground,
              ),
            ),
          ),
          // TODO: Implement app download functionality
          // ElevatedButton(
          //   onPressed: () {
          //     Navigator.of(context).pop();
          //     // TODO: Add link to app store/play store
          //   },
          //   child: Text('Скачать приложение'),
          // ),
        ],
      ),
    );
  }
}
