import 'package:flutter/material.dart';
import 'package:core_domain/core_domain.dart';

class PartnerImage extends StatelessWidget {
  const PartnerImage({super.key, required this.partner});
  final Partner partner;

  @override
  Widget build(BuildContext context) {
    return Align(
        alignment: Alignment.center,
        child: partner.imageURL != null
            ? ClipOval(
                child: Image.network(
                  partner.imageURL!,
                  height: 150,
                  width: 150,
                  fit: BoxFit.cover,
                ),
              )
            : CircleAvatar(
                radius: 75,
                child: Icon(
                  Icons.person,
                  color: Colors.grey.shade700,
                  size: 80,
                ),
              ));
  }
}
