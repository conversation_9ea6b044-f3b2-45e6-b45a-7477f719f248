import 'package:core_theme/core_theme.dart';
import 'package:core_ui/constants/app_sizes.dart';
import 'package:flutter/material.dart';
import 'package:core_domain/core_domain.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

/// Enhanced contact section with better visual hierarchy and readability
class PartnerContactSectionEnhanced extends StatelessWidget {
  final Partner partner;

  const PartnerContactSectionEnhanced({
    super.key,
    required this.partner,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Text(
          'Контактная информация',
          style: theme.typography.lg.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        gapH20,

        // Contact cards with better spacing
        _PhoneContactCard(
          phoneNumber: partner.phoneNumber,
        ),

        if (partner.instagramm != null && partner.instagramm!.isNotEmpty) ...[
          gapH16,
          _ContactCard(
            icon: FontAwesomeIcons.instagram,
            title: 'Instagram',
            value: '@${partner.instagramm!}',
            onTap: () =>
                _launchURL('https://instagram.com/${partner.instagramm}'),
          ),
        ],

        if (partner.address != null && partner.address!.isNotEmpty) ...[
          gapH16,
          _ContactCard(
            icon: FIcons.mapPin,
            title: 'Адрес',
            value: partner.address!,
          ),
        ],

        if (partner.about != null && partner.about!.isNotEmpty) ...[
          gapH16,
          _ExpandableContactCard(
            icon: FIcons.info,
            title: 'О партнере',
            value: partner.about!,
          ),
        ],
      ],
    );
  }

  Future<void> _launchURL(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      if (url.startsWith('tel:')) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else if (url.contains('instagram.com')) {
        try {
          final instagramUri =
              Uri.parse('instagram://user?username=${uri.pathSegments.last}');
          if (await canLaunchUrl(instagramUri)) {
            await launchUrl(instagramUri, mode: LaunchMode.externalApplication);
          } else {
            await launchUrl(uri, mode: LaunchMode.externalApplication);
          }
        } catch (e) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        }
      } else {
        await launchUrl(uri, mode: LaunchMode.platformDefault);
      }
    }
  }
}

/// Phone contact card with call and WhatsApp options
class _PhoneContactCard extends StatelessWidget {
  final String phoneNumber;

  const _PhoneContactCard({
    required this.phoneNumber,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return FCard(
      style: theme.cardStyle.copyWith(
        contentStyle: theme.cardStyle.contentStyle.copyWith(
          padding: EdgeInsets.all(16),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: theme.colors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  FIcons.phone,
                  size: 20,
                  color: theme.colors.primary,
                ),
              ),
              gapW12,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Телефон',
                      style: theme.typography.xs.copyWith(
                        color: theme.colors.mutedForeground,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    gapH4,
                    Text(
                      _formatPhoneNumber(phoneNumber),
                      style: theme.typography.base.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          gapH12,
          // Action buttons
          Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: () => _launchURL('tel:$phoneNumber'),
                  borderRadius: BorderRadius.circular(6),
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                    decoration: BoxDecoration(
                      border: Border.all(color: theme.colors.border),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          FIcons.phone,
                          size: 16,
                          color: theme.colors.foreground,
                        ),
                        gapW8,
                        Text(
                          'Позвонить',
                          style: theme.typography.sm.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              gapW12,
              Expanded(
                child: InkWell(
                  onTap: () => _launchURL('https://wa.me/$phoneNumber'),
                  borderRadius: BorderRadius.circular(6),
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                    decoration: BoxDecoration(
                      border: Border.all(color: theme.colors.border),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          FontAwesomeIcons.whatsapp,
                          size: 16,
                          color: Color(0xFF25D366), // WhatsApp green icon
                        ),
                        gapW8,
                        Text(
                          'WhatsApp',
                          style: theme.typography.sm.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatPhoneNumber(String phoneNumber) {
    if (phoneNumber.startsWith('+7') && phoneNumber.length == 12) {
      return '+7 (${phoneNumber.substring(2, 5)}) ${phoneNumber.substring(5, 8)}-${phoneNumber.substring(8, 10)}-${phoneNumber.substring(10, 12)}';
    }
    return phoneNumber;
  }

  Future<void> _launchURL(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      if (url.startsWith('tel:')) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else if (url.contains('wa.me')) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        await launchUrl(uri, mode: LaunchMode.platformDefault);
      }
    }
  }
}

/// Contact card widget with icon, title and value
class _ContactCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String value;
  final VoidCallback? onTap;

  const _ContactCard({
    required this.icon,
    required this.title,
    required this.value,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return FCard(
      style: theme.cardStyle.copyWith(
        contentStyle: theme.cardStyle.contentStyle.copyWith(
          padding: EdgeInsets.all(16),
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: theme.colors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    size: 20,
                    color: theme.colors.primary,
                  ),
                ),
                gapW12,
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: theme.typography.xs.copyWith(
                          color: theme.colors.mutedForeground,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      gapH4,
                      Text(
                        value,
                        style: theme.typography.base.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                if (onTap != null)
                  Align(
                    alignment: Alignment.center,
                    child: Icon(
                      FIcons.chevronRight,
                      size: 20,
                      color: theme.colors.mutedForeground,
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// Expandable contact card for long text content like "About"
class _ExpandableContactCard extends StatefulWidget {
  final IconData icon;
  final String title;
  final String value;

  const _ExpandableContactCard({
    required this.icon,
    required this.title,
    required this.value,
  });

  @override
  State<_ExpandableContactCard> createState() => _ExpandableContactCardState();
}

class _ExpandableContactCardState extends State<_ExpandableContactCard> {
  bool _isExpanded = false;
  static const int _maxLines = 3;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    // Check if text is actually long enough to need expansion
    final textPainter = TextPainter(
      text: TextSpan(
        text: widget.value,
        style: theme.typography.base.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      maxLines: _maxLines,
      textDirection: TextDirection.ltr,
    );
    textPainter.layout(maxWidth: MediaQuery.of(context).size.width - 120);
    final bool needsExpansion = textPainter.didExceedMaxLines;

    return FCard(
      style: theme.cardStyle.copyWith(
        contentStyle: theme.cardStyle.contentStyle.copyWith(
          padding: EdgeInsets.all(16),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: theme.colors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  widget.icon,
                  size: 20,
                  color: theme.colors.primary,
                ),
              ),
              gapW12,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.title,
                      style: theme.typography.xs.copyWith(
                        color: theme.colors.mutedForeground,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    gapH4,
                    InkWell(
                      onTap: needsExpansion
                          ? () {
                              setState(() {
                                _isExpanded = !_isExpanded;
                              });
                            }
                          : null,
                      child: Text(
                        widget.value,
                        style: theme.typography.base.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: _isExpanded ? null : _maxLines,
                        overflow: _isExpanded ? null : TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (needsExpansion) ...[
            gapH12,
            InkWell(
              onTap: () {
                setState(() {
                  _isExpanded = !_isExpanded;
                });
              },
              borderRadius: BorderRadius.circular(4),
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                child: Text(
                  _isExpanded ? 'Показать меньше' : 'Показать больше',
                  style: theme.typography.sm.copyWith(
                    color: theme.colors.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
