import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Карточка для отображения контактной информации партнера в минималистичном стиле Airbnb.
///
/// Особенности:
/// - Чистый, минималистичный дизайн
/// - Отображает иконку и значение
/// - Поддерживает действия при нажатии и долгом нажатии
class PartnerContactCard extends StatelessWidget {
  final IconData icon;
  final String value;
  final String? tooltip; // Добавлен тултип
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final bool showArrow;

  const PartnerContactCard({
    super.key,
    required this.icon,
    required this.value,
    this.tooltip,
    this.onTap,
    this.onLongPress,
    this.showArrow = true,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Tooltip(
      message: tooltip ?? (onTap != null ? 'Нажмите для действия' : ''),
      child: InkWell(
        onTap: onTap,
        onLongPress: onLongPress,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.symmetric(
              vertical: 16, horizontal: 16), // Увеличенные отступы
          child: Row(
            children: [
              Icon(
                icon,
                size: 24, // Немного увеличен размер
                color: Colors.black87,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  value,
                  style: textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
              if (showArrow)
                Icon(
                  Icons.chevron_right,
                  size: 20,
                  color: Colors.black54,
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Форматирует телефонный номер для лучшей читаемости
  static String formatPhoneNumber(String phoneNumber) {
    // Простое форматирование для казахстанских номеров
    if (phoneNumber.startsWith('+7') && phoneNumber.length == 12) {
      return '${phoneNumber.substring(0, 2)} (${phoneNumber.substring(2, 5)}) ${phoneNumber.substring(5, 8)}-${phoneNumber.substring(8, 10)}-${phoneNumber.substring(10, 12)}';
    }
    return phoneNumber;
  }

  /// Создает карточку для телефонного номера с возможностью звонка
  static PartnerContactCard phone({
    required String phoneNumber,
    required BuildContext context,
  }) {
    return PartnerContactCard(
        icon: Icons.phone,
        value: formatPhoneNumber(phoneNumber),
        tooltip: 'Позвонить по номеру',
        // onTap: () => _launchURL('tel:$phoneNumber'), // Закомментировано для веб-версии
        onLongPress: () => _copyToClipboard(context, phoneNumber));
  }

  /// Создает карточку для Instagram с возможностью перехода
  static PartnerContactCard instagram({
    required String? instagramHandle,
    required BuildContext context,
  }) {
    final handle = instagramHandle ?? '';
    // Добавляем символ @ перед именем пользователя для лучшей узнаваемости
    final displayHandle = handle.isNotEmpty ? '@$handle' : '';

    return PartnerContactCard(
        icon: Icons.camera_alt_outlined,
        value: displayHandle,
        tooltip: 'Открыть Instagram',
        // onTap: () => _launchURL('https://instagram.com/$handle'), // Закомментировано для веб-версии
        onLongPress: () => _copyToClipboard(context, handle));
  }

  /// Создает карточку для адреса с возможностью открытия карты
  static PartnerContactCard address({
    required String? address,
    required BuildContext context,
  }) {
    final addressValue = address ?? '';

    return PartnerContactCard(
        icon: Icons.location_on_outlined,
        value: addressValue,
        tooltip: 'Открыть на карте',
        // onTap: () => _launchURL('https://maps.google.com/?q=$addressValue'), // Закомментировано для веб-версии
        onLongPress: () => _copyToClipboard(context, addressValue));
  }

  /// Создает карточку для описания
  static PartnerContactCard about({
    required String? about,
    required BuildContext context,
  }) {
    final aboutValue = about ?? '';

    return PartnerContactCard(
      icon: Icons.info_outline,
      value: aboutValue,
      tooltip: 'Информация о партнере',
      showArrow: false,
    );
  }
}

/// Копирует текст в буфер обмена и показывает уведомление
void _copyToClipboard(BuildContext context, String text) {
  Clipboard.setData(ClipboardData(text: text));
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text('Скопировано: $text'),
      duration: Duration(seconds: 2),
    ),
  );
}
