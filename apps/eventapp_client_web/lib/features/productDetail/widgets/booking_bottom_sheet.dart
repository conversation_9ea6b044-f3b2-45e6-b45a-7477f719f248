import 'package:core_utils/core_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:core_ui/core_ui.dart';
import 'package:core_theme/core_theme.dart';
import '../controller/product_detail_provider.dart';

class DetailBottomSheet extends ConsumerWidget {
  const DetailBottomSheet({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // ref.listen(productDetailControllerProvider, (prev, next) {
    //   if (next.hasError && next.error != null) {
    //     showErrorDialog(context, 'Ошибка', next.error.toString());
    //   }
    // });

    final myService = ref
        .watch(productDetailStateProvider.select((state) => state.myService!));
    final slots =
        ref.watch(productDetailStateProvider.select((state) => state.slots));
    //final controller = ref.watch(productDetailControllerProvider);
    final theme = context.theme;
    bool isLimit = slots >= myService.slots - myService.slotsReserved;
    final totalCost = myService.price * slots; // Use selected slots

    return Material(
      color: theme.colors.background,
      borderRadius: const BorderRadius.vertical(
        top: Radius.circular(20),
      ),
      child: SafeArea(
        top: false,
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Title section
              Text(
                myService.dateStart.format('d MMMM, EEE'),
                style: theme.typography.lg.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              gapH24,

              // Content section
              gapH8,
              Text(
                'Количество мест',
                style:
                    theme.typography.lg.copyWith(fontWeight: FontWeight.w600),
              ),
              gapH16,

              // Counter section
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  DecoratedBox(
                    decoration: BoxDecoration(
                      color: theme.colors.border,
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      onPressed: slots > 0
                          ? () {
                              ref
                                  .read(productDetailStateProvider.notifier)
                                  .decrement();
                            }
                          : null,
                      icon: Icon(
                        FIcons.minus,
                        size: 45,
                      ),
                    ),
                  ),
                  gapW48,
                  Text(
                    slots.toString(),
                    style:
                        theme.typography.lg.copyWith(fontSize: 46, height: 1),
                  ),
                  gapW48,
                  DecoratedBox(
                    decoration: BoxDecoration(
                      color: theme.colors.border,
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      onPressed: isLimit
                          ? null
                          : () {
                              ref
                                  .read(productDetailStateProvider.notifier)
                                  .increment();
                            },
                      icon: Icon(
                        FIcons.plus,
                        size: 45,
                      ),
                    ),
                  ),
                ],
              ),
              gapH16,
              Text(
                'Свободно ${myService.slots - myService.slotsReserved}',
                style: TextStyle(
                  color: isLimit
                      ? theme.colors.destructive
                      : theme.colors.mutedForeground,
                  fontWeight: isLimit ? FontWeight.bold : null,
                ),
              ),
              gapH32,

              // Total cost,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Цена за гостя',
                    style: theme.typography.base,
                  ),
                  Text(
                    ' ${myService.price.toPrice()}',
                    style: theme.typography.base,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 12),
                child: Divider(
                  color: theme.colors.border,
                  thickness: 1,
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Итого:',
                    style: theme.typography.lg
                        .copyWith(fontWeight: FontWeight.w600, height: 1),
                  ),
                  Text(
                    ' ${totalCost.toPrice()}',
                    style: theme.typography.lg
                        .copyWith(fontWeight: FontWeight.w600, height: 1),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
              gapH24,

              // Action button
              SizedBox(
                width: double.infinity,
                child: PrimaryButton(
                  text: 'Подтвердить',
                  onPressed: (slots <= 0)
                      ? null
                      : () {
                          Navigator.of(context).pop(); // Закрываем текущий bottom sheet
                          _showMobileAppInfoDialog(context);
                        },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showMobileAppInfoDialog(BuildContext context) {
    final theme = context.theme;
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Бронирование',
            style: theme.typography.lg.copyWith(fontWeight: FontWeight.w600),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(
                FIcons.smartphone,
                size: 48,
                color: theme.colors.primary,
              ),
              gapH16,
              Text(
                'Бронирование доступно только в мобильном приложении',
                style: theme.typography.base,
                textAlign: TextAlign.center,
              ),
              gapH12,
              Text(
                'Скачайте наше приложение для быстрого и удобного бронирования с уведомлениями о статусе заявки',
                style: theme.typography.sm.copyWith(
                  color: theme.colors.mutedForeground,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Понятно'),
            ),
          ],
        );
      },
    );
  }
}
