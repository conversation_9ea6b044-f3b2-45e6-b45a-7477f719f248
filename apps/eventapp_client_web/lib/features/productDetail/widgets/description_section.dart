import 'package:core_domain/core_domain.dart';
import 'package:core_theme/core_theme.dart';
import 'package:flutter/material.dart';
import 'package:core_ui/core_ui.dart';

class DescriptionSection extends StatelessWidget {
  const DescriptionSection({
    super.key,
    required this.product,
    required this.textTheme,
  });

  final Product product;
  final TextTheme textTheme;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Информация',
            style: theme.typography.lg.copyWith(height: 1),
          ),
          gapH12,
          Text(
            product.description,
          ),
        ],
      ),
    );
  }
}
