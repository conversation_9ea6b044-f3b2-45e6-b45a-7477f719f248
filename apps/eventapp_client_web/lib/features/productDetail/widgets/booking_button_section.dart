import 'package:eventapp_client_web/features/productDetail/widgets/booking_bottom_sheet.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:core_ui/core_ui.dart';

import '../controller/product_detail_provider.dart';

class BookingButtonSection extends ConsumerWidget {
  const BookingButtonSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedService = ref
        .watch(productDetailStateProvider.select((state) => state.myService));
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(
          top: BorderSide(color: Colors.grey.shade300, width: 0.5),
        ),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: PrimaryButton(
        text: 'Забронировать',
        onPressed: selectedService == null
            ? null
            : () async {
                await showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  backgroundColor: Colors.transparent,
                  builder: (context) {
                    return const DetailBottomSheet();
                  },
                );
              },
      ),
    );
  }
}
