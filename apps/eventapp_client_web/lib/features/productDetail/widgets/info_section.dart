import 'package:core_domain/core_domain.dart';
import 'package:core_theme/core_theme.dart';
import 'package:core_ui/core_ui.dart';
import 'package:core_utils/core_utils.dart';
import 'package:eventapp_client_web/routing/typed_routes.dart';
import 'package:flutter/material.dart';
import 'package:forui/forui.dart';

import '../product_detail_args.dart';
import 'partner_section.dart';

class ProductInfoSection extends StatelessWidget {
  const ProductInfoSection({
    super.key,
    required this.args,
    required this.textTheme,
  });

  final ProductDetailArgs args;
  final TextTheme textTheme;

  @override
  Widget build(BuildContext context) {
    final product = args.product;
    final theme = context.theme;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // *** Name
          Text(
            product.name,
            style: theme.typography.xl.copyWith(
              fontWeight: FontWeight.w600,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          gapH8,
          // *** Info List
          _InfoItem(
            icon: FIcons.banknote,
            label: 'Цена',
            value: '${product.price.formatWithSpaces()} тнг',
          ),
          _InfoItem(
            icon: FIcons.hourglass,
            label: 'Длительность',
            value:
                '${product.duration} ${product.durationType.formatDuration(product.duration)}',
          ),
          _InfoItem(
            icon: FIcons.users,
            label: 'Размер группы',
            value: 'до ${product.slots} гостей',
          ),
          const SDivider(),

          // *** PartnerField
          if (!args.hidePartnerField)
            PartnerSection(
              textTheme: textTheme,
              product: product,
              onTap: () => PartnerDetailFromProductRouteData(
                productID: product.id,
                partnerID: product.partnerRef,
              ).push(context),
            ),
        ],
      ),
    );
  }
}

class _InfoItem extends StatelessWidget {
  const _InfoItem({
    required this.icon,
    required this.label,
    required this.value,
  });

  final IconData icon;
  final String label;
  final String value;

  @override
  Widget build(BuildContext context) {
    final textTheme = context.theme.typography;

    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: Row(
        children: [
          Icon(icon),
          gapW8,
          Text(
            '$label: $value',
            style: textTheme.base.copyWith(),
          ),
        ],
      ),
    );
  }
}
