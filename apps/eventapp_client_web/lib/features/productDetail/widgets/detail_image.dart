import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:core_ui/core_ui.dart';

class DetailImage extends ConsumerWidget {
  const DetailImage({
    super.key,
    required this.imageUrl,
    required this.productID,
  });

  final String imageUrl;
  final String productID;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.4,
      width: double.infinity,
      child: Stack(
        children: [
          MyNetworkImage(
            imageUrl: imageUrl,
            height: MediaQuery.of(context).size.height * 0.4,
            width: double.infinity,
            fit: BoxFit.cover,
            cacheKey: 'product_detail_$productID',
            memCacheHeight: 600,
            memCacheWidth: 800,
          ),
          _ImageOverlayButtons(
            isFavorite: false,
            onBackButtonPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }
}

// --- Extracted Private Widget for Overlay Buttons ---

class _ImageOverlayButtons extends StatelessWidget {
  const _ImageOverlayButtons({
    required this.isFavorite,
    this.onBackButtonPressed,
    this.onFavoriteButtonPressed,
  });

  final bool isFavorite;
  final VoidCallback? onBackButtonPressed;
  final VoidCallback? onFavoriteButtonPressed;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Back Button
            // Change background to semi-transparent
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.black.withValues(alpha: 0.3),
              ),
              child: IconButton(
                icon: const Icon(Icons.arrow_back_outlined,
                    color: Colors.white), // Ensure icon is visible
                onPressed: onBackButtonPressed,
              ),
            ),
            // Favorite Button
            // Change background to semi-transparent
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.black.withValues(alpha: 0.3),
              ),
              child: IconButton(
                // Ensure icon is visible
                icon: isFavorite
                    ? Icon(
                        Icons.favorite,
                        color: Colors.red, // Or theme accent color
                      )
                    : const Icon(
                        Icons.favorite_border,
                        color: Colors.white, // Ensure icon is visible
                      ),
                onPressed: onFavoriteButtonPressed,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
