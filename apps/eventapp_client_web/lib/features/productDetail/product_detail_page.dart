import 'package:core_data/core_data.dart';
import 'package:core_domain/core_domain.dart';
import 'package:core_ui/core_ui.dart';
import 'package:eventapp_client_web/core/constants/my_const.dart';
import 'package:eventapp_client_web/features/productDetail/product_detail_args.dart';
import 'package:eventapp_client_web/features/productDetail/widgets/detail_image.dart';
import 'package:eventapp_client_web/features/productDetail/widgets/info_section.dart';
import 'package:eventapp_client_web/features/productDetail/widgets/schedule_section.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'widgets/booking_button_section.dart';
import 'widgets/description_section.dart';

class ProductDetailsPage extends ConsumerWidget {
  const ProductDetailsPage({
    super.key,
    this.args,
    this.productID,
  }) : assert(args != null || productID != null,
            'Either args or productID must be provided');

  final ProductDetailArgs? args;
  final String? productID;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Если есть args, используем продукт из них
    if (args != null) {
      return _buildProductDetails(
        context: context,
        product: args!.product,
        args: args!,
      );
    }

    // Иначе загружаем продукт по ID
    final productAsync = ref.watch(fetchProductByIdProvider(productID!));

    return productAsync.when(
      data: (product) => _buildProductDetails(
        context: context,
        product: product,
        args: ProductDetailArgs(
          product: product,
          hidePartnerField: false,
        ),
      ),
      loading: () => Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      ),
      error: (error, stack) => Scaffold(
        body: Center(
          child: Text('Ошибка загрузки продукта: $error'),
        ),
      ),
    );
  }

  Widget _buildProductDetails({
    required BuildContext context,
    required Product product,
    required ProductDetailArgs args,
  }) {
    final textTheme = Theme.of(context).textTheme;
    final imageURL = product.imageUrl ?? myImageURL;

    return Scaffold(
      // Add SafeArea
      body: SafeArea(
        top: true,
        bottom: false,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // *** Image
                    DetailImage(
                      imageUrl: imageURL,
                      productID: product.id,
                    ),
                    gapH16,

                    // *** Info Card (следуем структуре мобильной версии)
                    ProductInfoSection(
                      args: args,
                      textTheme: textTheme,
                    ),

                    // *** Schedule
                    ScheduleSection(product: product),

                    // *** Description
                    DescriptionSection(
                      product: product,
                      textTheme: textTheme,
                    ),
                    const SizedBox(height: 100),
                  ],
                ),
              ),
            ),
            const BookingButtonSection(),
          ],
        ),
      ), // Close SafeArea
    );
  }
}
