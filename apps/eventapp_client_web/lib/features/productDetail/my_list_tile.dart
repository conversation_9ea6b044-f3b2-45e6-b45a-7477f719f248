import 'package:flutter/material.dart';

class MyListTile extends StatelessWidget {
  const MyListTile({
    super.key,
    //required this.event,
    this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.onTap,
  });

  final Widget? title;

  final Widget? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding: EdgeInsets.all(0),
      minTileHeight: 0,
      //minLeadingWidth: 0,
      //minVerticalPadding: 0,
      style: ListTileStyle.drawer,
      leading: leading,
      // title: Text(
      //   'Цена',
      // ),
      title: title,
      subtitle: subtitle,
      trailing: trailing,
      onTap: onTap,
      //dense: true,
    );
  }
}
