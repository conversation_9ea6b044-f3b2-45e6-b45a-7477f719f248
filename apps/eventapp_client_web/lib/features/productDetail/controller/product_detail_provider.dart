import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:core_domain/core_domain.dart';
part 'product_detail_provider.g.dart';
part 'product_detail_provider.freezed.dart';

@riverpod
class ProductDetailState extends _$ProductDetailState {
  @override
  ProductDetailModel build() {
    return ProductDetailModel();
  }

  void selectEvent(Event event) {
    state = state.copyWith(myService: event, slots: 1);
  }

  void increment() {
    state = state.copyWith(slots: state.slots + 1);
  }

  void decrement() {
    if (state.slots <= 0) return;
    state = state.copyWith(slots: state.slots - 1);
  }
}

@freezed
class ProductDetailModel with _$ProductDetailModel {
  const factory ProductDetailModel({
    Event? myService,
    @Default(1) int slots,
  }) = _ProductDetailModel;
}
