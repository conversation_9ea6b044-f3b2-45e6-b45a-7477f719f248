import 'package:flutter/foundation.dart';
import 'package:core_domain/core_domain.dart';

/// Arguments class for navigating to the ProductDetailsPage.
@immutable
class ProductDetailArgs {
  final Product product;
  final bool hidePartnerField; // Added flag (Task 2.3)

  const ProductDetailArgs({
    required this.product,
    this.hidePartnerField = false, // Default to false
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProductDetailArgs &&
          runtimeType == other.runtimeType &&
          product == other.product &&
          hidePartnerField ==
              other.hidePartnerField; // Include flag in comparison

  @override
  int get hashCode =>
      product.hashCode ^ hidePartnerField.hashCode; // Include flag in hash
}
