import 'package:core_theme/providers/theme_provider.dart';
import 'package:eventapp_client_web/routing/app_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final goRouter = ref.watch(goRouterProvider);
    final themeMode = ref.watch(currentThemeModeProvider);
    final foruiTheme = ref.watch(foruiThemeDataProvider);

    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(
          maxWidth: 600,
        ),
        child: MaterialApp.router(
          routerConfig: goRouter,
          debugShowCheckedModeBanner: false,
          theme: foruiTheme.toApproximateMaterialTheme(),
          themeMode: themeMode,
          builder: (context, child) => FTheme(
            data: foruiTheme,
            child: child!,
          ),
        ),
      ),
    );
  }
}
