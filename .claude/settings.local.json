{"permissions": {"allow": ["Bash(export PATH=\"$PATH\":\"$HOME/.pub-cache/bin\")", "Bash(fvm flutter analyze:*)", "Bash(flutter analyze:*)", "Bash(dart analyze:*)", "Bash(flutter pub global run:*)", "WebFetch(domain:forui.dev)", "Bash(grep:*)", "Bash(flutter pub:*)", "Bash(find:*)", "Bash(rm:*)", "Bash(melos bootstrap:*)", "<PERSON><PERSON>(flutter run:*)", "WebFetch(domain:pub.dev)", "WebFetch(domain:github.com)", "WebFetch(domain:flutter-shadcn-ui.mariuti.com)", "WebFetch(domain:raw.githubusercontent.com)", "WebFetch(domain:ui.shadcn.com)", "Bash(ls:*)", "Bash(dart run build_runner build:*)", "Bash($HOME/.pub-cache/bin/melos clean)", "Bash($HOME/.pub-cache/bin/melos bootstrap)", "Bash(git add:*)", "mcp__ide__getDiagnostics", "Bash(git commit:*)", "Bash(fvm flutter pub:*)", "Bash(rg:*)", "Bash(fvm flutter:*)", "<PERSON><PERSON>(flutter clean:*)", "Bash(flutter build:*)", "Ba<PERSON>(flutter:*)", "Bash(melos run:*)", "Bash(cp:*)", "WebFetch(domain:docs.flutter.dev)", "mcp__sequentialthinking__sequentialthinking", "<PERSON><PERSON>(dart run:*)", "WebFetch(domain:docs.google.com)", "WebFetch(domain:flutter.dev)", "<PERSON><PERSON>(claude mcp:*)", "WebFetch(domain:docs.anthropic.com)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(diff:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(true)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(melos list:*)", "Bash(dart pub global activate:*)", "Bash(melos:*)"], "deny": []}}