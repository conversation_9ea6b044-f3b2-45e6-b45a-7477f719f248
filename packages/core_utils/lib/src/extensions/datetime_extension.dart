import 'package:intl/intl.dart';

extension DateTimeFromatting on DateTime {
  String format([String pattern = 'd MMM, EEE', String? locale]) {
    return DateFormat(pattern, locale).format(this);
  }

  String toShortDate([String? locale]) => DateFormat('dd.MM', locale).format(this);
  String toLongDate([String? locale]) => DateFormat('dd.MM.yyyy', locale).format(this);
  String toDMW([String? locale]) => DateFormat('dd MMMM, EEEE', locale).format(this);
  
  // Получаем сокращенное название дня недели на русском
  String get shortDayOfWeek {
    switch (weekday) {
      case DateTime.monday:
        return 'пн';
      case DateTime.tuesday:
        return 'вт';
      case DateTime.wednesday:
        return 'ср';
      case DateTime.thursday:
        return 'чт';
      case DateTime.friday:
        return 'пт';
      case DateTime.saturday:
        return 'сб';
      case DateTime.sunday:
        return 'вс';
      default:
        return '';
    }
  }
}
