/// String extensions for working with user names and extracting initials.
///
/// Example:
/// ```dart
/// 'Иван Иванов'.getInitials(); // II
/// 'Мария'.getInitials();       // M
/// ''.getInitials();           // ''
/// ```
///
/// The logic duplicates the implementation that used to live in
/// apps/parnter2024 but is now shared from core_utils.
extension StringInitialsExtension on String {
  /// Returns uppercase initials from a name string.
  ///
  /// Rules:
  /// * Multiple words – returns the first letters of the first two non-empty words.
  /// * Single word – returns the first letter.
  /// * Empty string – returns an empty string.
  String getInitials() {
    if (isEmpty) return '';

    final parts = trim().split(' ');
    if (parts.length > 1) {
      final first = parts[0].isNotEmpty ? parts[0][0] : '';
      final second = parts[1].isNotEmpty ? parts[1][0] : '';
      return '$first$second'.toUpperCase();
    }
    return isNotEmpty ? this[0].toUpperCase() : '';
  }
}
