import 'package:talker_flutter/talker_flutter.dart';

/// A singleton service class for managing the Talker instance.
///
/// Provides a global access point to the initialized Talker instance
/// throughout the application(s).
class TalkerService {
  // Private constructor to prevent instantiation.
  TalkerService._();

  // The single instance of TalkerService.
  static final TalkerService _instance = TalkerService._();

  /// The globally accessible Talker instance.
  /// Initialized using `TalkerFlutter.init()` with default settings.
  /// You can customize the initialization here if needed (e.g., add observers, filters).
  static final Talker talker = TalkerFlutter.init(
      // Example customization: Add settings or logger configuration if needed
      // settings: TalkerSettings(...),
      // logger: TalkerLogger(...),
      );

  /// Provides access to the singleton instance of [TalkerService].
  /// This is generally not needed as the [talker] instance is static.
  static TalkerService get instance => _instance;
}
