import 'package:core_data/core_data.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:core_domain/core_domain.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'category_repo_provider.g.dart';

@riverpod
class CategoryListByProduct extends _$CategoryListByProduct {
  @override
  FutureOr<List<CategoryModel>> build(List<String> categoryIDList) async {
    return await ref
        .watch(categoryRepoProvider)
        .categoryListbyProduct(categoryIDList);
  }
}

@Riverpod(keepAlive: true)
Stream<List<CategoryModel>> categoryList(Ref ref) {
  return ref.watch(categoryRepoProvider).categoryListStream();
}

@Riverpod(keepAlive: true)
ICategoryRepository categoryRepo(Ref ref) {
  final firestore = ref.watch(firebaseFirestoreProvider);
  return CategoryRepoFirebase(firestore);
}
