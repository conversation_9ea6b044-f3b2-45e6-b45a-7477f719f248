import 'package:core_domain/core_domain.dart';
import 'package:firebase_auth/firebase_auth.dart';

class FirebaseAuthRepository implements IAuthRepository {
  FirebaseAuthRepository({required FirebaseAuth firebaseAuth})
      : _firebaseAuth = firebaseAuth;
  final FirebaseAuth _firebaseAuth;
  //user Stream
  @override
  Stream<User?> authStateChanges() => _firebaseAuth.authStateChanges();
  //get user
  @override
  User? get currentUser => _firebaseAuth.currentUser;

  @override
  Future<void> signOut() async {
    await _firebaseAuth.signOut();
  }
}
