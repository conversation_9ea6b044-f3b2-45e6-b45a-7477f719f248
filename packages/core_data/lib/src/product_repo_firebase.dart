import 'dart:developer';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:core_domain/core_domain.dart';

class ProductRepoFirebase implements IProductRepository {
  final _productCollection = FirebaseFirestore.instance.collection('products');
  final _storage = FirebaseStorage.instance;

  // *** All products Future
  @override
  Future<List<Product>> fetchProductList() async {
    try {
      final snapshot = await _productCollection
          .where('eventRefList', isNotEqualTo: []).get();
      return snapshot.docs.map((doc) {
        return Product.fromJson(doc.data());
      }).toList();
    } catch (e) {
      print('Error fetching services: $e');
      rethrow;
    }
  }

  // *** All products Stream
  @override
  Stream<List<Product>> watchProductList() {
    final snapshots =
        _productCollection.where('eventRefList', isNotEqualTo: []).snapshots();
    return snapshots.map(
      (event) {
        return event.docs.map((docSnapshot) {
          final doc = docSnapshot.data();
          return Product.fromJson(doc);
        }).toList();
      },
    );
  }

  // *** Products by Tag
  @override
  Stream<List<Product>> watchProductsByTag(String tag) {
    final snapshots = _productCollection
        .where('tags', arrayContains: tag)
        .where('eventRefList', isNotEqualTo: []).snapshots();

    return snapshots.map((snapshot) {
      return snapshot.docs
          .map((doc) {
            try {
              return Product.fromJson(doc.data());
            } catch (e, st) {
              log('Error parsing product ${doc.id} by tag "$tag": $e',
                  stackTrace: st, error: e);
              return null;
            }
          })
          .whereType<Product>()
          .toList();
    });
  }

  @override
  Future<List<Product>> fetchProductByIdList(List<String> idList) async {
    try {
      List<Product> productList = [];
      for (String id in idList) {
        final docSnapshot = await _productCollection.doc(id).get();
        if (docSnapshot.exists) {
          final doc = docSnapshot.data() as Map<String, dynamic>;
          productList.add(Product.fromJson(doc));
        }
      }

      return productList;
    } catch (e, st) {
      log(e.toString(), stackTrace: st);
      rethrow;
    }
  }

  // *** Products by partner
  @override
  Future<List<Product>> productListPartner(String partnerID) async {
    try {
      final snapshot = await _productCollection
          .where('partnerRef', isEqualTo: partnerID)
          .get();
      return snapshot.docs.map((doc) {
        return Product.fromJson(doc.data());
      }).toList();
    } catch (e, st) {
      log(e.toString(), stackTrace: st);
      rethrow;
    }
  }

  @override
  Future<Product> fetchProductById(String productID) async {
    final docSnapshot = await _productCollection.doc(productID).get();
    return Product.fromJson(docSnapshot.data() as Map<String, dynamic>);
  }

  // *** Partner2024-specific methods
  @override
  Stream<List<Product>> productListStream() {
    // This returns all products, partner filtering should be done by caller
    return watchProductList();
  }

  @override
  Future<Product> productFuture(String productID) async {
    try {
      final docSnapshot = await _productCollection.doc(productID).get();
      if (!docSnapshot.exists) {
        throw Exception('Product not found');
      }
      final doc = docSnapshot.data()!;
      return Product.fromJson(doc);
    } catch (e, st) {
      log('Error fetching product: ${e.toString()}', stackTrace: st);
      rethrow;
    }
  }

  @override
  Future<void> addProduct(Product product) async {
    try {
      await _productCollection.doc(product.id).set(product.toJson());
    } catch (e, st) {
      log('Error adding product: ${e.toString()}', stackTrace: st);
      rethrow;
    }
  }

  @override
  Future<void> updateProduct(Product product) async {
    try {
      await Future.delayed(const Duration(seconds: 2));
      await _productCollection.doc(product.id).update(product.toJson());
    } catch (e, st) {
      log('Error updating product: ${e.toString()}', stackTrace: st);
      rethrow;
    }
  }

  @override
  void updateProductBatch(Product product, WriteBatch batch) {
    batch.update(_productCollection.doc(product.id), product.toJson());
  }

  @override
  Future<void> deleteProduct(Product product) async {
    try {
      await _productCollection.doc(product.id).delete();
    } catch (e, st) {
      log('Error deleting product: ${e.toString()}', stackTrace: st);
      rethrow;
    }
  }

  @override
  Future<String> upLoadImage(String filePath, String productID) async {
    try {
      File imageFile = File(filePath);
      Reference imageRef = _storage.ref().child('images/${productID}_image');
      await imageRef.putFile(imageFile);
      String imageURL = await imageRef.getDownloadURL();
      return imageURL;
    } catch (e, st) {
      log('Error uploading image: ${e.toString()}', stackTrace: st);
      rethrow;
    }
  }

  // *** Partner-filtered methods
  @override
  Future<List<Product>> fetchProductListByPartner(String partnerID) async {
    try {
      final snapshot = await _productCollection
          .where('partnerRef', isEqualTo: partnerID)
          .get();
      return snapshot.docs.map((doc) => Product.fromJson(doc.data())).toList();
    } catch (e, st) {
      log('Error fetching products by partner: ${e.toString()}', stackTrace: st);
      rethrow;
    }
  }

  @override
  Stream<List<Product>> productListStreamByPartner(String partnerID) {
    try {
      final snapshots = _productCollection
          .where('partnerRef', isEqualTo: partnerID)
          .snapshots();
      return snapshots.map((snapshot) {
        return snapshot.docs.map((doc) => Product.fromJson(doc.data())).toList();
      });
    } catch (e, st) {
      log('Error streaming products by partner: ${e.toString()}', stackTrace: st);
      rethrow;
    }
  }
} // END REPOSITORY
