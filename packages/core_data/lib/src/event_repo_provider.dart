// Riverpod - Providers
import 'package:core_data/core_data.dart';
import 'package:core_domain/core_domain.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
part 'event_repo_provider.g.dart';

@Riverpod(keepAlive: true)
IEventRepository myServiceRepository(Ref ref) {
  return EventFirebaseRepo();
}

@riverpod
class MyServiceListNotifier extends _$MyServiceListNotifier {
  @override
  Future<List<Event>> build() async {
    final myServiceRepository = ref.watch(myServiceRepositoryProvider);
    return await myServiceRepository.fetchMyServiceList();
  }
}

@riverpod
class FetchEventsByProduct extends _$FetchEventsByProduct {
  @override
  FutureOr<List<Event>> build(String productId) async {
    final myServiceRepository = ref.watch(myServiceRepositoryProvider);
    return myServiceRepository.fetchEventsByProduct(productId);
  }
}

@riverpod
class EventListByIDListFuture extends _$EventListByIDListFuture {
  @override
  Future<List<Event>> build(List<String> eventIDList) async {
    final myServiceRepo = ref.watch(myServiceRepositoryProvider);
    final newEventIDList =
        await myServiceRepo.eventListByIDListFuture(eventIDList);

    return newEventIDList;
  }
}

@riverpod
class FetchEventListByPartner extends _$FetchEventListByPartner {
  @override
  Future<List<Event>> build(String partnerId) async {
    final repo = ref.watch(myServiceRepositoryProvider);
    return await repo.fetchEventListByPartner(partnerId);
  }
}

@riverpod
class EventListStreamByPartner extends _$EventListStreamByPartner {
  @override
  Stream<List<Event>> build(String partnerId) {
    final repo = ref.watch(myServiceRepositoryProvider);
    return repo.eventListStreamByPartner(partnerId);
  }
}

@riverpod
class FetchEvent extends _$FetchEvent {
  @override
  FutureOr<Event> build(String id) async {
    final repo = ref.watch(myServiceRepositoryProvider);
    return await repo.fetchEvent(id);
  }
}
