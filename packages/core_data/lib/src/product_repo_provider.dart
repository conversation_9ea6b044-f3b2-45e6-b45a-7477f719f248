import 'package:core_data/core_data.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:core_domain/core_domain.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'product_repo_provider.g.dart';

@riverpod
class ProductListFuture extends _$ProductListFuture {
  @override
  Future<List<Product>> build() async {
    final repo = ref.watch(productRepoProvider);
    return await repo.fetchProductList();
  }
}

@riverpod
class ProductListStream extends _$ProductListStream {
  @override
  Stream<List<Product>> build() {
    final repo = ref.watch(productRepoProvider);
    return repo.watchProductList();
    //return Stream.value([]);
  }
}

// *** Products by Tag
@riverpod
Stream<List<Product>> productsByTagStream(Ref ref, String tag) {
  final repo = ref.watch(productRepoProvider);
  return repo.watchProductsByTag(tag);
}

@riverpod
Stream<List<Product>> newProductsStream(Ref ref) {
  final repo = ref.watch(productRepoProvider);
  return repo.watchProductsByTag('new');
}

@riverpod
Stream<List<Product>> popularProductsStream(Ref ref) {
  final repo = ref.watch(productRepoProvider);
  return repo.watchProductsByTag('popular');
}

@riverpod
class FetchServiceTypeByIdList extends _$FetchServiceTypeByIdList {
  @override
  Future<List<Product>> build() async {
    final repo = ref.watch(productRepoProvider);
    //final client = await ref.watch(fetchClientProvider.future);
    final client = await ref.watch(watchClientProvider.future);

    return await repo.fetchProductByIdList(client.favoriteProductIDList);
  }
}

@riverpod
class FetchServiceTypeListPartner extends _$FetchServiceTypeListPartner {
  @override
  FutureOr<List<Product>> build(List<String> listID) async {
    final repo = ref.watch(productRepoProvider);
    return await repo.fetchProductByIdList(listID);
  }
}

@riverpod
class ProductListPartnerFuture extends _$ProductListPartnerFuture {
  @override
  FutureOr<List<Product>> build(String productID) async {
    final repo = ref.watch(productRepoProvider);
    return await repo.productListPartner(productID);
  }
}

@riverpod
class ProductListByCategoryFuture extends _$ProductListByCategoryFuture {
  @override
  FutureOr<List<Product>> build(String categoryID) async {
    // *** Получаем все продукты
    final allProducts = await ref.watch(productListStreamProvider.future);

    // ***Фильтруем продукты, где categoryId содержится в categoryIDList
    return allProducts
        .where((product) => product.caterogyIDList.contains(categoryID))
        .toList();
  }
}

@riverpod
class FetchProductById extends _$FetchProductById {
  @override
  FutureOr<Product> build(String productID) async {
    final repo = ref.watch(productRepoProvider);
    return await repo.fetchProductById(productID);
  }
}

@riverpod
IProductRepository productRepo(Ref ref) {
  return ProductRepoFirebase();
}

// *** Partner2024-specific providers
@riverpod
class ProductFuture extends _$ProductFuture {
  @override
  Future<Product> build(String productID) async {
    final repo = ref.watch(productRepoProvider);
    return await repo.productFuture(productID);
  }
}

@riverpod
class FetchProductListByPartner extends _$FetchProductListByPartner {
  @override
  Future<List<Product>> build(String partnerID) async {
    final repo = ref.watch(productRepoProvider);
    return await repo.fetchProductListByPartner(partnerID);
  }
}

@riverpod
class ProductListStreamByPartner extends _$ProductListStreamByPartner {
  @override
  Stream<List<Product>> build(String partnerID) {
    final repo = ref.watch(productRepoProvider);
    return repo.productListStreamByPartner(partnerID);
  }
}
