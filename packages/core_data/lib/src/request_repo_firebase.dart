import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:core_domain/core_domain.dart';

class RequestRepoFirebase implements IRequestRepository {
  RequestRepoFirebase(FirebaseFirestore firestore, this.clientID)
      : _requestsCollection = firestore.collection('requests');
  final CollectionReference _requestsCollection;
  final String clientID;

  // *** Request Stream
  @override
  Stream<Request?> requestStream(String requestID) {
    return _requestsCollection.doc(requestID).snapshots().map((docSnapshot) {
      if (!docSnapshot.exists) {
        return null;
      }
      final doc = docSnapshot.data() as Map<String, dynamic>;
      return Request.fromJson(doc);
    });
  }

  @override
  Future<List<Request>> fetchRequestList(String clientID) async {
    try {
      // final snapshot = await _requestsCollection
      //     .where('clientRef', isEqualTo: clientID)
      //     .get();
      final snapshot = await _requestsCollection.get();
      return snapshot.docs.map((snapshot) {
        final doc = snapshot.data() as Map<String, dynamic>;
        return Request.fromJson(doc);
      }).toList();
    } catch (e, st) {
      print('Error fetching requests: $e $st');
      rethrow;
    }
  }

  // *** RequestList by Status
  @override
  Stream<List<Request>> requestListStatusStream(RequestStatus status) {
    try {
      final snapshots = _requestsCollection
          .where('status', isEqualTo: status.name)
          .where('clientID', isEqualTo: clientID)
          .snapshots();
      return snapshots.map((snapshot) {
        return snapshot.docs.map((doc) {
          final value = doc.data() as Map<String, dynamic>;
          return Request.fromJson(value);
        }).toList();
      });
    } catch (e, st) {
      print('Error fetching requests: $e $st');
      rethrow;
    }
  }

  // *** Rejected and Canceled Request List Stream

  @override
  Stream<List<Request>> rejectedAndCanceledRequestListStream() {
    return _requestsCollection
        .where('clientID', isEqualTo: clientID)
        .where('status',
            whereIn: [RequestStatus.declined.name, RequestStatus.canceled.name])
        .snapshots()
        .map((snapshot) {
          return snapshot.docs.map((doc) {
            return Request.fromJson(doc.data() as Map<String, dynamic>);
          }).toList();
        });
  }

  // *** Add Request
  @override
  Future<void> addRequest(Request request) async {
    // *Конвертируем объект в Map
    final requestData = request.toJson();

    // *Добавляем серверное время для createdAt
    requestData['createdAt'] = FieldValue.serverTimestamp();

    // * Сохраняем в Firestore
    await _requestsCollection.doc(request.id).set(requestData);
    //await _requestsCollection.doc(request.id).set(request.toJson());
  }

  // *** Update Request
  @override
  Future<void> updateRequest(Request request) async {
    try {
      // Validate request
      if (request.id.isEmpty) {
        throw FirebaseException(
          plugin: 'firebase_request_repository',
          message: 'Request ID cannot be empty',
        );
      }

      // Update in Firestore
      await _requestsCollection.doc(request.id).update(request.toJson());
    } on FirebaseException catch (e) {
      print('Error updating request: ${e.message}');
      rethrow; // Rethrow to handle in UI layer
    }
  }

  // *** Delete Request
  @override
  Future<void> deleteRequest(Request request) async {
    try {
      // Validate request
      if (request.id.isEmpty) {
        throw FirebaseException(
          plugin: 'firebase_request_repository',
          message: 'Request ID cannot be empty',
        );
      }

      // Check if document exists before deleting
      final docSnapshot = await _requestsCollection.doc(request.id).get();
      if (!docSnapshot.exists) {
        throw FirebaseException(
          plugin: 'firebase_request_repository',
          message: 'Request not found',
        );
      }

      // Delete from Firestore
      await _requestsCollection.doc(request.id).delete();
    } on FirebaseException catch (e) {
      print('Error deleting request: ${e.message}');
      rethrow; // Rethrow to handle in UI layer
    }
  }

  // *** Partner2024-specific methods
  @override
  Future<List<Request>> fetchRequestListAll() async {
    try {
      var docSnapshot = await _requestsCollection.get();
      return docSnapshot.docs.map((snapshot) {
        var doc = snapshot.data() as Map<String, dynamic>;
        return Request.fromJson(doc);
      }).toList();
    } catch (e, st) {
      print('Error fetching all requests: $e $st');
      rethrow;
    }
  }

  @override
  Stream<List<Request>> requestListStream() {
    return _requestsCollection.snapshots().map((snapshot) {
      return snapshot.docs.map((doc) {
        return Request.fromJson(doc.data() as Map<String, dynamic>);
      }).toList();
    });
  }

  @override
  Future<List<Request>> fetchAcceptedRequestList() async {
    try {
      var docSnapshot = await _requestsCollection
          .where('status', isEqualTo: RequestStatus.accepted.name)
          .get();
      return docSnapshot.docs.map((snapshot) {
        var doc = snapshot.data() as Map<String, dynamic>;
        return Request.fromJson(doc);
      }).toList();
    } catch (e, st) {
      print('Error fetching accepted requests: $e $st');
      rethrow;
    }
  }

  @override
  Future<List<Request>> fetchRejectedRequestList() async {
    try {
      var docSnapshot = await _requestsCollection
          .where('status', isEqualTo: RequestStatus.declined.name)
          .get();
      return docSnapshot.docs.map((snapshot) {
        var doc = snapshot.data() as Map<String, dynamic>;
        return Request.fromJson(doc);
      }).toList();
    } catch (e, st) {
      print('Error fetching rejected requests: $e $st');
      rethrow;
    }
  }

  @override
  Future<List<Request>> fetchNewRequestList() async {
    try {
      var docSnapshot = await _requestsCollection
          .where('status', isEqualTo: RequestStatus.pending.name)
          .get();
      return docSnapshot.docs.map((snapshot) {
        var doc = snapshot.data() as Map<String, dynamic>;
        return Request.fromJson(doc);
      }).toList();
    } catch (e, st) {
      print('Error fetching new requests: $e $st');
      rethrow;
    }
  }

  @override
  Stream<List<Request>> acceptedRequestListStream() {
    return _requestsCollection
        .where('status', isEqualTo: RequestStatus.accepted.name)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        return Request.fromJson(doc.data() as Map<String, dynamic>);
      }).toList();
    });
  }

  @override
  Stream<List<Request>> rejectedRequestListStream() {
    return _requestsCollection
        .where('status', isEqualTo: RequestStatus.declined.name)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        return Request.fromJson(doc.data() as Map<String, dynamic>);
      }).toList();
    });
  }

  @override
  Stream<List<Request>> newRequestListStream() {
    return _requestsCollection
        .where('status', isEqualTo: RequestStatus.pending.name)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        return Request.fromJson(doc.data() as Map<String, dynamic>);
      }).toList();
    });
  }

  @override
  Future<Request> fetchRequest(String id) async {
    try {
      final snapshot = await _requestsCollection.doc(id).get();
      if (snapshot.exists == false) {
        throw Exception('Request not found');
      }
      return Request.fromJson(snapshot.data()! as Map<String, dynamic>);
    } catch (e, st) {
      print('Error fetching request: $e $st');
      rethrow;
    }
  }

  @override
  DocumentReference<Object?> fetchDocRef(String id) {
    return _requestsCollection.doc(id);
  }

  // *** Partner-filtered methods
  @override
  Stream<List<Request>> requestListStreamByPartner(String partnerID) {
    return _requestsCollection
        .where('partnerID', isEqualTo: partnerID)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        return Request.fromJson(doc.data() as Map<String, dynamic>);
      }).toList();
    });
  }

  @override
  Stream<List<Request>> acceptedRequestListStreamByPartner(String partnerID) {
    return _requestsCollection
        .where('partnerID', isEqualTo: partnerID)
        .where('status', isEqualTo: RequestStatus.accepted.name)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        return Request.fromJson(doc.data() as Map<String, dynamic>);
      }).toList();
    });
  }

  @override
  Stream<List<Request>> rejectedRequestListStreamByPartner(String partnerID) {
    return _requestsCollection
        .where('partnerID', isEqualTo: partnerID)
        .where('status', isEqualTo: RequestStatus.declined.name)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        return Request.fromJson(doc.data() as Map<String, dynamic>);
      }).toList();
    });
  }

  @override
  Stream<List<Request>> rejectedAndCanceledRequestListStreamByPartner(String partnerID) {
    return _requestsCollection
        .where('partnerID', isEqualTo: partnerID)
        .where('status',
            whereIn: [RequestStatus.declined.name, RequestStatus.canceled.name])
        .snapshots()
        .map((snapshot) {
          return snapshot.docs.map((doc) {
            return Request.fromJson(doc.data() as Map<String, dynamic>);
          }).toList();
        });
  }

  @override
  Stream<List<Request>> newRequestListStreamByPartner(String partnerID) {
    return _requestsCollection
        .where('partnerID', isEqualTo: partnerID)
        .where('status', isEqualTo: RequestStatus.pending.name)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        return Request.fromJson(doc.data() as Map<String, dynamic>);
      }).toList();
    });
  }
} // RequestFirebaseRepository
