import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:core_domain/core_domain.dart';

class CategoryRepoFirebase implements ICategoryRepository {
  final FirebaseFirestore _firestore;
  CategoryRepoFirebase(this._firestore) {
    _collectionCategories = _firestore.collection('categories');
  }

  late CollectionReference _collectionCategories;
  @override
  Stream<List<CategoryModel>> categoryListStream() {
    return _collectionCategories
        .where('isActive', isEqualTo: true)
        .orderBy('order')
        .snapshots()
        .map((snapshot) {
      if (snapshot.docs.isEmpty) {
        return [];
      }
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return CategoryModel.fromJson(data);
      }).toList();
    });
  }

  @override
  Future<List<CategoryModel>> categoryListbyProduct(
      List<String> categoryIDList) async {
    try {
      if (categoryIDList.isEmpty) {
        return [];
      }
      // Получаем текущий список категорий из стрима
      final allCategories = await categoryListStream().first;

      // Фильтруем локально
      return allCategories
          .where((category) => categoryIDList.contains(category.id))
          .toList();
    } catch (e) {
      rethrow;
    }
  }
}
