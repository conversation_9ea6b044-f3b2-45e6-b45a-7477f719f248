import 'package:core_data/core_data.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
part 'auth_firebase_repo_provider.g.dart';

@Riverpod(keepAlive: true)
FirebaseAuthRepository firebaseAuthRepository(Ref ref) {
  return FirebaseAuthRepository(firebaseAuth: FirebaseAuth.instance);
}

@Riverpod(keepAlive: true)
class CurrentUser extends _$CurrentUser {
  @override
  Stream<User?> build() {
    return ref.watch(firebaseAuthRepositoryProvider).authStateChanges();
  }
}

@Riverpod(keepAlive: true)
class AuthStateChanges extends _$AuthStateChanges {
  @override
  Stream<User?> build() {
    final myStream =
        ref.watch(firebaseAuthRepositoryProvider).authStateChanges();
    return myStream;
  }
}
