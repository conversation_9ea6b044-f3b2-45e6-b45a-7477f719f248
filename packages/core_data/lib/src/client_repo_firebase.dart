import 'dart:developer';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:core_domain/core_domain.dart';

class ClientRepoFirebase implements IClientRepository {
  ClientRepoFirebase(FirebaseFirestore firestore)
      : _collection = firestore.collection('clients');
  final CollectionReference _collection;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  //CollectionReference get collection => _collection;
  Future<DocumentSnapshot> getDocSnapshot(String id) async {
    return await _collection.doc(id).get();
  }

  @override
  Future<Client> fetchClient(String id) async {
    try {
      final docSnapshot = await _collection.doc(id).get();
      if (docSnapshot.exists) {
        final doc = docSnapshot.data() as Map<String, dynamic>;
        return Client.fromJson(doc);
      } else {
        return Future.error('Client not found');
      }
    } catch (e, st) {
      log(e.toString(), stackTrace: st);
      rethrow;
    }
  }

  @override
  Stream<Client> watchClient(String id) {
    return _collection.doc(id).snapshots().map((docSnapshot) {
      if (docSnapshot.exists) {
        final doc = docSnapshot.data() as Map<String, dynamic>;
        return Client.fromJson(doc);
      } else {
        throw StateError('Client not found');
      }
    });
  }

  @override
  Future<void> setClient(Client client) async {
    try {
      await _collection.doc(client.id).set(client.toJson());
    } catch (e) {
      log(e.toString());
      rethrow;
    }
  }

  @override
  Future<void> updateClient(Client client) async {
    try {
      final json = client.toJson();
      print('Updating client with data: $json');
      await _collection.doc(client.id).update(json);
    } catch (e, st) {
      log('Error updating client: ${e.toString()}', stackTrace: st);
      rethrow;
    }
  }

  @override
  Future<String> uploadImage(String id, File file) async {
    try {
      UploadTask uploadTask = _storage.ref('profile_images/$id').putFile(file);
      TaskSnapshot taskSnapshot = await uploadTask;
      return await taskSnapshot.ref.getDownloadURL();
    } catch (e) {
      rethrow;
    }
  }

  @override
  Future<List<Client>> fetchClientList() async {
    try {
      final snapshots = await _collection.get();
      return snapshots.docs.map((snapshot) {
        final doc = snapshot.data() as Map<String, dynamic>;
        return Client.fromJson(doc);
      }).toList();
    } catch (e, st) {
      log('Error fetching client list: ${e.toString()}', stackTrace: st);
      rethrow;
    }
  }

  @override
  Future<List<Client>> fetchClientListById(List<String> clientIds) async {
    try {
      if (clientIds.isEmpty) {
        return [];
      }
      final snapshots =
          await _collection.where('id', whereIn: clientIds).get();
      return snapshots.docs.map((snapshot) {
        final doc = snapshot.data() as Map<String, dynamic>;
        return Client.fromJson(doc);
      }).toList();
    } catch (e, st) {
      log('Error fetching clients by ID list: ${e.toString()}', stackTrace: st);
      rethrow;
    }
  }
} // END REPO
