import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:core_domain/core_domain.dart';

class PartnerFirebaseRepo implements IPartnerRepository {
  PartnerFirebaseRepo(FirebaseFirestore firebaseFirestore)
      : _partnersCollection = firebaseFirestore.collection('partners');
  final CollectionReference _partnersCollection;

  // *** Fetch partner
  @override
  Future<Partner> fetchPartner(String id) async {
    try {
      final snapshot = await _partnersCollection.doc(id).get();
      if (snapshot.exists) {
        final doc = snapshot.data() as Map<String, dynamic>;
        return Partner.fromJson(doc);
      } else {
        return Future.error('Partner not found');
      }
    } catch (e) {
      print(e.toString());
      rethrow;
    }
  }

  @override
  Future<List<Partner>> fetchProductByIdList(List<String> idList) async {
    try {
      List<Partner> productList = [];
      for (String id in idList) {
        final docSnapshot = await _partnersCollection.doc(id).get();
        if (docSnapshot.exists) {
          final doc = docSnapshot.data() as Map<String, dynamic>;
          productList.add(Partner.fromJson(doc));
        }
      }

      return productList;
    } catch (e, st) {
      log(e.toString(), stackTrace: st);
      rethrow;
    }
  }

  @override
  Stream<List<Partner>> watchPartners() {
    try {
      return _partnersCollection.snapshots().map((snapshot) {
        return snapshot.docs.map((doc) {
          final data = doc.data() as Map<String, dynamic>;
          return Partner.fromJson(data);
        }).toList();
      });
    } catch (e, stackTrace) {
      log('Error watching partners: ${e.toString()}', stackTrace: stackTrace);
      rethrow;
    }
  }

  // *** Partner2024-specific methods
  @override
  Future<Partner> partnerFuture(String partnerId) async {
    try {
      final snapshot = await _partnersCollection.doc(partnerId).get();
      if (snapshot.exists == false) {
        throw Exception('Partner not found');
      }
      return Partner.fromJson(snapshot.data() as Map<String, dynamic>);
    } catch (e, st) {
      log('Error fetching partner: ${e.toString()}', stackTrace: st);
      rethrow;
    }
  }

  @override
  Stream<Partner> partnerStream(String partnerId) {
    try {
      final snapshots = _partnersCollection.doc(partnerId).snapshots();
      return snapshots.map((snapshot) {
        if (snapshot.exists == false) {
          throw Exception('Partner not found');
        }
        return Partner.fromJson(snapshot.data() as Map<String, dynamic>);
      });
    } catch (e, st) {
      log('Error streaming partner: ${e.toString()}', stackTrace: st);
      rethrow;
    }
  }

  @override
  Future<void> updatePartner(Partner partner) async {
    try {
      await _partnersCollection.doc(partner.id).update(partner.toJson());
    } catch (e, st) {
      log('Error updating partner: ${e.toString()}', stackTrace: st);
      rethrow;
    }
  }
} // PartnerRepository
