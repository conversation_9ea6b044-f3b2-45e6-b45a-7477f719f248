import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:core_data/src/auth_firebase_repo_provider.dart';
import 'package:core_data/src/client_repo_firebase.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:core_domain/core_domain.dart';

part 'client_repo_provider.g.dart';

@Riverpod(keepAlive: true)
class FirebaseClientRepo extends _$FirebaseClientRepo {
  @override
  IClientRepository build() {
    return ClientRepoFirebase(FirebaseFirestore.instance);
  }
}

@Riverpod(keepAlive: false)
class FetchClient extends _$FetchClient {
  @override
  Future<Client> build() async {
    //throw Exception('Not implemented');
    final currentUser = await ref.watch(currentUserProvider.future);
    if (currentUser == null) {
      return Future.error('User not found');
    }

    final id = currentUser.uid;
    final repo = ref.watch(firebaseClientRepoProvider);
    return repo.fetchClient(id);
  }
}

@riverpod
class WatchClient extends _$WatchClient {
  @override
  Stream<Client> build() {
    return ref.watch(currentUserProvider).when(
        data: (user) {
          if (user == null) {
            throw Exception('User not found');
          }
          final repo = ref.watch(firebaseClientRepoProvider);
          return repo.watchClient(user.uid);
        },
        error: (error, stack) => Stream.error(error, stack),
        loading: () => Stream.empty());
    // final currentUser = ref.watch(currentUserProvider.future);
    // if (currentUser == null) {
    //   throw Exception('User not found');
    // }
    // final id = currentUser.uid;
    // final repo = ref.watch(firebaseClientRepoProvider);
    // return repo.watchClient(id);
  }
}

@riverpod
class FetchClientListById extends _$FetchClientListById {
  @override
  FutureOr<List<Client>> build(List<String> clientIds) async {
    final repo = ref.watch(firebaseClientRepoProvider);
    return repo.fetchClientListById(clientIds);
  }
}

@riverpod
class FetchClientById extends _$FetchClientById {
  @override
  FutureOr<Client> build(String clientId) async {
    final repo = ref.watch(firebaseClientRepoProvider);
    return repo.fetchClient(clientId);
  }
}
