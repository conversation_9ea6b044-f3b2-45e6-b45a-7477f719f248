import 'dart:io';

import 'package:core_domain/core_domain.dart';
import 'package:firebase_storage/firebase_storage.dart';

class StorageRepoFirebase implements IStorageRepository {
  final _storage = FirebaseStorage.instance;
  @override
  Future<String> upLoadClientImage(String filePath, String clientID) async {
    return _uploadImage(
      filePath: filePath,
      storagePath: 'client_images/${clientID}_image',
    );
  }

  @override
  Future<String> upLoadPartnerImage(String filePath, String partnerID) async {
    return _uploadImage(
      filePath: filePath,
      storagePath: 'partner_images/${partnerID}_image',
    );
  }

  @override
  Future<String> upLoadProductImage(String filePath, String productID) async {
    return _uploadImage(
      filePath: filePath,
      storagePath: 'product_images/${productID}_image',
    );
  }

  Future<String> _uploadImage(
      {required String filePath, required String storagePath}) async {
    File imageFile = File(filePath);
    Reference imageRef = _storage.ref().child(storagePath);
    await imageRef.putFile(imageFile);
    return imageRef.getDownloadURL();
  }
}
