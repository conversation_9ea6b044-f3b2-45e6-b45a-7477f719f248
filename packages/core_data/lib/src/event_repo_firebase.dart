import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:core_domain/core_domain.dart';

class EventFirebaseRepo implements IEventRepository {
  final _myServiceCollection = FirebaseFirestore.instance.collection('events');

  // fetch myService list
  @override
  Future<List<Event>> fetchMyServiceList() async {
    try {
      var snapshot = await _myServiceCollection
          .where('dateStart',
              isGreaterThanOrEqualTo: Timestamp.fromDate(DateTime.now()))
          .get();

      return snapshot.docs.map((snapshot) {
        var doc = snapshot.data();

        return Event.fromJson(doc);
      }).toList();
    } catch (e) {
      print('Error fetching services: $e');

      rethrow;
    }
  }

  @override
  Future<List<Event>> fetchEventsByProduct(String productId) async {
    var snapshot = await _myServiceCollection
        .where('productRef', isEqualTo: productId)
        .where('dateStart',
            isGreaterThanOrEqualTo: Timestamp.fromDate(DateTime.now()))
        .get();
    return snapshot.docs.map((snapshot) {
      var doc = snapshot.data();
      return Event.fromJson(doc);
    }).toList();
  }

  // fetch selected myService
  @override
  Future<List<Event>> eventListByIDListFuture(List<String> eventIDList) async {
    try {
      // Проверяем, пуст ли список eventIDList
      if (eventIDList.isEmpty) {
        print('eventIDList is empty, returning empty list');
        return [];
      }

      // Firestore ограничивает whereIn до 30 элементов
      const int batchSize = 30;
      List<Event> allEvents = [];

      // Разбиваем список на части по 30 элементов
      for (int i = 0; i < eventIDList.length; i += batchSize) {
        final batch = eventIDList.skip(i).take(batchSize).toList();

        final snapshotList = await _myServiceCollection
            .where(FieldPath.documentId, whereIn: batch)
            .orderBy('dateStart')
            .get();

        final batchEvents = snapshotList.docs.map((snapshot) {
          final doc = snapshot.data();
          return Event.fromJson(doc);
        }).toList();

        allEvents.addAll(batchEvents);
      }

      final now = DateTime.now();
      // return filtered events
      return allEvents.where((event) => event.dateStart.isAfter(now)).toList()
        ..sort(
            (a, b) => a.dateStart.compareTo(b.dateStart)); // Сортируем по дате
    } catch (e) {
      print('Error fetching services form refList: $e');
      rethrow;
    }
  }

  @override
  Future<List<Event>> fetchEventListByPartner(String partnerId) async {
    try {
      final snapshot = await _myServiceCollection
          .where('partnerRef', isEqualTo: partnerId)
          .orderBy('dateStart', descending: false)
          .get();
      return snapshot.docs.map((doc) => Event.fromJson(doc.data())).toList();
    } catch (e) {
      print('Error fetching events by partner: $e');
      rethrow;
    }
  }

  @override
  Stream<List<Event>> eventListStreamByPartner(String partnerId) {
    final snapshots = _myServiceCollection
        .where('partnerRef', isEqualTo: partnerId)
        .orderBy('dateStart', descending: false)
        .snapshots();
    return snapshots.map((snapshot) =>
        snapshot.docs.map((doc) => Event.fromJson(doc.data())).toList());
  }

  @override
  Future<void> addEvent(Event event) async {
    try {
      await _myServiceCollection.doc(event.id).set(event.toJson());
    } catch (e) {
      print('Error adding event: $e');
      rethrow;
    }
  }

  @override
  void addEventBatch(Event event, WriteBatch batch) {
    batch.set(_myServiceCollection.doc(event.id), event.toJson());
  }

  @override
  Future<void> updateEvent(Event event) async {
    try {
      await _myServiceCollection.doc(event.id).update(event.toJson());
    } catch (e) {
      print('Error updating event: $e');
      rethrow;
    }
  }

  @override
  Future<void> deleteEvent(Event event) async {
    try {
      await _myServiceCollection.doc(event.id).delete();
    } catch (e) {
      print('Error deleting event: $e');
      rethrow;
    }
  }

  @override
  Future<Event> fetchEvent(String id) async {
    try {
      final snapshot = await _myServiceCollection.doc(id).get();
      
      if (!snapshot.exists) {
        throw Exception("Услуга не найдена");
      }
      
      return Event.fromJson(snapshot.data()!);
    } catch (e) {
      print('Error fetching event: $e');
      rethrow;
    }
  }

  @override
  DocumentReference fetchDocRef(String id) {
    return _myServiceCollection.doc(id);
  }
} // END REPO CLASS
