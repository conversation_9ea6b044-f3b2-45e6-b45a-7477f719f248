import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:core_data/core_data.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:core_domain/core_domain.dart';
part 'request_repo_provider.g.dart';

@riverpod
class FetchRequestList extends _$FetchRequestList {
  @override
  Future<List<Request>> build(String clientID) async {
    final repo = ref.watch(requestRepositoryProvider);
    return await repo.fetchRequestList(clientID);
  }
}

@riverpod
class RequestListStatusStream extends _$RequestListStatusStream {
  @override
  Stream<List<Request>> build(RequestStatus status) {
    // final currentUser = ref.watch(firebaseAuthRepositoryProvider).currentUser;
    // if (currentUser == null) {
    //   print('*************User not found');
    //   return Stream.value(
    //       []); // Возвращаем пустой список если пользователь не авторизован
    // }
    final repo = ref.watch(requestRepositoryProvider);
    if (status == RequestStatus.declined || status == RequestStatus.canceled) {
      return repo.rejectedAndCanceledRequestListStream();
    }
    return repo.requestListStatusStream(status);
  }
}

@riverpod
class RequestStream extends _$RequestStream {
  @override
  Stream<Request?> build(String requestID) {
    final repo = ref.watch(requestRepositoryProvider);
    return repo.requestStream(requestID);
  }
}

@riverpod
IRequestRepository requestRepository(Ref ref) {
  final currentUser = ref.watch(firebaseAuthRepositoryProvider).currentUser;
  if (currentUser == null) {
    print('*************User not found');
  }
  return RequestRepoFirebase(FirebaseFirestore.instance, currentUser!.uid);
}

// *** Partner2024-specific providers
@riverpod
class FetchRequest extends _$FetchRequest {
  @override
  Future<Request> build(String id) async {
    final repo = ref.watch(requestRepositoryProvider);
    return await repo.fetchRequest(id);
  }
}

@riverpod
class RequestListStreamByPartner extends _$RequestListStreamByPartner {
  @override
  Stream<List<Request>> build(String partnerID) {
    final repo = ref.watch(requestRepositoryProvider);
    return repo.requestListStreamByPartner(partnerID);
  }
}

@riverpod
class AcceptedRequestListStreamByPartner extends _$AcceptedRequestListStreamByPartner {
  @override
  Stream<List<Request>> build(String partnerID) {
    final repo = ref.watch(requestRepositoryProvider);
    return repo.acceptedRequestListStreamByPartner(partnerID);
  }
}

@riverpod
class RejectedRequestListStreamByPartner extends _$RejectedRequestListStreamByPartner {
  @override
  Stream<List<Request>> build(String partnerID) {
    final repo = ref.watch(requestRepositoryProvider);
    return repo.rejectedRequestListStreamByPartner(partnerID);
  }
}

@riverpod
class RejectedAndCanceledRequestListStreamByPartner extends _$RejectedAndCanceledRequestListStreamByPartner {
  @override
  Stream<List<Request>> build(String partnerID) {
    final repo = ref.watch(requestRepositoryProvider);
    return repo.rejectedAndCanceledRequestListStreamByPartner(partnerID);
  }
}

@riverpod
class NewRequestListStreamByPartner extends _$NewRequestListStreamByPartner {
  @override
  Stream<List<Request>> build(String partnerID) {
    final repo = ref.watch(requestRepositoryProvider);
    return repo.newRequestListStreamByPartner(partnerID);
  }
}
