import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:core_data/core_data.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:core_domain/core_domain.dart';
part 'partner_repo_provider.g.dart';

@riverpod
class PartnerListFavorite extends _$PartnerListFavorite {
  @override
  FutureOr<List<Partner>> build() async {
    final repo = ref.watch(partnerRepositoryProvider);
    final client = await ref.watch(watchClientProvider.future);
    return repo.fetchProductByIdList(client.favoritePartnerIDList);
  }
}

@riverpod
class FetchPartner extends _$FetchPartner {
  @override
  Future<Partner> build(String id) async {
    final repo = ref.watch(partnerRepositoryProvider);
    return repo.fetchPartner(id);
  }
}

@riverpod
Stream<List<Partner>> partnersStream(Ref ref) {
  final repo = ref.watch(partnerRepositoryProvider);
  return repo.watchPartners();
}

@riverpod
IPartnerRepository partnerRepository(Ref ref) {
  return PartnerFirebaseRepo(FirebaseFirestore.instance);
}

// *** Partner2024-specific providers
@riverpod
class PartnerFuture extends _$PartnerFuture {
  @override
  Future<Partner> build(String partnerId) async {
    final repo = ref.watch(partnerRepositoryProvider);
    return repo.partnerFuture(partnerId);
  }
}

@riverpod
class PartnerStreamProvider extends _$PartnerStreamProvider {
  @override
  Stream<Partner> build(String partnerId) {
    final repo = ref.watch(partnerRepositoryProvider);
    return repo.partnerStream(partnerId);
  }
}
