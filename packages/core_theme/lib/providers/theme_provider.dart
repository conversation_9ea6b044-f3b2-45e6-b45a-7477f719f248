import 'package:core_theme/themes/app_themes.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:forui/forui.dart';
part 'theme_provider.g.dart';

@riverpod
class CurrentThemeMode extends _$CurrentThemeMode {
  @override
  ThemeMode build() {
    return ThemeMode.system;
  }

  void setThemeMode(ThemeMode mode) {
    state = mode;
  }
}

@riverpod
FThemeData foruiThemeData(Ref ref) {
  final brightness = _getCurrentBrightness(ref);
  return AppThemes.getForuiThemeData(brightness);
}

Brightness _getCurrentBrightness(Ref ref) {
  final themeMode = ref.watch(currentThemeModeProvider);

  return themeMode == ThemeMode.dark
      ? Brightness.dark
      : (themeMode == ThemeMode.light
          ? Brightness.light
          : WidgetsBinding.instance.platformDispatcher.platformBrightness);
}
