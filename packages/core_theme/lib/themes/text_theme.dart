import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class TTextTheme {
  TTextTheme._();

  static final TextTheme lightTextTheme = TextTheme(
    displayLarge: GoogleFonts.montserrat()
        .copyWith(fontWeight: FontWeight.w500, fontSize: 57),
    displayMedium: GoogleFonts.montserrat()
        .copyWith(fontWeight: FontWeight.w500, fontSize: 45),
    displaySmall: GoogleFonts.montserrat()
        .copyWith(fontWeight: FontWeight.w500, fontSize: 36),
    titleLarge: GoogleFonts.montserrat()
        .copyWith(fontWeight: FontWeight.w500, fontSize: 22),
    titleMedium: GoogleFonts.montserrat()
        .copyWith(fontWeight: FontWeight.w600, fontSize: 16),
    titleSmall: GoogleFonts.montserrat()
        .copyWith(fontWeight: FontWeight.w600, fontSize: 14),
    bodyLarge: GoogleFonts.montserrat()
        .copyWith(fontWeight: FontWeight.w500, fontSize: 16),
    bodyMedium: GoogleFonts.montserrat()
        .copyWith(fontWeight: FontWeight.w500, fontSize: 14),
    bodySmall: GoogleFonts.montserrat()
        .copyWith(fontWeight: FontWeight.w500, fontSize: 12),
    labelLarge: GoogleFonts.montserrat()
        .copyWith(fontWeight: FontWeight.w600, fontSize: 14),
    labelMedium: GoogleFonts.montserrat()
        .copyWith(fontWeight: FontWeight.w600, fontSize: 12),
    labelSmall: GoogleFonts.montserrat()
        .copyWith(fontWeight: FontWeight.w600, fontSize: 11),
  );
}
