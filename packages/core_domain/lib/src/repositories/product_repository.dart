import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/product_model.dart';

abstract class IProductRepository {
  // *** All products Future
  Future<List<Product>> fetchProductList();

  // *** All products Stream
  Stream<List<Product>> watchProductList();

  Stream<List<Product>> watchProductsByTag(String tag);

  Future<List<Product>> fetchProductByIdList(List<String> idList);

  // *** Products by partner
  Future<List<Product>> productListPartner(String partnerID);
  Future<Product> fetchProductById(String productID);

  // *** Partner2024-specific methods
  Stream<List<Product>> productListStream();
  Future<Product> productFuture(String productID);
  Future<void> addProduct(Product product);
  Future<void> updateProduct(Product product);
  void updateProductBatch(Product product, WriteBatch batch);
  Future<void> deleteProduct(Product product);
  Future<String> upLoadImage(String filePath, String productID);
  
  // *** Partner-filtered methods
  Future<List<Product>> fetchProductListByPartner(String partnerID);
  Stream<List<Product>> productListStreamByPartner(String partnerID);
}
