import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/client_model.dart';

abstract class IClientRepository {
  // *** Get document snapshot
  Future<DocumentSnapshot> getDocSnapshot(String id);

  // *** Fetch client
  Future<Client> fetchClient(String id);

  // *** Watch client stream
  Stream<Client> watchClient(String id);

  // *** Set client
  Future<void> setClient(Client client);

  // *** Update client
  Future<void> updateClient(Client client);

  // *** Upload image
  Future<String> uploadImage(String id, File file);

  // *** Fetch all clients
  Future<List<Client>> fetchClientList();

  // *** Fetch clients by ID list
  Future<List<Client>> fetchClientListById(List<String> clientIds);
}
