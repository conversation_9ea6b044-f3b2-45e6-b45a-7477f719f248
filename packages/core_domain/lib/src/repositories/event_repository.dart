import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/event_model.dart';

abstract class IEventRepository {
  // *** Fetch all events
  Future<List<Event>> fetchMyServiceList();

  // *** Fetch events by product
  Future<List<Event>> fetchEventsByProduct(String productId);

  // *** Fetch events by ID list
  Future<List<Event>> eventListByIDListFuture(List<String> eventIDList);

  // *** Partner-specific methods
  // *** Fetch events by partner
  Future<List<Event>> fetchEventListByPartner(String partnerId);

  // *** Stream events by partner
  Stream<List<Event>> eventListStreamByPartner(String partnerId);

  // *** CRUD operations
  Future<void> addEvent(Event event);
  
  void addEventBatch(Event event, WriteBatch batch);
  
  Future<void> updateEvent(Event event);
  
  Future<void> deleteEvent(Event event);
  
  // *** Fetch single event
  Future<Event> fetchEvent(String id);
  
  // *** Get document reference
  DocumentReference fetchDocRef(String id);
}
