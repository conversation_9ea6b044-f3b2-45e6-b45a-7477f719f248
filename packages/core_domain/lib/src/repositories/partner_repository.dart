import '../models/partner.dart';

abstract class IPartnerRepository {
  // *** Fetch partner
  Future<Partner> fetchPartner(String id);

  Future<List<Partner>> fetchProductByIdList(List<String> idList);

  Stream<List<Partner>> watchPartners();

  // *** Partner2024-specific methods
  Future<Partner> partnerFuture(String partnerId);
  Stream<Partner> partnerStream(String partnerId);
  Future<void> updatePartner(Partner partner);
}
