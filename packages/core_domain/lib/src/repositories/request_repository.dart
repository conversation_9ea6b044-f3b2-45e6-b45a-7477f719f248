import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/request_model.dart';
import '../common/enums/enum_request_status.dart';

abstract class IRequestRepository {
  // *** Stream methods
  Stream<Request?> requestStream(String requestID);
  Stream<List<Request>> requestListStatusStream(RequestStatus status);
  Stream<List<Request>> rejectedAndCanceledRequestListStream();

  // *** Future methods
  Future<List<Request>> fetchRequestList(String clientID);
  Future<void> addRequest(Request request);
  Future<void> updateRequest(Request request);
  Future<void> deleteRequest(Request request);

  // *** Partner2024-specific methods
  Future<List<Request>> fetchRequestListAll();
  Stream<List<Request>> requestListStream();
  Future<List<Request>> fetchAcceptedRequestList();
  Future<List<Request>> fetchRejectedRequestList();
  Future<List<Request>> fetchNewRequestList();
  Stream<List<Request>> acceptedRequestListStream();
  Stream<List<Request>> rejectedRequestListStream();
  Stream<List<Request>> newRequestListStream();
  Future<Request> fetchRequest(String id);
  DocumentReference fetchDocRef(String id);
  
  // *** Partner-filtered methods
  Stream<List<Request>> requestListStreamByPartner(String partnerID);
  Stream<List<Request>> acceptedRequestListStreamByPartner(String partnerID);
  Stream<List<Request>> rejectedRequestListStreamByPartner(String partnerID);
  Stream<List<Request>> rejectedAndCanceledRequestListStreamByPartner(String partnerID);
  Stream<List<Request>> newRequestListStreamByPartner(String partnerID);
}
