enum DurationType {
  hour,
  day,
}

// Расширение для добавления методов сериализации
extension DurationTypeExtension on DurationType {
  // Метод для сериализации enum в строку
  String toJson() {
    return toString().split('.').last;
  }

  // Статический метод для десериализации строки в enum
  static DurationType fromJson(String json) {
    return DurationType.values.firstWhere(
      (type) => type.toString().split('.').last == json,
      orElse: () => DurationType.day, // Default value if not found
    );
  }

  // Метод для форматирования текста длительности
  String formatDuration(int count, {bool abbreviated = false}) {
    switch (this) {
      case DurationType.hour:
        if (count == 1) return 'час';
        if (count >= 2 && count <= 4) return 'часа';
        return 'часов';
      case DurationType.day:
        if (abbreviated) {
          if (count == 1) return 'дн.';
          return 'дн.';
        } else {
          if (count == 1) return 'день';
          if (count >= 2 && count <= 4) return 'дня';
          return 'дней';
        }
    }
  }
}
