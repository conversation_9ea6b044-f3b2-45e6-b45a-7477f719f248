enum RequestStatus {
  pending,
  accepted,
  declined,
  canceled,
}

extension RequestStatusExtension on RequestStatus {
  String get description {
    switch (this) {
      case RequestStatus.pending:
        return 'Рассматривается';
      case RequestStatus.accepted:
        return 'Принята';
      case RequestStatus.declined:
        return 'Отклонена';
      case RequestStatus.canceled:
        return 'Отменена';
    }
  }
}

enum CancelInitiator {
  client, // отменено клиентом
  partner, // отменено партнером
  system // отменено системой
}

extension CancelInitiatorExtension on CancelInitiator {
  String get description {
    switch (this) {
      case CancelInitiator.client:
        return 'Отменено клиентом';
      case CancelInitiator.partner:
        return 'Отменено организатором';
      case CancelInitiator.system:
        return 'Отменено системой';
    }
  }
}
