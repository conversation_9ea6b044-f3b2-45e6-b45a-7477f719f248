import 'package:freezed_annotation/freezed_annotation.dart';
import '../common/enums/enum_duration_type.dart';

part 'product_model.freezed.dart';
part 'product_model.g.dart';

@freezed
class Product with _$Product {
  const factory Product({
    required String id,
    required String name,
    required String description,
    String? address,
    String? imageUrl,
    required int slots,
    required int price,
    required int duration,
    required DurationType durationType,
    @Default([]) List<String> caterogyIDList,
    @Default([]) List<String> tags,
    @Default([]) List<String> eventRefList,
    required String partnerRef,
    @Default('Партнер') String partnerName,
    String? partnerImageUrl,
  }) = _Product;

  factory Product.fromJson(Map<String, dynamic> json) =>
      _$ProductFromJson(json);
}
