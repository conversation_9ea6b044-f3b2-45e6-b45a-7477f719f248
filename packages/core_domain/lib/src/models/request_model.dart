// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../common/enums/enum_request_status.dart';

part 'request_model.freezed.dart';
part 'request_model.g.dart';

@freezed
class Request with _$Request {
  const factory Request({
    required String id,
    required int slots,
    required RequestStatus status,
    required String eventName,
    required String partnerName,
    String? imageURL,
    @JsonKey(fromJson: _timestampToDateTime, toJson: _dateTimeToTimestamp)
    required DateTime dateStart,
    required String eventID,
    required String clientID,
    required String partnerID,
    String? rejectReason,
    CancelInitiator? cancelInitiator,
    @JsonKey(
        fromJson: _timestampToDateTimeNull, toJson: _dateTimeToTimestampNull)
    DateTime? createdAt,
  }) = _Request;

  factory Request.fromJson(Map<String, dynamic> json) =>
      _$RequestFromJson(json);
}

DateTime _timestampToDateTime(Timestamp timestamp) => timestamp.toDate();
Timestamp _dateTimeToTimestamp(DateTime date) => Timestamp.fromDate(date);

DateTime? _timestampToDateTimeNull(Timestamp? timestamp) => timestamp?.toDate();
Timestamp? _dateTimeToTimestampNull(DateTime? date) =>
    date == null ? null : Timestamp.fromDate(date);
