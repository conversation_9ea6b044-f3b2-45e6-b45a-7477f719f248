import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../common/enums/enum_duration_type.dart';

part 'event_model.freezed.dart';
part 'event_model.g.dart';

@freezed
class Event with _$Event {
  const factory Event({
    required String id,
    required String name,
    required String description,
    @JsonKey(fromJson: timestampToDateTime, toJson: dateTimeToTimestamp)
    required DateTime dateStart,
    required int duration,
    required DurationType durationType,
    required int price,
    required int slots,
    required int slotsFree,
    @Default(0) int slotsReserved,
    String? address,
    String? imageURL,
    @Default([]) List<String> clientRefList,
    required String partnerRef,
    required String productRef,
    @Default([]) List<String> requestIdList,
  }) = _Event;

  factory Event.fromJson(Map<String, dynamic> json) => _$EventFromJson(json);
}

DateTime timestampToDateTime(Timestamp timestamp) => timestamp.toDate();
Timestamp dateTimeToTimestamp(DateTime date) => Timestamp.fromDate(date);

extension EventExtensions on Event {
  int get remainingSlots => slots - requestIdList.length;
}
