// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:freezed_annotation/freezed_annotation.dart';

part 'partner.freezed.dart';
part 'partner.g.dart';

@freezed
class Partner with _$Partner {
  const factory Partner({
    required String id,
    required String name,
    required String phoneNumber,
    @Default([]) List<String> productRefList,
    @Default([]) List<String> eventRefList,
    @Default([]) List<String> fcmTokens,
    String? instagramm,
    String? imageURL,
    String? address,
    String? about,
  }) = _Partner;

  factory Partner.fromJson(Map<String, dynamic> json) =>
      _$PartnerFrom<PERSON>son(json);
}
