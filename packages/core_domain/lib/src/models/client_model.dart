// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:freezed_annotation/freezed_annotation.dart';

part 'client_model.freezed.dart';
part 'client_model.g.dart';

@freezed
class Client with _$Client {
  const factory Client({
    required String id,
    String? name,
    required String phoneNumber,
    String? imageURL,
    String? email,
    @Default([]) List<String> favoriteProductIDList,
    @Default([]) List<String> favoritePartnerIDList,
    @Default([]) List<String> fcmTokens,
  }) = _Client;

  factory Client.fromJson(Map<String, dynamic> json) => _$ClientFromJson(json);
}
