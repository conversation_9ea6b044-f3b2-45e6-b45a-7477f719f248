name: core_ui
description: Shared UI widgets and constants for the event app monorepo.
version: 0.0.1
publish_to: none # Не публикуем в pub.dev

environment:
  sdk: '>=3.0.0 <4.0.0' 
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter

  # Зависимости, нужные ProductCard и его частям:
  core_domain: # Зависимость от Product, DurationType
    path: ../core_domain
  core_utils: # Зависимость от NumberFormatExtension
    path: ../core_utils
  core_logging: # Если будешь добавлять логирование сюда
     path: ../core_logging
  core_theme:
    path: ../core_theme

  # Зависимости, нужные ProductCard и MyNetworkImage:
  flutter_riverpod: ^2.6.1 # Или твоя версия
  go_router: ^15.1.2      # Latest version
  cached_network_image: ^3.3.1 # Или твоя версия (нужна для MyNetworkImage)
  # Removed skeletonizer due to Canvas API compatibility issues
  intl: 0.20.2         # Updated for Flutter SDK compatibility

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0 # Или твоя версия

flutter:
  assets:
    - assets/images/