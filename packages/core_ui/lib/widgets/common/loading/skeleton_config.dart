import 'package:flutter/material.dart';

/// Конфигурация для Shimmer эффектов
class ShimmerConfig {
  /// Цвета для светлой темы
  static const lightColor = Color(0xFFE0E0E0);
  static const lightHighlightColor = Color(0xFFF5F5F5);

  /// Цвета для темной темы
  static const darkColor = Color(0xFF424242);
  static const darkHighlightColor = Color(0xFF616161);

  /// Длительность анимации
  static const duration = Duration(milliseconds: 1500);

  /// Получить цвет базового фона для shimmer
  static Color getBaseColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? darkColor
        : lightColor;
  }

  /// Получить цвет подсветки для shimmer
  static Color getHighlightColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? darkHighlightColor
        : lightHighlightColor;
  }
}
