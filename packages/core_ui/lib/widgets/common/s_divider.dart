import 'package:core_theme/core_theme.dart';
import 'package:flutter/material.dart';

class SDivider extends StatelessWidget {
  const SDivider({
    super.key,
    this.verticalPadding = 20.0,
    this.horizontalPadding = 0.0,
    this.height,
    this.thickness = 1,
    this.color,
    this.indent,
    this.endIndent,
  });

  final double verticalPadding;
  final double horizontalPadding;
  final double? height;
  final double? thickness;
  final Color? color;
  final double? indent;
  final double? endIndent;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: verticalPadding,
        horizontal: horizontalPadding,
      ),
      child: Divider(
        height: height,
        thickness: thickness,
        color: color ?? theme.colors.border,
        indent: indent,
        endIndent: endIndent,
      ),
    );
  }
}
