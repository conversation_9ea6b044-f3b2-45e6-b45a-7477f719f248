import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../loading/shimmer_widget.dart';
import '../loading/skeleton_config.dart';

const String _defaultPlaceholderPath = 'packages/core_ui/assets/images/placeholder.jpg';

/// Виджет для отображения сетевых изображений с кэшированием, обработкой ошибок и состояния загрузки
class MyNetworkImage extends StatelessWidget {
  const MyNetworkImage({
    super.key,
    required this.imageUrl,
    this.height,
    this.width,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.cacheKey,
    this.maxHeightDiskCache = 1280,
    this.maxWidthDiskCache = 1280,
    this.memCacheHeight,
    this.memCacheWidth,
    this.placeholderPath,
    this.showPlaceholderOnError = true,
    this.placeholder,
    this.errorWidget,
  });

  /// URL изображения для загрузки
  final String? imageUrl;

  /// Высота изображения
  final double? height;

  /// Ширина изображения
  final double? width;

  /// Режим отображения изображения
  final BoxFit fit;

  /// Радиус скругления углов
  final BorderRadius? borderRadius;

  /// Ключ для кэширования изображения
  final String? cacheKey;

  /// Максимальная высота изображения в кэше на диске
  final int maxHeightDiskCache;

  /// Максимальная ширина изображения в кэше на диске
  final int maxWidthDiskCache;

  /// Высота изображения в памяти
  final int? memCacheHeight;

  /// Ширина изображения в памяти
  final int? memCacheWidth;

  /// Путь к изображению-заглушке
  final String? placeholderPath;

  /// Показывать ли плейсхолдер при ошибке
  final bool showPlaceholderOnError;

  /// Кастомный виджет для отображения во время загрузки
  final Widget? placeholder;

  /// Кастомный виджет для отображения при ошибке
  final Widget? errorWidget;

  @override
  Widget build(BuildContext context) {
    // Если URL пустой, показываем плейсхолдер
    if (imageUrl == null || imageUrl!.isEmpty) {
      return _buildPlaceholder(context);
    }

    return ClipRRect(
      borderRadius: borderRadius ?? BorderRadius.zero,
      child: CachedNetworkImage(
        imageUrl: imageUrl!,
        height: height,
        width: width,
        fit: fit,
        cacheKey: cacheKey,
        maxHeightDiskCache: maxHeightDiskCache,
        maxWidthDiskCache: maxWidthDiskCache,
        memCacheHeight: memCacheHeight,
        memCacheWidth: memCacheWidth,
        progressIndicatorBuilder: (context, url, progress) {
          return placeholder ?? _buildShimmerPlaceholder(context);
        },
        errorWidget: (context, url, error) {
          return errorWidget ?? _buildErrorWidget(context);
        },
      ),
    );
  }

  Widget _buildShimmerPlaceholder(BuildContext context) {
    return ShimmerWidget(
      baseColor: ShimmerConfig.getBaseColor(context),
      highlightColor: ShimmerConfig.getHighlightColor(context),
      duration: ShimmerConfig.duration,
      child: Container(
        height: height,
        width: width,
        decoration: BoxDecoration(
          borderRadius: borderRadius,
          color: ShimmerConfig.getBaseColor(context),
        ),
      ),
    );
  }

  Widget _buildErrorWidget(BuildContext context) {
    if (showPlaceholderOnError) {
      return _buildPlaceholder(context);
    }
    return const Center(
      child: Icon(
        Icons.error_outline,
        color: Colors.red,
        size: 32,
      ),
    );
  }

  Widget _buildPlaceholder(BuildContext context) {
    final path = placeholderPath ?? _defaultPlaceholderPath;
    return Image.asset(
      path,
      width: width,
      height: height,
      fit: fit,
    );
  }

  /// Статический метод для построения изображения (для совместимости с ImageHelper)
  static Widget buildNetworkImage({
    required String? imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
    int? cacheWidth,
    int? cacheHeight,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    return MyNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      borderRadius: borderRadius,
      memCacheWidth: cacheWidth,
      memCacheHeight: cacheHeight,
      placeholder: placeholder,
      errorWidget: errorWidget,
    );
  }
}
