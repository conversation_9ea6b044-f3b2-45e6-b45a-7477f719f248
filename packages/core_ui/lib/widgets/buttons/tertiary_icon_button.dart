import 'package:flutter/material.dart';

class TertiaryIconButton extends StatelessWidget {
  const TertiaryIconButton({
    super.key,
    required this.label,
    required this.icon,
    required this.onPressed,
    this.isLoading = false,
  });
  final String label;
  final IconData? icon;
  final void Function()? onPressed;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    return isLoading
        ? CircularProgressIndicator()
        : OutlinedButton.icon(
            onPressed: onPressed,
            icon: Icon(
              icon,
            ),
            label: Text(label),
          );
  }
}
