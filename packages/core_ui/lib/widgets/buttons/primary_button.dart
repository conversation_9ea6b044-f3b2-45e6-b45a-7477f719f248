import 'package:core_theme/core_theme.dart';
import 'package:core_ui/constants/app_sizes.dart';
import 'package:flutter/material.dart';

class PrimaryButton extends StatelessWidget {
  const PrimaryButton(
      {super.key,
      required this.text,
      this.isLoading = false,
      this.onDisabled = false,
      this.onPressed});
  final String text;
  final bool isLoading;
  final VoidCallback? onPressed;
  final bool onDisabled;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return SizedBox(
      height: Sizes.p48,
      width: double.infinity,
      // TODO: Forui migration - temporarily using ShadButton
      // child: ShadButton(
      //   onPressed: isLoading ? () {} : onPressed,
      //   child: isLoading
      //       ? CircularProgressIndicator(
      //           color: Colors.white,
      //         )
      //       : Text(
      //           text,
      //           style: theme.typography.base
      //               .copyWith(color: theme.colors.primaryForeground, height: 1),
      //         ),
      // ),

      child: FButton(
        onPress: isLoading ? () {} : onPressed,
        child: isLoading
            ? CircularProgressIndicator(
                color: Colors.white,
              )
            : Text(
                text,
                style: theme.typography.base
                    .copyWith(color: theme.colors.primaryForeground, height: 1),
              ),
      ),
    );
  }
}
