import 'package:core_theme/core_theme.dart';
import 'package:flutter/material.dart';

class SecondaryButton extends StatelessWidget {
  const SecondaryButton({
    super.key,
    required this.text,
    this.isLoading = false,
    this.onPressed,
    this.onDisabled = false,
  });
  final String text;
  final bool isLoading;
  final VoidCallback? onPressed;
  final bool onDisabled;

  @override
  Widget build(BuildContext context) {
    return FButton(
      style: FButtonStyle.secondary,
      onPress: isLoading ? () {} : onPressed,
      child: isLoading
          ? FProgress.circularIcon()
          : Text(
              text,
            ),
    );
  }
}
