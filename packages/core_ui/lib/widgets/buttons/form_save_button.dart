import 'package:flutter/material.dart';
import 'package:forui/forui.dart';

class FormSaveButton extends StatelessWidget {
  /// Конструктор для стандартного отображения внутри формы
  const FormSaveButton({
    super.key,
    required this.onPressed,
    this.isLoading = false,
    this.text = 'Сохранить',
    this.padding = const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
  }) : isFixed = false;

  /// Конструктор для кнопки, закрепленной внизу экрана
  const FormSaveButton.fixed({
    super.key,
    required this.onPressed,
    this.isLoading = false,
    this.text = 'Сохранить',
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
  }) : isFixed = true;

  /// Функция, вызываемая при нажатии на кнопку
  final VoidCallback? onPressed;

  /// Индикатор загрузки, если true - показывает индикатор вместо текста
  final bool isLoading;

  /// Текст на кнопке
  final String text;

  /// Отступы вокруг кнопки
  final EdgeInsetsGeometry padding;

  /// Режим отображения: true - фиксированный внизу экрана, false - обычный
  final bool isFixed;

  @override
  Widget build(BuildContext context) {
    if (isFixed) {
      return Container(
        decoration: BoxDecoration(
          border: Border(
            top: BorderSide(
              width: 1,
              color: context.theme.colors.border,
            ),
          ),
        ),
        width: double.infinity,
        padding: padding,
        child: FButton(
          onPress: onPressed,
          child: isLoading ? FProgress.circularIcon() : Text(text),
        ),
      );
    }

    return Padding(
      padding: padding,
      child: FButton(
        onPress: onPressed,
        child: isLoading ? FProgress.circularIcon() : Text(text),
      ),
    );
  }
}