import 'package:flutter/material.dart';

class MyTextButton extends StatelessWidget {
  const MyTextButton({
    required this.text,
    required this.onPressed,
    super.key,
  });
  final String text;
  final void Function()? onPressed;

  @override
  Widget build(BuildContext context) {
    return TextButton(
        onPressed: onPressed,
        child: Text(
          text,
          style: TextStyle(
              //fontSize: 16,
              decoration: TextDecoration.underline,
              //decorationStyle: TextDecorationStyle.wavy,
              decorationColor: Colors.black,
              decorationThickness: 2,
              color: Colors.black,
              fontWeight: FontWeight.w500),
        ));
  }
}
