import 'package:core_domain/core_domain.dart';
import 'package:core_theme/core_theme.dart';
import 'package:core_ui/widgets/product_card/card_image.dart';
import 'package:core_ui/widgets/product_card/card_info.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../constants/card_styles.dart';

/// Карточка продукта, отображающая основную информацию о продукте
///
/// Параметры:
/// * [product] - модель продукта для отображения
/// * [isfavorite] - флаг, показывающий находится ли продукт в избранном
/// * [isCompactMode] - флаг компактного режима отображения
class ProductCard extends ConsumerWidget {
  const ProductCard({
    super.key,
    required this.product,
    this.showFavoriteButton = false,
    this.isfavorite = false,
    this.isCompactMode = false,
    this.fromPartnerDetail = false,
    this.onCardTap,
    this.onFavoriteTap,
  });
  final Product product;
  final bool showFavoriteButton;
  final bool isfavorite;
  final bool isCompactMode;
  final bool fromPartnerDetail;
  final VoidCallback? onCardTap;
  final VoidCallback? onFavoriteTap;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = context.theme;

    final screenWidth = MediaQuery.of(context).size.width;
    // Ограничиваем максимальную ширину для веба/больших экранов
    final constrainedWidth = screenWidth > 600 ? 600 : screenWidth;
    final cardWidth = constrainedWidth * CardStyles.cardWidthFactor;

    return InkWell(
      onTap: onCardTap,
      // TODO: Forui migration - FCard layout system different from ShadCard
      // FCard.title is meant for text, not complex widgets with double.infinity width
      // Moved CardImage from title to child to fix layout constraints
      child: SizedBox(
        width: cardWidth,
        child: FCard(
          style: theme.cardStyle.copyWith(
            decoration: BoxDecoration(
              border: Border.all(style: BorderStyle.none),
            ),
            contentStyle: theme.cardStyle.contentStyle.copyWith(
              padding: EdgeInsets.zero,
            ),
          ),
          // title: null, // Removed CardImage from title
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // *** Image - moved from title to child
              CardImage(
                product: product,
                isCompactMode: isCompactMode,
                showFavoriteButton: showFavoriteButton,
                isFavorite: isfavorite,
                onFavoriteTap: onFavoriteTap,
              ),

              // *** Content
              Padding(
                padding: CardStyles.contentPadding,
                child: CardInfo(
                  product: product,
                  theme: theme,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
