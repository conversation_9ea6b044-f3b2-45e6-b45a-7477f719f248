import 'package:core_theme/core_theme.dart';
import 'package:core_ui/constants/app_sizes.dart';
import 'package:flutter/material.dart';

class InfoTile extends StatelessWidget {
  const InfoTile({super.key, required this.icon, required this.title});

  final IconData? icon;
  final String title;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    final textStyle = theme.typography.sm
        .copyWith(fontWeight: FontWeight.w400); // Use theme color
    //final iconColor = colorScheme.onSurfaceVariant; // Use theme color
    const iconSize = 14.0; // Keep size for now

    return Row(
      // Replaced ListTile with Row for simplicity and control
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,

      children: [
        if (icon != null) ...[
          Icon(
            icon,
            //color: iconColor,
            size: iconSize,
            color: theme.colors.mutedForeground,
          ),
          gapW4, // Replicates horizontalTitleGap
        ],
        // Use Expanded to prevent overflow if title is long
        //gapW8,
        Text(
          title,
          style: textStyle.copyWith(
            color: theme.colors.mutedForeground,
            height: 1,
          ),
          //overflow: TextOverflow.ellipsis, // Handle potential overflow
          //maxLines: 1,
        ),
      ],
    );
  }
}
