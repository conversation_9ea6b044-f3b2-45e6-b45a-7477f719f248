import 'package:core_domain/core_domain.dart';
import 'package:core_theme/core_theme.dart';
import 'package:core_ui/constants/card_styles.dart';
import 'package:core_ui/widgets/common/images/my_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class CardImage extends ConsumerWidget {
  const CardImage({
    super.key,
    required this.product,
    required this.isCompactMode,
    required this.showFavoriteButton,
    required this.isFavorite,
    this.onFavoriteTap,
  });

  final Product product;
  final bool isCompactMode;
  final bool showFavoriteButton;
  final bool isFavorite;

  final VoidCallback? onFavoriteTap;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    //final screenSize = MediaQuery.of(context).size;
    final imageHeight =
        isCompactMode ? CardStyles.compactImageHeight : CardStyles.imageHeight;
    final theme = context.theme;

    // TODO: Forui migration - <PERSON><PERSON> needs explicit size constraints to fix layout error
    // "RenderBox was not laid out" occurs when <PERSON><PERSON> has no defined size
    // This happened after switching from ShadCard to FCard - FCard doesn't provide
    // the same layout constraints as ShadCard did. Need to investigate FCard
    // layout behavior and potentially adjust how we handle image sizing in cards.
    // Wrapped in SizedBox to provide explicit dimensions as temporary fix.
    return SizedBox(
      height: imageHeight,
      width: double.infinity,
      child: Stack(
        children: [
          MyNetworkImage(
            imageUrl: product.imageUrl ?? CardStyles.defaultProductImage,
            height: imageHeight,
            width: double.infinity,
            borderRadius: theme.style.borderRadius,
          // BorderRadius.only(
          //topLeft: Radius.circular(CardStyles.borderRadius),
          //topRight: Radius.circular(CardStyles.borderRadius),

          // ),
          cacheKey: 'product_card_${product.id}',
          memCacheHeight: (imageHeight * CardStyles.retinaScale).round(),
          maxHeightDiskCache:
              (CardStyles.imageHeight * CardStyles.retinaScale).round(),
        ),
        if (showFavoriteButton)
          Positioned(
            top: 8,
            right: 8,
            child: Container(
              decoration: BoxDecoration(
                color: CardStyles.overlayBackground,
                shape: BoxShape.circle,
              ),
              child: IconButton(
                onPressed: onFavoriteTap,
                icon: Icon(
                  isFavorite ? Icons.favorite : Icons.favorite_border,
                  color: isFavorite ? Colors.red : Colors.white,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
