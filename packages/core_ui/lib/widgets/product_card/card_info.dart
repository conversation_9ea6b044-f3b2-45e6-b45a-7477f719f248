import 'package:core_domain/core_domain.dart';
import 'package:core_theme/core_theme.dart';
import 'package:core_ui/constants/app_sizes.dart';
import 'package:core_ui/widgets/product_card/info_tile.dart';
import 'package:core_utils/core_utils.dart';
import 'package:flutter/material.dart';
import 'package:core_ui/widgets/tenge_price.dart';

/// Информационная часть карточки продукта
///
/// Отображает название, информацию о партнере, длительность, количество мест и цену
class CardInfo extends StatelessWidget {
  const CardInfo({
    super.key,
    required this.product,
    //required this.textTheme,
    required this.theme,
  });

  final Product product;
  //final ShadTextTheme textTheme;
  final FThemeData theme;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // *** name
        gapH8,
        Text(
          product.name,
          style: theme.typography.lg,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),

        gapH8,
        InfoTile(
          title: product.partnerName,
          icon: Icons.support_agent_outlined,
        ),
        // *** duration
        // InfoTile(
        //   title:
        //       '${product.duration} ${product.durationType.formatDuration(product.duration)}',
        //   icon: Icons.access_time_outlined,
        // ),

        // *** slots
        // InfoTile(
        //   title:
        //       product.slots > 0 ? '${product.slots} гостей' : 'Индивидуально',
        //   icon: Icons.people_outlined,
        // ),

        // *** price
        gapH8,
        Row(
          children: [
            TengePrice(
              amount: product.price.formatWithSpaces(),
              amountColor: theme.colors.primary,
              symbolColor: theme.colors.primary,
              amountSize: 16,
              symbolSize: 16,
            ),
            Expanded(
              child: Text(
                ' за гостя, до ${product.slots} гостей, ${product.duration} ${product.durationType.formatDuration(product.duration)}',
                style: theme.typography.sm.copyWith(
                  color: theme.colors.mutedForeground,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        )
      ],
    );
  }
}
