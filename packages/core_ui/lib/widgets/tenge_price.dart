import 'package:core_theme/core_theme.dart';
import 'package:flutter/material.dart';

class TengePrice extends StatelessWidget {
  final String amount;
  final double symbolSize;
  final double amountSize;
  final Color symbolColor;
  final Color amountColor;
  final FontWeight fontWeight;

  const TengePrice({
    Key? key,
    required this.amount,
    this.symbolSize = 14,
    this.amountSize = 16,
    this.symbolColor = Colors.grey,
    this.amountColor = Colors.black,
    this.fontWeight = FontWeight.bold,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final textTheme = context.theme.typography;
    return RichText(
      text: TextSpan(
        style: textTheme.sm,
        children: [
          TextSpan(
            text: '\u20B8 ', // Юникод тенге
            style: TextStyle(
              fontFamily: 'Roboto', // Шрифт, поддерживающий ₸
              fontSize: symbolSize,
              color: symbolColor,
              fontWeight: fontWeight,
            ),
          ),
          TextSpan(
            text: amount,
            style: textTheme.sm.copyWith(
              fontSize: amountSize,
              fontWeight: fontWeight,
              color: amountColor,
              //height: 1.2,
            ),
            // TextStyle(
            //   //fontFamily: 'Geist', // Основной шрифт приложения
            //   fontSize: amountSize,
            //   fontWeight: fontWeight,
            //   color: amountColor,
            // ),
          ),
        ],
      ),
    );
  }
}
