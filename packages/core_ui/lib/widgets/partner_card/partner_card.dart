import 'package:core_domain/core_domain.dart';
import 'package:core_ui/core_ui.dart';
import 'package:core_theme/core_theme.dart';
import 'package:flutter/material.dart';

class PartnerCard extends StatelessWidget {
  const PartnerCard({super.key, required this.partner, required this.onTap});
  final Partner partner;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: ListTile(
        title: Text(
          partner.name,
          style: theme.typography.base.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          partner.phoneNumber,
          style: theme.typography.sm.copyWith(
            color: theme.colors.mutedForeground,
          ),
        ),
        leading: MyNetworkImage(
          imageUrl: partner.imageURL!,
          height: 48,
          width: 48,
          borderRadius: BorderRadius.circular(24),
          memCacheHeight: 48,
        ),
        trailing: Icon(
          FIcons.chevronRight,
          size: 20,
          color: theme.colors.mutedForeground,
        ),
        onTap: onTap,
        contentPadding: EdgeInsets.symmetric(horizontal: 0),
      ),
    );
  }
}
