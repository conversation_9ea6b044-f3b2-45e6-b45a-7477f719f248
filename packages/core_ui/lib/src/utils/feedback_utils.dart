import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Утилиты для отображения обратной связи пользователю (SnackBar, Dialog).

/// Показывает SnackBar с сообщением об успехе.
void showSuccessSnackBar(BuildContext context, String message) {
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text(message),
      duration: const Duration(seconds: 2),
      behavior: SnackBarBehavior.floating,
      backgroundColor: Colors.green, // Добавим цвет для наглядности
    ),
  );
}

/// Показывает SnackBar с сообщением об ошибке.
void showErrorSnackBar(BuildContext context, String message) {
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text(message),
      duration: const Duration(seconds: 3), // Чуть дольше для ошибок
      behavior: SnackBarBehavior.floating,
      backgroundColor: Theme.of(context).colorScheme.error,
    ),
  );
}

/// Показывает диалоговое окно с сообщением об ошибке.
void showErrorDialog(BuildContext context, String title, String message) {
  showDialog(
    context: context,
    builder: (context) {
      return AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () {
              // Используем context.pop() из go_router, если он доступен,
              // иначе Navigator.pop(context)
              if (GoRouter.of(context).canPop()) {
                context.pop();
              } else {
                Navigator.of(context).pop();
              }
            },
            child: const Text('Ok'),
          ),
        ],
      );
    },
  );
}
