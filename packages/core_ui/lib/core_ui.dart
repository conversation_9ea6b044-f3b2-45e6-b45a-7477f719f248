// packages/core_ui/lib/core_ui.dart

// Constants
export 'constants/app_sizes.dart';
export 'constants/card_styles.dart';

// Common Widgets
export 'widgets/common/images/my_network_image.dart';
export 'widgets/common/loading/skeleton_config.dart';
export 'widgets/common/loading/shimmer_widget.dart';
export 'widgets/common/async_value_widget.dart';
export 'widgets/common/s_divider.dart';
export 'widgets/buttons/primary_button.dart';
export 'widgets/buttons/secondary_button.dart';
export 'widgets/buttons/tertiary_button.dart';
export 'widgets/buttons/tertiary_icon_button.dart';
export 'widgets/buttons/my_text_button.dart';
export 'widgets/buttons/form_save_button.dart';

// Product Card Widgets
export 'widgets/product_card/product_card.dart';
export 'widgets/partner_card/partner_card.dart';
// Экспортировать составные части карточки не обязательно,
// если они используются только внутри ProductCard,
// но можно добавить, если они нужны отдельно:
// export 'widgets/product_card/card_image.dart';
// export 'widgets/product_card/card_info.dart';
// export 'widgets/product_card/info_tile.dart';

// Utils
export 'src/utils/feedback_utils.dart';
