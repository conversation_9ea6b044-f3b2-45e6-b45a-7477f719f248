import 'package:flutter/material.dart';

/// Константы для стилизации карточек продуктов
class CardStyles {
  /// Ширина карточки относительно ширины экрана (70%)
  static const double cardWidthFactor = 0.7;

  /// Радиус скругления углов для карточек
  static const double borderRadius = 12.0;

  /// Отступы внутри карточки
  static const EdgeInsets contentPadding = EdgeInsets.all(4);

  /// Высота изображения в обычном режиме
  static const double imageHeight = 200.0;

  /// Высота изображения в компактном режиме
  static const double compactImageHeight = 160.0;

  /// Полупрозрачный фон для оверлеев
  static const Color overlayBackground = Color(0x40000000);

  /// URL изображения по умолчанию
  static const String defaultProductImage =
      'https://your-cdn.com/defaults/product.jpg';

  /// Коэффициент масштабирования для ретина дисплеев
  static const double retinaScale = 2.0;
}
